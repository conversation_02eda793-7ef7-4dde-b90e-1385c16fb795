# AIShowLab 支付网关模块模拟数据

本目录包含了AIShowLab项目支付网关模块的完整模拟数据，为开发测试和功能演示提供丰富的支付网关配置和地区映射数据。

## 📁 文件列表

### 核心数据文件
- **`支付网关模拟数据.sql`** - 支付网关模块模拟数据SQL脚本
- **`数据验证脚本.sql`** - 数据验证和统计查询脚本

### 导入工具
- **`导入数据脚本.sh`** - Linux/macOS 导入脚本
- **`导入数据脚本.bat`** - Windows 导入脚本

### 文档文件
- **`README.md`** - 本文件，项目概览

## 🚀 快速开始

### 前置条件
1. PostgreSQL 数据库已安装并运行
2. 已创建数据库和相关表结构（执行 `doc/sql/design/支付网关.sql`）
3. 已导入基础数据（国家地区数据等）

### 导入数据

#### Linux/macOS
```bash
# 导入支付网关数据
./导入数据脚本.sh -d aishowlab -u postgres

# 验证SQL语法
./导入数据脚本.sh --dry-run
```

#### Windows
```cmd
REM 导入支付网关数据
导入数据脚本.bat -d aishowlab -u postgres

REM 验证SQL语法
导入数据脚本.bat --dry-run
```

#### 手动导入
```bash
# 导入支付网关数据
psql -d aishowlab -f 支付网关模拟数据.sql

# 验证数据
psql -d aishowlab -f 数据验证脚本.sql
```

## 📊 数据概览

### 支付网关配置 (15个)

#### 国内支付方式 (3个)
1. **微信支付** - 腾讯公司推出的移动支付产品
   - 支持地区：中国大陆、香港、澳门、台湾、马来西亚、新加坡、泰国
   - 支付方式：扫码支付、APP支付、H5支付

2. **支付宝** - 蚂蚁集团旗下的第三方支付平台
   - 支持地区：中国及全球30+个国家和地区
   - 支付方式：网页支付、手机支付、扫码支付

3. **银联支付** - 中国银联推出的银行卡支付服务
   - 支持地区：中国及银联卡可用的17个国家和地区
   - 支付方式：借记卡和信用卡在线支付

#### 国际支付方式 (4个)
1. **PayPal** - 全球领先的在线支付平台
   - 支持地区：全球200+个国家和地区
   - 支付方式：信用卡、借记卡、PayPal余额

2. **Stripe** - 面向互联网企业的在线支付处理平台
   - 支持地区：全球46个国家和地区
   - 支付方式：信用卡、借记卡、数字钱包

3. **Apple Pay** - 苹果公司推出的移动支付服务
   - 支持地区：全球70+个国家和地区
   - 支付方式：Touch ID、Face ID、Apple Watch

4. **Google Pay** - 谷歌推出的数字钱包平台
   - 支持地区：全球80+个国家和地区
   - 支付方式：NFC支付、在线支付、P2P转账

#### 地区特色支付方式 (6个)
1. **LINE Pay** - 日本、韩国等地区广泛使用
2. **KakaoPay** - 韩国主流移动支付服务
3. **GrabPay** - 东南亚地区数字钱包服务
4. **SEPA Direct Debit** - 欧洲单一支付区域银行转账
5. **UPI** - 印度统一支付接口
6. **PIX** - 巴西央行推出的即时支付系统

#### 测试和其他 (2个)
1. **测试支付网关** - 用于开发测试的模拟支付网关
2. **旧版支付接口** - 已停用的历史支付接口

### 地区覆盖情况

#### 全球覆盖
- **亚洲**: 中国、日本、韩国、新加坡、马来西亚、泰国、菲律宾、印度尼西亚、越南、印度等
- **欧洲**: 英国、法国、德国、意大利、西班牙、荷兰、比利时、瑞士、瑞典、挪威等
- **北美**: 美国、加拿大、墨西哥
- **南美**: 巴西、阿根廷、智利、哥伦比亚、秘鲁等
- **大洋洲**: 澳大利亚、新西兰
- **非洲**: 南非、埃及、摩洛哥、尼日利亚、肯尼亚等
- **中东**: 阿联酋、沙特阿拉伯、卡塔尔、科威特、巴林等

#### 特殊地区支持
- **SEPA区域**: 欧盟27国 + 冰岛、列支敦士登、挪威、瑞士、摩纳哥、圣马力诺、梵蒂冈、安道尔
- **银联网络**: 中国大陆及全球银联卡受理地区
- **东南亚**: GrabPay覆盖新加坡、马来西亚、泰国、菲律宾、印尼、越南、缅甸、柬埔寨

## 🔧 技术特点

### 1. 完整的支付生态
- **多元化支付方式**: 涵盖移动支付、银行卡支付、数字钱包、银行转账等
- **全球化覆盖**: 支持全球主要国家和地区的本地化支付方式
- **实时配置管理**: 支持支付网关的启用、禁用、维护状态管理

### 2. 灵活的配置系统
- **JSON配置参数**: 支持复杂的支付网关配置参数
- **环境区分**: 支持生产环境和沙箱环境配置
- **回调机制**: 完整的支付回调和Webhook配置

### 3. 智能的地区映射
- **精确的地区支持**: 基于ISO国家代码的精确地区映射
- **动态支持范围**: 支持支付网关支持地区的动态调整
- **合规性考虑**: 考虑各地区的支付法规和限制

## ⚠️ 注意事项

### 安全考虑
- **敏感信息加密**: 所有API密钥、证书等敏感信息都已加密处理
- **配置参数保护**: 生产环境中需要对配置参数进行额外的安全保护
- **访问控制**: 支付网关配置需要严格的访问权限控制

### 数据依赖
- **国家基础数据**: 依赖 `a_base_country` 表的国家地区数据
- **表结构**: 需要完整的支付网关表结构
- **外键约束**: 确保支付网关与国家映射的数据一致性

### 业务规则
- **地区限制**: 不同支付网关有不同的地区支持限制
- **合规要求**: 需要遵守各地区的支付法规和合规要求
- **费率管理**: 不同地区和支付方式可能有不同的费率结构

## 📈 使用场景

1. **开发测试** - 为开发环境提供完整的支付网关配置
2. **功能演示** - 展示多种支付方式的集成能力
3. **地区化部署** - 支持不同地区的本地化支付方式
4. **合规测试** - 验证不同地区的支付合规性
5. **性能测试** - 测试支付网关的性能和稳定性

## 🔍 数据验证

执行验证脚本检查数据完整性：
```bash
psql -d aishowlab -f 数据验证脚本.sql
```

验证内容包括：
- 支付网关状态分布统计
- 地区覆盖情况分析
- 各大洲支付网关覆盖率
- 热门国家支付网关配置
- 数据完整性检查
- 业务逻辑一致性验证
- 支付网关功能特性分析

## 🎯 支付网关类型分析

### 按地区分类
| 类型 | 数量 | 主要特点 |
|------|------|----------|
| 国内支付 | 3个 | 微信支付、支付宝、银联支付 |
| 国际支付 | 4个 | PayPal、Stripe、Apple Pay、Google Pay |
| 地区特色支付 | 6个 | LINE Pay、KakaoPay、GrabPay、UPI、PIX、SEPA |
| 测试支付 | 2个 | 测试网关、旧版接口 |

### 按技术特点分类
- **移动优先**: 微信支付、支付宝、Apple Pay、Google Pay
- **银行集成**: 银联支付、SEPA、UPI、PIX
- **平台生态**: LINE Pay、KakaoPay、GrabPay
- **全球通用**: PayPal、Stripe

## 📞 支持

如有问题或建议，请参考：
- 数据验证脚本: `数据验证脚本.sql`
- 项目设计文档: `doc/sql/design/支付网关.sql`
- 导入工具帮助: `./导入数据脚本.sh --help`

---

**AIShowLab Team**  
*让AI触手可及*
