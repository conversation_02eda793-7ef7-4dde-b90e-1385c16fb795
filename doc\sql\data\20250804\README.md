# AIShowLab AI助手和计费模块模拟数据

本目录包含了AIShowLab项目AI助手和计费与消耗模块的完整模拟数据，为开发测试和功能演示提供丰富的数据支持。

## 📁 文件列表

### 核心数据文件
- **`AI助手模拟数据.20250804.sql`** - AI助手模块模拟数据SQL脚本
- **`计费与消耗模拟数据.20250804.sql`** - 计费与消耗模块模拟数据SQL脚本
- **`数据验证脚本.20250804.sql`** - 数据验证和统计查询脚本

### 导入工具
- **`导入数据脚本.20250804.sh`** - Linux/macOS 导入脚本
- **`导入数据脚本.20250804.bat`** - Windows 导入脚本

### 文档文件
- **`README.md`** - 本文件，项目概览

## 🚀 快速开始

### 前置条件
1. PostgreSQL 数据库已安装并运行
2. 已创建数据库和相关表结构（执行 `doc/sql/design/AI助手.sql` 和 `doc/sql/design/计费与消耗.sql`）
3. 已导入基础数据（货币、数据字典、模型数据等）

### 导入数据

#### Linux/macOS
```bash
# 导入所有模块数据
./导入数据脚本.20250804.sh -d aishowlab -u postgres

# 仅导入AI助手模块数据
./导入数据脚本.20250804.sh --assistant-only -d aishowlab -u postgres

# 仅导入计费模块数据
./导入数据脚本.20250804.sh --billing-only -d aishowlab -u postgres

# 验证SQL语法
./导入数据脚本.20250804.sh --dry-run
```

#### Windows
```cmd
REM 导入所有模块数据
导入数据脚本.20250804.bat -d aishowlab -u postgres

REM 仅导入AI助手模块数据
导入数据脚本.20250804.bat --assistant-only -d aishowlab -u postgres

REM 仅导入计费模块数据
导入数据脚本.20250804.bat --billing-only -d aishowlab -u postgres

REM 验证SQL语法
导入数据脚本.20250804.bat --dry-run
```

#### 手动导入
```bash
# 导入AI助手数据
psql -d aishowlab -f AI助手模拟数据.20250804.sql

# 导入计费数据
psql -d aishowlab -f 计费与消耗模拟数据.20250804.sql

# 验证数据
psql -d aishowlab -f 数据验证脚本.20250804.sql
```

## 📊 数据概览

### AI助手模块

#### 助手分类 (6个一级分类 + 6个二级分类)
- **生活娱乐**: 拍照识物、美食推荐
- **办公效率**: 文档处理、数据分析
- **创意趣味**: 图像创作、视频制作
- **学习教育**: 学习辅导
- **健康医疗**: 健康顾问
- **其他**: 智能客服

#### AI助手 (10个)
1. **拍照识物** - 多模态图像识别助手
2. **美食推荐师** - 美食推荐和菜谱助手
3. **文档写作助手** - 各类文档撰写助手
4. **数据分析师** - 数据分析和报告生成助手
5. **图像创作大师** - 图像生成和创意设计助手
6. **视频制作专家** - 视频制作和编辑助手
7. **学习辅导老师** - 个性化学习辅导助手
8. **健康生活顾问** - 健康管理和咨询助手
9. **智能客服** - 7x24小时客服助手
10. **编程助手** - 代码编写和优化助手

#### 助手参数配置
- 每个助手都配置了个性化参数
- 支持识别模式、风格选择、难度设置等
- 参数类型包括：选择框、输入框、数字、布尔值等

#### 会员助手实例
- 为10个会员创建了个性化助手实例
- 包含自定义名称、参数覆盖、使用统计等
- 支持收藏和活跃状态管理

### 计费与消耗模块

#### 计费方案 (6个)
1. **按Token计费** - 适用于大语言模型
2. **按次数计费** - 适用于简单API调用
3. **按图片计费** - 适用于图像生成模型
4. **按秒计费** - 适用于音频视频处理
5. **包月计费** - 固定月费不限次数
6. **按分钟计费** - 适用于长时间处理任务

#### 计费套餐 (12个)
- **免费体验包** - 新用户免费体验
- **基础/Pro/企业月卡** - 不同级别月度套餐
- **基础/Pro/企业年卡** - 年度套餐享受优惠
- **Token资源包** - 小/中/大型一次性资源包
- **特殊套餐** - 学生专享、创作者专享

#### 模型价格配置
- 为主要AI模型配置了分级价格
- 支持按会员等级差异化定价
- 包含国外模型和国产模型的价格策略

#### 会员数据
- **余额管理** - 10个会员的余额和冻结金额
- **使用记录** - 详细的模型使用记录
- **交易流水** - 充值、消费、退款、赠送记录
- **订单数据** - 完整的订单生命周期数据

## 🔧 技术特点

### 1. 数据关联完整性
- 使用外键关联确保数据一致性
- 支持软删除机制
- 完整的审计字段（创建时间、更新时间、操作人等）

### 2. 业务场景覆盖
- **多种助手类型** - 覆盖生活、工作、学习、娱乐等场景
- **灵活参数配置** - 支持助手个性化定制
- **完整计费流程** - 从套餐购买到使用消费的完整链路
- **多样化定价** - 支持多种计费模式和会员分级

### 3. 真实业务数据
- **合理的使用统计** - 符合实际使用模式的数据分布
- **完整的交易记录** - 包含各种交易状态和场景
- **时间序列数据** - 模拟真实的时间分布

## ⚠️ 注意事项

### 数据依赖
- **会员数据** - 假设已存在会员ID 1-10，实际使用时需要调整
- **模型数据** - 依赖之前导入的AI模型数据
- **基础数据** - 需要货币、数据字典等基础数据支持

### 业务规则
- **余额管理** - 包含余额不足、冻结金额等业务场景
- **订单状态** - 涵盖待支付、已支付、已取消、支付失败等状态
- **使用限制** - 根据会员等级和套餐类型进行权限控制

### 扩展建议
- 可根据实际业务需求添加更多助手类型
- 可完善计费规则和价格策略
- 可增加更多会员数据和使用场景

## 📈 使用场景

1. **开发测试** - 为开发环境提供完整的测试数据
2. **功能演示** - 展示AI助手和计费功能的完整性
3. **性能测试** - 提供足够的数据量进行性能测试
4. **业务培训** - 作为业务培训和产品演示的数据基础

## 🔍 数据验证

执行验证脚本检查数据完整性：
```bash
psql -d aishowlab -f 数据验证脚本.20250804.sql
```

验证内容包括：
- 数据统计和分布
- 关联关系完整性检查
- 业务逻辑一致性验证
- 数据质量检查

## 📞 支持

如有问题或建议，请参考：
- 数据验证脚本: `数据验证脚本.20250804.sql`
- 项目设计文档: `doc/sql/design/AI助手.sql` 和 `doc/sql/design/计费与消耗.sql`
- 导入工具帮助: `./导入数据脚本.20250804.sh --help`

---

**AIShowLab Team**  
*让AI触手可及*
