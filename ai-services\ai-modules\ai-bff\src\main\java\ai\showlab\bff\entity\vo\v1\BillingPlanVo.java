package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 计费方案视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingPlanVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 计费方案名称
     */
    private String name;

    /**
     * 计费单位
     */
    private Integer unit;
    
    /**
     * 计费单位描述
     */
    private String unitDesc;

    /**
     * 排序值
     */
    private Integer sortOrder;

    /**
     * 方案详细描述
     */
    private String description;
}
