package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 会员助手实例状态切换参数
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantToggleParam {
    
    /**
     * 会员助手实例ID
     */
    @NotNull(message = "会员助手实例ID不能为空")
    private Long memberAssistantId;
    
    /**
     * 目标状态值
     */
    @NotNull(message = "目标状态不能为空")
    private Boolean targetStatus;
}
