package ai.showlab.bff.entity.vo.v1;

import ai.showlab.common.protocol.enums.BillingOrderStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 支付状态视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "orderNo")
public class PaymentStatusVo {

    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 订单状态 (字典: order_status), 1-待支付, 2-已完成, 3-已取消, 4-支付失败
     */
    private Integer orderStatus;
    
    /**
     * 订单状态描述
     */
    private String orderStatusDesc;
    
    /**
     * 支付状态 (pending, success, failed, cancelled, expired)
     */
    private String paymentStatus;
    
    /**
     * 支付状态描述
     */
    private String paymentStatusDesc;
    
    /**
     * 支付网关名称
     */
    private String gatewayName;
    
    /**
     * 支付网关交易流水号
     */
    private String gatewayTransactionId;
    
    /**
     * 订单金额
     */
    private BigDecimal amount;
    
    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;
    
    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;
    
    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;
    
    /**
     * 订单创建时间
     */
    private OffsetDateTime createTime;
    
    /**
     * 支付完成时间
     */
    private OffsetDateTime paidTime;
    
    /**
     * 支付失败原因（如果失败）
     */
    private String failureReason;
    
    /**
     * 套餐名称
     */
    private String packageName;
    
    /**
     * 套餐描述
     */
    private String packageDescription;

    /**
     * 检查订单是否可以支付
     *
     * @return 是否可以支付
     */
    public boolean canPay() {
        return BillingOrderStatusEnum.canPay(this.orderStatus);
    }

    /**
     * 检查订单是否可以取消
     *
     * @return 是否可以取消
     */
    public boolean canCancel() {
        return BillingOrderStatusEnum.canCancel(this.orderStatus);
    }

    /**
     * 检查订单是否已完成
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return BillingOrderStatusEnum.isCompleted(this.orderStatus);
    }

    /**
     * 检查订单是否已取消
     *
     * @return 是否已取消
     */
    public boolean isCancelled() {
        return BillingOrderStatusEnum.isCancelled(this.orderStatus);
    }

    /**
     * 检查订单支付是否失败
     *
     * @return 支付是否失败
     */
    public boolean isPaymentFailed() {
        return BillingOrderStatusEnum.isPaymentFailed(this.orderStatus);
    }

    /**
     * 检查订单是否为终态
     *
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return BillingOrderStatusEnum.isFinalStatus(this.orderStatus);
    }

    /**
     * 获取订单状态枚举
     *
     * @return 订单状态枚举
     */
    public BillingOrderStatusEnum getOrderStatusEnum() {
        return BillingOrderStatusEnum.getByCode(this.orderStatus);
    }
}
