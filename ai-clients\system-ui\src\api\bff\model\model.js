import request from '@/utils/request'

// 查询AI模型列表
export function listModel(query) {
  return request({
    url: '/system/model/model/list',
    method: 'get',
    params: query
  })
}

// 查询AI模型详细
export function getModel(id) {
  return request({
    url: '/system/model/model/' + id,
    method: 'get'
  })
}

// 新增AI模型
export function addModel(data) {
  return request({
    url: '/system/model/model',
    method: 'post',
    data: data
  })
}

// 修改AI模型
export function updateModel(data) {
  return request({
    url: '/system/model/model',
    method: 'put',
    data: data
  })
}

// 删除AI模型
export function delModel(id) {
  return request({
    url: '/system/model/model/' + id,
    method: 'delete'
  })
}
