package ai.showlab.common.protocol.enums;

import lombok.Getter;

/**
 * API接口权限认证类型枚举
 * <p>
 * 用于定义接口的访问权限级别和认证要求。
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public enum ApiAuthTypeEnum {

    /**
     * 类型1：匿名访问
     * 不需要登录，允许匿名访问
     */
    ANONYMOUS(1, "匿名访问", "不需要登录，允许匿名访问"),

    /**
     * 类型2：登录访问
     * 需要登录后才能访问，但不校验具体权限
     */
    LOGIN_REQUIRED(2, "登录访问", "需要登录后才能访问，但不校验具体权限"),

    /**
     * 类型3：权限访问
     * 必须登录，并且要按会员ID校验角色权限
     */
    PERMISSION_REQUIRED(3, "权限访问", "必须登录，并且要按会员ID校验角色权限");

    /**
     * 类型值
     * -- GETTER --
     *  获取类型值
     *
     * @return 类型值

     */
    private final int type;

    /**
     * 类型名称
     * -- GETTER --
     *  获取类型名称
     *
     * @return 类型名称

     */
    private final String name;

    /**
     * 类型描述
     * -- GETTER --
     *  获取类型描述
     *
     * @return 类型描述

     */
    private final String description;

    /**
     * 构造函数
     *
     * @param type 类型值
     * @param name 类型名称
     * @param description 类型描述
     */
    ApiAuthTypeEnum(int type, String name, String description) {
        this.type = type;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ApiAuthTypeEnum getByType(int type) {
        for (ApiAuthTypeEnum authType : values()) {
            if (authType.getType() == type) {
                return authType;
            }
        }
        return null;
    }

    /**
     * 判断是否需要登录
     *
     * @return true表示需要登录，false表示不需要
     */
    public boolean requiresLogin() {
        return this != ANONYMOUS;
    }

    /**
     * 判断是否需要权限校验
     *
     * @return true表示需要权限校验，false表示不需要
     */
    public boolean requiresPermission() {
        return this == PERMISSION_REQUIRED;
    }

    @Override
    public String toString() {
        return String.format("ApiAuthTypeEnum{type=%d, name='%s', description='%s'}", type, name, description);
    }
}
