# AIShowLab 模型管理模拟数据

本目录包含了AIShowLab项目模型管理模块的完整模拟数据，涵盖了LLM、图像生成、视频生成、多模态四种主要AI服务类型。

## 📁 文件列表

### 核心数据文件
- **`模型管理模拟数据.20241204.sql`** - 主要的模拟数据SQL脚本
- **`验证模型数据.20241204.sql`** - 数据验证和统计查询脚本

### 文档文件
- **`模型管理数据说明.20241204.md`** - 详细的数据说明文档
- **`README.md`** - 本文件，项目概览

### 导入工具
- **`导入模型数据.20241204.sh`** - Linux/macOS 导入脚本
- **`导入模型数据.20241204.bat`** - Windows 导入脚本

## 🚀 快速开始

### 前置条件
1. PostgreSQL 数据库已安装并运行
2. 已创建数据库和相关表结构（执行 `doc/sql/design/模型管理.sql`）
3. 已导入基础数据（货币、数据字典等）

### 导入数据

#### Linux/macOS
```bash
# 基本导入
./导入模型数据.20241204.sh -d aishowlab -u postgres

# 指定完整连接参数
./导入模型数据.20241204.sh -h localhost -p 5432 -d aishowlab -u postgres -w password

# 仅验证SQL语法
./导入模型数据.20241204.sh --dry-run
```

#### Windows
```cmd
REM 基本导入
导入模型数据.20241204.bat -d aishowlab -u postgres

REM 指定完整连接参数
导入模型数据.20241204.bat -h localhost -p 5432 -d aishowlab -u postgres -w password

REM 仅验证SQL语法
导入模型数据.20241204.bat --dry-run
```

#### 手动导入
```bash
psql -d aishowlab -f 模型管理模拟数据.20241204.sql
```

### 验证数据
```bash
# 执行验证脚本
psql -d aishowlab -f 验证模型数据.20241204.sql

# 或者手动查询
psql -d aishowlab -c "SELECT c.name, COUNT(m.id) as model_count FROM a_model_category c LEFT JOIN a_model m ON c.id = m.category_id AND m.delete_time IS NULL WHERE c.delete_time IS NULL GROUP BY c.id, c.name ORDER BY c.sort_order;"
```

## 📊 数据概览

### 模型分类 (6个)
- **LLM大语言模型** - 14个模型
- **图像生成** - 6个模型  
- **视频生成** - 4个模型
- **多模态** - 4个模型
- **语音处理** - 0个模型（预留）
- **向量嵌入** - 0个模型（预留）

### 供应商 (12个)
- **国外**: OpenAI, Anthropic, Google, Stability AI, Midjourney, Runway, Pika Labs
- **国内**: 百度, 阿里云, 腾讯云, 智谱AI
- **本地**: 开源模型本地部署

### 主要模型
#### LLM大语言模型 (14个)
- GPT-4 Turbo, GPT-4, GPT-3.5 Turbo
- Claude 3.5 Sonnet, Claude 3 Opus, Claude 3 Haiku
- Gemini 1.5 Pro, Gemini Pro
- 文心一言 4.0, 通义千问 Max, 混元大模型, GLM-4
- Llama 3.1 70B, Qwen2.5 72B

#### 图像生成模型 (6个)
- DALL-E 3, DALL-E 2
- Stable Diffusion XL, Stable Diffusion 3
- Midjourney V6
- Stable Diffusion WebUI (本地)

#### 视频生成模型 (4个)
- Runway Gen-3, Runway Gen-2
- Pika 1.0
- AnimateDiff (本地)

#### 多模态模型 (4个)
- GPT-4 Vision
- Gemini 1.5 Pro Vision
- Claude 3.5 Sonnet Vision
- LLaVA 1.6 (本地)

## 🔧 技术特点

### 1. 智能关联设计
- 使用模型编码(code)进行关联，避免硬编码ID依赖
- 支持分批导入和重复执行
- 提高数据维护的可靠性

### 2. 完整的配置覆盖
- **模型特性**: 上下文长度、最大token、温度范围、支持语言等
- **输出格式**: JSON、文本、图像、视频四种格式
- **API密钥**: 包含额度管理和负载均衡配置
- **权限控制**: 按会员等级的可见性控制

### 3. 实用的示例数据
- 真实的模型参数配置
- 完整的供应商信息
- 合理的权限分级策略

## ⚠️ 注意事项

### 安全提醒
- **API密钥**: 示例中的API密钥为演示数据，实际使用时必须加密存储
- **密码管理**: 建议使用 `.pgpass` 文件管理数据库密码

### 数据依赖
- 确保已导入基础数据（货币表、数据字典等）
- 会员等级引用: 1=免费，2=VIP，3=企业
- 货币引用: 1=CNY，2=USD

### 扩展建议
- 可根据实际需求添加更多AI模型
- 可完善模型特性配置
- 可增加更细粒度的权限控制

## 📈 使用场景

1. **开发测试**: 为开发环境提供完整的测试数据
2. **功能演示**: 展示AI模型管理功能的完整性
3. **性能测试**: 提供足够的数据量进行性能测试
4. **培训教学**: 作为学习AI模型管理的参考数据

## 🔄 更新记录

- **2024-12-04**: 初始版本，包含28个AI模型的完整数据
- **2024-12-04**: 优化数据关联方式，使用模型编码替代硬编码ID
- **2024-12-04**: 添加导入脚本和验证工具

## 📞 支持

如有问题或建议，请参考：
- 详细说明文档: `模型管理数据说明.20241204.md`
- 数据验证脚本: `验证模型数据.20241204.sql`
- 项目文档: `doc/sql/design/模型管理.sql`

---

**AIShowLab Team**  
*让AI触手可及*
