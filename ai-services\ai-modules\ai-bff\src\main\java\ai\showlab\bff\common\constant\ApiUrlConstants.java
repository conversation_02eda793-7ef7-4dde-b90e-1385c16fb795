package ai.showlab.bff.common.constant;

import com.ruoyi.common.core.utils.StringUtils;

/**
 * API URL路径常量配置
 * <p>
 * 统一管理API路径配置，避免在多个配置文件中重复定义。
 * 用于Spring Security和拦截器的URL路径配置。
 * </p>
 *
 * <AUTHOR>
 */
public class ApiUrlConstants {

    /**
     * 所有API接口路径模式
     */
    public static final String API_PATTERN = "/api/**";

    /**
     * 需要排除的公共路径（不需要任何认证和权限校验）
     */
    public static final String[] PUBLIC_PATHS = {
            // 健康检查接口
            "/api/health",
            "/api/actuator/**",
            // Swagger文档接口
            "/api/v*/api-docs/**",
            "/api/swagger-ui/**",
            "/api/swagger-resources/**",
            "/api/webjars/**"
    };

    /**
     * 需要排除JWT认证但可能需要权限校验的路径
     * （这些路径由拦截器根据@ApiAuth注解处理）
     * 注意：这里列出的路径会跳过Spring Security的JWT认证，
     * 但仍然会被ApiAuthInterceptor处理，根据@ApiAuth注解决定是否需要权限校验。
     */
    public static final String[] AUTH_EXCLUDED_PATHS = {
            // 会员注册登录接口（匿名访问）
            "/api/v1/member/register",
            "/api/v1/member/login",
            "/api/v1/member/sendPasswordResetCode",
            "/api/v1/member/resetPassword",
            "/api/v1/member/token/anti-resubmit",

            // 公共接口（匿名访问）
            "/api/v1/common/**",

            // 助手相关的匿名访问接口
            "/api/v1/assistant/categories",
            "/api/v1/assistant/list",
            "/api/v1/assistant/detail/**",
            "/api/v1/assistant/popular",

            // 其他可能需要匿名访问的接口
            "/api/v1/captcha/**",
            "/api/v1/upload/public/**"
    };

    /**
     * 获取所有需要排除认证的路径
     * （包括公共路径和认证排除路径）
     */
    public static String[] getAllExcludedPaths() {
        String[] allPaths = new String[PUBLIC_PATHS.length + AUTH_EXCLUDED_PATHS.length];
        System.arraycopy(PUBLIC_PATHS, 0, allPaths, 0, PUBLIC_PATHS.length);
        System.arraycopy(AUTH_EXCLUDED_PATHS, 0, allPaths, PUBLIC_PATHS.length, AUTH_EXCLUDED_PATHS.length);
        return allPaths;
    }

    /**
     * 检查路径是否为公共路径
     *
     * @param path 请求路径
     * @return true表示是公共路径，false表示不是
     */
    public static boolean isPublicPath(String path) {
        if (StringUtils.isEmpty(path)) {
            return false;
        }
        for (String pattern : PUBLIC_PATHS) {
            if (pathMatches(path, pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查路径是否为认证排除路径
     * <p>
     * 认证排除路径会跳过Spring Security的JWT认证，
     * 但仍然会被ApiAuthInterceptor根据@ApiAuth注解处理。
     * </p>
     *
     * @param path 请求路径
     * @return true表示是认证排除路径，false表示不是
     */
    public static boolean isAuthExcludedPath(String path) {
        if (StringUtils.isEmpty(path)) {
            return false;
        }
        for (String pattern : AUTH_EXCLUDED_PATHS) {
            if (pathMatches(path, pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 路径匹配方法（支持Spring风格的通配符）
     * <p>
     * 支持的模式：
     * - /api/v1/common/document - 精确匹配
     * - /api/v1/common/** - 匹配/api/v1/common/及其所有子路径
     * - /api/v1/* /document - 匹配单级通配符
     * </p>
     *
     * @param path    实际路径
     * @param pattern 路径模式
     * @return true表示匹配，false表示不匹配
     */
    private static boolean pathMatches(String path, String pattern) {
        // 精确匹配
        if (pattern.equals(path)) {
            return true;
        }

        // 处理 /** 通配符（匹配任意子路径）
        if (pattern.endsWith("/**")) {
            String prefix = pattern.substring(0, pattern.length() - 3);
            return path.startsWith(prefix + "/") || path.equals(prefix);
        }

        // 处理其他通配符
        if (pattern.contains("*")) {
            // 将Spring风格的通配符转换为正则表达式
            String regex = pattern
                    // /** 匹配任意子路径
                    .replace("/**", "/.*")
                    // /* 匹配单级路径
                    .replace("/*", "/[^/]*")
                    // * 匹配除/外的任意字符
                    .replace("*", "[^/]*");

            return path.matches(regex);
        }

        return false;
    }

    /**
     * 测试路径匹配功能（仅用于开发调试）
     * <p>
     * 可以在开发环境中调用此方法来验证路径匹配是否正确工作。
     * </p>
     */
    public static void testPathMatching() {
        // 测试用例
        String[] testPaths = {
                "/api/v1/common/document",
                "/api/v1/common/document/privacy-policy",
                "/api/v1/assistant/categories",
                "/api/v1/assistant/list",
                "/api/v1/member/profile"
        };

        System.out.println("=== 路径匹配测试 ===");
        for (String path : testPaths) {
            boolean isPublic = isPublicPath(path);
            boolean isAuthExcluded = isAuthExcludedPath(path);
            System.out.printf("路径: %-35s | 公共路径: %-5s | 认证排除: %-5s%n",
                    path, isPublic, isAuthExcluded);
        }
        System.out.println("==================");
    }
}
