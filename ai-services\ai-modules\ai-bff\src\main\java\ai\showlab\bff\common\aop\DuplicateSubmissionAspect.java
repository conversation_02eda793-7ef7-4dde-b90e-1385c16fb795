package ai.showlab.bff.common.aop;

import ai.showlab.bff.common.annotation.PreventDuplicateSubmission;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.entity.bo.LoginMember;
import ai.showlab.bff.service.common.IDuplicateSubmissionTokenService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

/**
 * 防重复提交切面
 * 拦截带有 @PreventDuplicateSubmission 注解的方法，进行 Token 校验。
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class DuplicateSubmissionAspect {

    private static final String SUBMISSION_TOKEN_HEADER = "X-Submission-Token";

    @Autowired
    private IDuplicateSubmissionTokenService duplicateSubmissionTokenService;

    /**
     * 定义切点，拦截所有带有 @PreventDuplicateSubmission 注解的方法
     */
    @Pointcut("@annotation(ai.showlab.bff.common.annotation.PreventDuplicateSubmission)")
    public void preventDuplicateSubmissionPointcut() {
    }

    /**
     * 环绕通知，在方法执行前后进行处理
     * @param joinPoint 切点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("preventDuplicateSubmissionPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (attributes != null) {
            request = attributes.getRequest();
        }
        String submissionToken = request != null ? request.getHeader(SUBMISSION_TOKEN_HEADER) : null;
        if (submissionToken == null) {
            throw new BusinessException("缺少防重复提交Token");
        }
        // 获取当前用户ID，这里需要根据实际认证情况获取
        String userId = getCurrentMemberId();
        // 移除原有的 userId == null 检查，因为 getCurrentUserId 现在会返回 Session ID

        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        PreventDuplicateSubmission annotation = method.getAnnotation(PreventDuplicateSubmission.class);
        String keyPrefix = annotation.keyPrefix();

        // 校验并消耗 Token
        boolean isValid = duplicateSubmissionTokenService.validateAndConsumeToken(submissionToken, userId);
        if (!isValid) {
            // validateAndConsumeToken 内部会抛出 BusinessException
            throw new BusinessException("防重复提交Token无效或已被消耗");
        }
        return joinPoint.proceed();
    }

    /**
     * 获取当前登录用户ID
     * @return 用户ID字符串
     */
    private String getCurrentMemberId() {
        LoginMember loginMember = null;
        try {
            SecurityContext securityContext = SecurityContextHolder.getContext();
            if (securityContext != null) {
                Authentication authentication = securityContext.getAuthentication();
                if (authentication != null) {
                    Object principal = authentication.getPrincipal();
                    if (principal instanceof LoginMember) {
                        loginMember = (LoginMember) principal;
                    }
                }
            }
        } catch (Exception e) {
            // Log this, but don't rethrow yet. We will try session ID.
            log.warn("尝试从 SecurityContextHolder 获取当前登录会员信息失败：{}", e.getMessage());
        }

        if (loginMember != null && loginMember.getMemberId() != null) {
            return "user:" + loginMember.getMemberId();
        } else {
            log.warn("无法从 SecurityContextHolder 获取有效会员ID，尝试使用 IP地址+Session ID 作为防重复提交Token的标识。");
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                try {
                    // 获取客户端IP地址
                    String clientIp = getClientIpAddress(request);

                    // 获取或创建SessionID
                    jakarta.servlet.http.HttpSession session = request.getSession(true);
                    String sessionId = session.getId();

                    String identifier = "guest:" + clientIp + ":" + sessionId;
                    log.debug("使用访客标识作为防重复提交Token标识: {}", identifier);
                    return identifier;
                } catch (IllegalStateException ise) {
                    // getSession() can throw IllegalStateException if the response has been committed
                    log.error("获取 HttpSession 失败 (可能响应已提交)，将生成 UUID 作为防重复提交Token的标识", ise);
                    return "guest:unknown:" + java.util.UUID.randomUUID();
                }
            }
            log.error("无法从 HttpServletRequest 获取有效的请求信息，将生成临时UUID作为标识。");
            return "guest:unknown:" + java.util.UUID.randomUUID();
        }
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (org.springframework.util.StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return normalizeIpAddress(xForwardedFor.split(",")[0].trim());
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (org.springframework.util.StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return normalizeIpAddress(xRealIp);
        }

        return normalizeIpAddress(request.getRemoteAddr());
    }

    /**
     * 标准化IP地址，将IPv6的本地回环地址转换为IPv4格式
     *
     * @param ip 原始IP地址
     * @return 标准化后的IP地址
     */
    private String normalizeIpAddress(String ip) {
        if (ip == null) {
            return "unknown";
        }
        // 将IPv6的本地回环地址转换为IPv4格式
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return "127.0.0.1";
        }
        return ip;
    }
}