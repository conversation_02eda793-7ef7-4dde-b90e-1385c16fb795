package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权限主体类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SubjectTypeEnum {
    
    /** 会员 */
    MEMBER(1, "会员"),
    
    /** 角色 */
    ROLE(2, "角色");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static SubjectTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SubjectTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 