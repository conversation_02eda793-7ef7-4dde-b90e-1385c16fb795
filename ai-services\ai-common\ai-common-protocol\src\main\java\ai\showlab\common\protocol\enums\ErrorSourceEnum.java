package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 错误来源枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ErrorSourceEnum {
    
    /** 系统 */
    SYSTEM(1, "系统"),
    
    /** 模型 */
    MODEL(2, "模型"),
    
    /** 网络 */
    NETWORK(3, "网络"),
    
    /** 数据库 */
    DATABASE(4, "数据库");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ErrorSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ErrorSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 