package ai.showlab.bff.common.docs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AI助手相关API文档注解
 * <p>
 * 为AI助手相关的接口提供统一的API文档注解。
 * </p>
 *
 * <AUTHOR>
 */
public class AssistantApiAnnotations {

    // ==================== 助手分类相关API ====================

    /**
     * 获取助手分类列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取助手分类列表",
            description = "获取所有助手分类的树形结构，用于前端分类筛选和展示"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAssistantCategoriesApiDoc {
    }

    // ==================== 助手查询相关API ====================

    /**
     * 获取助手列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取助手列表",
            description = "分页查询助手列表，支持按分类、关键词、交互模式等条件筛选"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAssistantListApiDoc {
    }

    /**
     * 获取助手详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取助手详情",
            description = "根据助手ID获取详细信息，包括参数配置、模型建议、关联知识库等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "助手不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAssistantDetailApiDoc {
    }

    /**
     * 根据编码获取助手API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "根据编码获取助手",
            description = "根据助手编码获取助手信息，用于助手调用前的信息获取"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "404", description = "助手不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAssistantByCodeApiDoc {
    }

    /**
     * 获取热门助手API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取热门助手列表",
            description = "获取热门推荐的助手列表，用于首页展示"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetPopularAssistantsApiDoc {
    }

    /**
     * 获取可访问助手API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取可访问助手列表",
            description = "获取当前会员可访问的助手列表，根据会员权限进行过滤"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAccessibleAssistantsApiDoc {
    }

    // ==================== 助手收藏相关API ====================

    /**
     * 获取收藏助手API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取收藏助手列表",
            description = "获取当前会员收藏的助手列表"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetFavoriteAssistantsApiDoc {
    }

    /**
     * 收藏助手API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "收藏助手",
            description = "将指定助手添加到当前会员的收藏列表"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "收藏成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "助手不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface FavoriteAssistantApiDoc {
    }

    /**
     * 取消收藏助手API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "取消收藏助手",
            description = "将指定助手从当前会员的收藏列表中移除"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "取消收藏成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "助手不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UnfavoriteAssistantApiDoc {
    }

    // ==================== 使用统计相关API ====================

    /**
     * 获取会员使用统计API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取会员助手使用统计",
            description = "获取当前会员的助手使用统计信息，包括总体使用情况"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetMemberUsageStatsApiDoc {
    }

    /**
     * 获取助手使用统计API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取助手使用统计",
            description = "获取当前会员对指定助手的使用统计信息"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "助手不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetAssistantUsageStatsApiDoc {
    }

    // ==================== 会员助手实例管理API ====================

    /**
     * 获取会员助手实例列表API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的助手列表",
            description = "获取当前会员的助手实例列表，支持按条件筛选和分页"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetMemberAssistantListApiDoc {
    }

    /**
     * 获取会员助手实例详情API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取我的助手详情",
            description = "获取指定会员助手实例的详细信息，包括配置参数、使用统计等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetMemberAssistantDetailApiDoc {
    }

    /**
     * 创建会员助手实例API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "创建我的助手",
            description = "基于助手模板为当前会员创建个性化的助手实例"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "404", description = "助手模板不存在"),
            @ApiResponse(responseCode = "409", description = "助手实例已存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface CreateMemberAssistantApiDoc {
    }

    /**
     * 更新会员助手实例API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "更新我的助手配置",
            description = "更新会员助手实例的配置信息，如名称、模型、参数等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UpdateMemberAssistantApiDoc {
    }

    /**
     * 删除会员助手实例API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "删除我的助手",
            description = "删除指定的会员助手实例"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface DeleteMemberAssistantApiDoc {
    }

    // ==================== 会员助手实例状态管理API ====================

    /**
     * 切换会员助手实例状态API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "切换助手状态",
            description = "激活或停用指定的会员助手实例"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "状态切换成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface ToggleMemberAssistantStatusApiDoc {
    }

    /**
     * 切换会员助手实例收藏状态API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "切换助手收藏状态",
            description = "收藏或取消收藏指定的会员助手实例"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "收藏状态切换成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface ToggleMemberAssistantFavoriteApiDoc {
    }

    // ==================== 会员助手实例配置管理API ====================

    /**
     * 更新会员助手实例名称API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "更新助手名称",
            description = "更新会员助手实例的自定义名称"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "名称更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UpdateMemberAssistantNameApiDoc {
    }

    /**
     * 更新会员助手实例模型API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "更新助手模型",
            description = "更新会员助手实例使用的AI模型"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "模型更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例或模型不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UpdateMemberAssistantModelApiDoc {
    }

    /**
     * 更新会员助手实例参数配置API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "更新助手参数配置",
            description = "更新会员助手实例的参数配置，如唤醒词、响应风格等"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "参数配置更新成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface UpdateMemberAssistantSettingsApiDoc {
    }

    // ==================== 会员助手实例快捷查询API ====================

    /**
     * 获取激活的会员助手实例API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取激活的助手列表",
            description = "获取当前会员所有激活状态的助手实例"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetActiveMemberAssistantsApiDoc {
    }

    /**
     * 获取收藏的会员助手实例API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取收藏的助手列表",
            description = "获取当前会员收藏的助手实例列表"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetFavoriteMemberAssistantsApiDoc {
    }

    /**
     * 获取会员助手实例使用统计API文档
     */
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    @Operation(
            summary = "获取助手实例使用统计",
            description = "获取指定会员助手实例的使用统计信息"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功"),
            @ApiResponse(responseCode = "400", description = "参数错误"),
            @ApiResponse(responseCode = "401", description = "未授权"),
            @ApiResponse(responseCode = "403", description = "无权访问"),
            @ApiResponse(responseCode = "404", description = "助手实例不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public @interface GetMemberAssistantUsageStatsApiDoc {
    }
}
