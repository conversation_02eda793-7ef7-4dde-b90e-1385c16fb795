import request from '@/utils/request'

// 查询会员余额列表
export function listBillingBalance(query) {
  return request({
    url: '/system/billing/balance/list',
    method: 'get',
    params: query
  })
}

// 查询会员余额详细
export function getBillingBalance(id) {
  return request({
    url: '/system/billing/balance/' + id,
    method: 'get'
  })
}

// 新增会员余额
export function addBillingBalance(data) {
  return request({
    url: '/system/billing/balance',
    method: 'post',
    data: data
  })
}

// 修改会员余额
export function updateBillingBalance(data) {
  return request({
    url: '/system/billing/balance',
    method: 'put',
    data: data
  })
}

// 删除会员余额
export function delBillingBalance(id) {
  return request({
    url: '/system/billing/balance/' + id,
    method: 'delete'
  })
}
