import request from '@/utils/request'

// 查询助手分类列表
export function listAssistantCategory(query) {
  return request({
    url: '/system/assistant/category/list',
    method: 'get',
    params: query
  })
}

// 查询助手分类详细
export function getAssistantCategory(id) {
  return request({
    url: '/system/assistant/category/' + id,
    method: 'get'
  })
}

// 新增助手分类
export function addAssistantCategory(data) {
  return request({
    url: '/system/assistant/category',
    method: 'post',
    data: data
  })
}

// 修改助手分类
export function updateAssistantCategory(data) {
  return request({
    url: '/system/assistant/category',
    method: 'put',
    data: data
  })
}

// 删除助手分类
export function delAssistantCategory(id) {
  return request({
    url: '/system/assistant/category/' + id,
    method: 'delete'
  })
}
