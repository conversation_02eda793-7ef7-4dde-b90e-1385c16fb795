package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单状态枚举
 * <p>
 * 定义订单的各种状态，用于订单生命周期管理。
 * </p>
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BillingOrderStatusEnum {

    /**
     * 待支付
     */
    PENDING_PAYMENT(1, "待支付"),

    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),

    /**
     * 已取消
     */
    CANCELLED(3, "已取消"),

    /**
     * 支付失败
     */
    PAYMENT_FAILED(4, "支付失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String type;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static BillingOrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BillingOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态描述获取枚举
     *
     * @param type 状态描述
     * @return 对应的枚举，如果不存在则返回null
     */
    public static BillingOrderStatusEnum getByType(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }
        for (BillingOrderStatusEnum status : values()) {
            if (status.getType().equals(type)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的状态码
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 检查订单是否可以支付
     *
     * @param code 状态码
     * @return 是否可以支付
     */
    public static boolean canPay(Integer code) {
        return PENDING_PAYMENT.getCode().equals(code);
    }

    /**
     * 检查订单是否可以取消
     *
     * @param code 状态码
     * @return 是否可以取消
     */
    public static boolean canCancel(Integer code) {
        return PENDING_PAYMENT.getCode().equals(code);
    }

    /**
     * 检查订单是否已完成
     *
     * @param code 状态码
     * @return 是否已完成
     */
    public static boolean isCompleted(Integer code) {
        return COMPLETED.getCode().equals(code);
    }

    /**
     * 检查订单是否已取消
     *
     * @param code 状态码
     * @return 是否已取消
     */
    public static boolean isCancelled(Integer code) {
        return CANCELLED.getCode().equals(code);
    }

    /**
     * 检查订单支付是否失败
     *
     * @param code 状态码
     * @return 支付是否失败
     */
    public static boolean isPaymentFailed(Integer code) {
        return PAYMENT_FAILED.getCode().equals(code);
    }

    /**
     * 检查订单是否为终态（已完成、已取消、支付失败）
     *
     * @param code 状态码
     * @return 是否为终态
     */
    public static boolean isFinalStatus(Integer code) {
        return isCompleted(code) || isCancelled(code) || isPaymentFailed(code);
    }

    /**
     * 获取所有状态码
     *
     * @return 状态码数组
     */
    public static Integer[] getAllCodes() {
        BillingOrderStatusEnum[] values = values();
        Integer[] codes = new Integer[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }

    /**
     * 获取所有状态描述
     *
     * @return 状态描述数组
     */
    public static String[] getAllTypes() {
        BillingOrderStatusEnum[] values = values();
        String[] types = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            types[i] = values[i].getType();
        }
        return types;
    }
}
