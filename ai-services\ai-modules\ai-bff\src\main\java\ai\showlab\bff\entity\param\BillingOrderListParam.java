package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 订单列表查询参数
 * 
 * <AUTHOR>
 */
@Data
public class BillingOrderListParam {
    
    /**
     * 订单状态，可选 (字典: order_status), 1-待支付, 2-已完成, 3-已取消, 4-支付失败
     */
    private Integer status;
    
    /**
     * 开始时间，可选
     */
    private OffsetDateTime startTime;
    
    /**
     * 结束时间，可选
     */
    private OffsetDateTime endTime;
    
    /**
     * 最小金额，可选
     */
    private java.math.BigDecimal minAmount;
    
    /**
     * 最大金额，可选
     */
    private java.math.BigDecimal maxAmount;
    
    /**
     * 排序字段，可选 (create_time-创建时间, amount-金额, paid_time-支付时间)
     */
    private String sortBy = "create_time";
    
    /**
     * 排序方向，可选 (asc-升序, desc-降序)
     */
    private String sortOrder = "desc";
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
