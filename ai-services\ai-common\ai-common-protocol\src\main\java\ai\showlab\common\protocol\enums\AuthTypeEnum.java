package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 认证方式枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuthTypeEnum {
    
    /** 密码 */
    PASSWORD(1, "密码"),
    
    /** 手机 */
    PHONE(2, "手机"),
    
    /** 邮箱 */
    EMAIL(3, "邮箱"),
    
    /** 谷歌 */
    GOOGLE(4, "谷歌"),
    
    /** GitHub */
    GITHUB(5, "GitHub"),
    
    /** 微信 */
    WECHAT(6, "微信");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static AuthTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AuthTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断指定code是否存在于枚举中
     *
     * @param code 编码
     * @return 是否存在
     */
    public static boolean existsByCode(Integer code) {
        if (code == null) {
            return false;
        }
        for (AuthTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
} 