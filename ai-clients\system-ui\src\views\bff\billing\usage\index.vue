<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员" prop="memberId">
        <el-input
          v-model="queryParams.memberId"
          placeholder="请输入会员"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模型" prop="modelId">
        <el-input
          v-model="queryParams.modelId"
          placeholder="请输入模型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计费方案" prop="planId">
        <el-input
          v-model="queryParams.planId"
          placeholder="请输入计费方案"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="计费单位" prop="unit">
        <el-select v-model="queryParams.unit" placeholder="请选择计费单位" clearable>
          <el-option
            v-for="dict in billing_unit"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="本次用量" prop="amount">
        <el-input
          v-model="queryParams.amount"
          placeholder="请输入本次用量"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求耗时" prop="durationMs">
        <el-input
          v-model="queryParams.durationMs"
          placeholder="请输入请求耗时"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结果大小" prop="resultSize">
        <el-input
          v-model="queryParams.resultSize"
          placeholder="请输入结果大小"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="使用时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUsedTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:billingUsage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:billingUsage:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:billingUsage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:billingUsage:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="billingUsageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="会员" align="center" prop="memberId" />
      <el-table-column label="模型" align="center" prop="modelId" />
      <el-table-column label="计费方案" align="center" prop="planId" />
      <el-table-column label="计费单位" align="center" prop="unit">
        <template #default="scope">
          <dict-tag :options="billing_unit" :value="scope.row.unit"/>
        </template>
      </el-table-column>
      <el-table-column label="本次用量" align="center" prop="amount" />
      <el-table-column label="请求耗时" align="center" prop="durationMs" />
      <el-table-column label="结果大小" align="center" prop="resultSize" />
      <el-table-column label="使用时间" align="center" prop="usedTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.usedTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:billingUsage:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:billingUsage:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员使用模型记录对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="billingUsageRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="会员" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员" />
        </el-form-item>
        <el-form-item label="模型" prop="modelId">
          <el-input v-model="form.modelId" placeholder="请输入模型" />
        </el-form-item>
        <el-form-item label="计费方案" prop="planId">
          <el-input v-model="form.planId" placeholder="请输入计费方案" />
        </el-form-item>
        <el-form-item label="计费单位" prop="unit">
          <el-select v-model="form.unit" placeholder="请选择计费单位">
            <el-option
              v-for="dict in billing_unit"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="本次用量" prop="amount">
          <el-input v-model="form.amount" placeholder="请输入本次用量" />
        </el-form-item>
        <el-form-item label="请求耗时" prop="durationMs">
          <el-input v-model="form.durationMs" placeholder="请输入请求耗时" />
        </el-form-item>
        <el-form-item label="结果大小" prop="resultSize">
          <el-input v-model="form.resultSize" placeholder="请输入结果大小" />
        </el-form-item>
        <el-form-item label="使用时间" prop="usedTime">
          <el-date-picker clearable
            v-model="form.usedTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择使用时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BillingUsage">
import { listBillingUsage, getBillingUsage, delBillingUsage, addBillingUsage, updateBillingUsage } from "@/api/bff/billing/usage"

const { proxy } = getCurrentInstance()
const { billing_unit } = proxy.useDict('billing_unit')

const billingUsageList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeUsedTime = ref([])
const daterangeCreateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    memberId: null,
    modelId: null,
    planId: null,
    unit: null,
    amount: null,
    durationMs: null,
    resultSize: null,
    usedTime: null,
    createTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询会员使用模型记录列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeUsedTime && '' != daterangeUsedTime) {
    queryParams.value.params["beginUsedTime"] = daterangeUsedTime.value[0]
    queryParams.value.params["endUsedTime"] = daterangeUsedTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  listBillingUsage(queryParams.value).then(response => {
    billingUsageList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    memberId: null,
    modelId: null,
    planId: null,
    unit: null,
    amount: null,
    durationMs: null,
    resultSize: null,
    usedTime: null,
    createTime: null
  }
  proxy.resetForm("billingUsageRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeUsedTime.value = []
  daterangeCreateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会员使用模型记录"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getBillingUsage(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会员使用模型记录"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["billingUsageRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateBillingUsage(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addBillingUsage(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除会员使用模型记录编号为"' + _ids + '"的数据项？').then(function() {
    return delBillingUsage(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/billing/usage/export', {
    ...queryParams.value
  }, `billingUsage_${new Date().getTime()}.xlsx`)
}

getList()
</script>
