# 模型管理模拟数据说明文档

## 文件信息
- **文件名**: 模型管理模拟数据.20241204.sql
- **创建时间**: 2024-12-04
- **数据库**: PostgreSQL
- **编码**: UTF-8

## 数据概览

本文件为AIShowLab项目的模型管理模块生成了完整的模拟数据，涵盖了四种主要的AI模型服务类型：

### 1. 模型服务类型
- **LLM大语言模型**: 14个模型（GPT、Claude、Gemini、国产LLM、开源模型）
- **图像生成**: 6个模型（DALL-E、Stable Diffusion、Midjourney等）
- **视频生成**: 4个模型（Runway、Pika、AnimateDiff等）
- **多模态**: 4个模型（GPT-4 Vision、Gemini Vision、Claude Vision、LLaVA）

### 2. 数据表结构

#### 2.1 模型分类表 (a_model_category)
```sql
- llm: LLM大语言模型
- image: 图像生成
- video: 视频生成
- multimodal: 多模态
- audio: 语音处理
- embedding: 向量嵌入
```

#### 2.2 模型供应商表 (a_model_provider)
包含12个主要供应商：
- **国外供应商**: OpenAI、Anthropic、Google、Stability AI、Midjourney、Runway、Pika Labs
- **国内供应商**: 百度、阿里云、腾讯云、智谱AI
- **本地部署**: 开源模型本地部署

#### 2.3 AI模型主表 (a_model)
总计28个模型，按类型分布：
- LLM模型: 14个
- 图像生成模型: 6个
- 视频生成模型: 4个
- 多模态模型: 4个

#### 2.4 模型特性表 (a_model_feature)
为主要模型配置了关键特性：
- **上下文长度**: context_length
- **最大输出**: max_tokens
- **温度范围**: temperature_range
- **支持语言**: languages
- **图像尺寸**: image_sizes
- **视频参数**: video_duration, video_resolution
- **多模态能力**: vision_capabilities

#### 2.5 模型输出格式表 (a_model_output_format)
定义了各模型支持的输出格式：
- **JSON格式** (output_type=1): 适用于LLM和多模态模型
- **文本格式** (output_type=2): 适用于LLM和多模态模型
- **图像格式** (output_type=3): 适用于图像生成模型
- **视频格式** (output_type=4): 适用于视频生成模型

#### 2.6 API密钥表 (a_model_api_key)
为各供应商配置了示例API密钥：
- 包含主要和备用密钥
- 设置了额度和使用情况
- 配置了优先级和权重

#### 2.7 模型可见性控制表 (a_model_visibility)
按会员等级控制模型访问权限：
- **免费会员**: 仅可访问GPT-3.5 Turbo
- **VIP会员**: 可访问GPT-4 Turbo、Claude 3.5 Sonnet、DALL-E 3
- **企业会员**: 可访问所有模型，包括视频生成

## 使用说明

### 1. 数据导入
```bash
# 确保已创建数据库和表结构
psql -d aishowlab -f "模型管理模拟数据.20241204.sql"
```

### 2. 注意事项
- **API密钥安全**: 实际环境中API密钥必须加密存储
- **模型关联优化**: 使用模型编码(code)进行关联，避免硬编码ID依赖，提高数据导入的可靠性
- **货币ID**: API密钥表中的currency_id引用基础货币表，1=CNY，2=USD
- **会员等级**: 可见性控制中的reference_id对应会员等级，1=免费，2=VIP，3=企业
- **数据依赖**: 确保在导入模型数据前，已导入基础数据（货币、数据字典等）

### 3. 数据验证
导入后可执行以下查询验证数据：

```sql
-- 查看模型分类统计
SELECT c.name, COUNT(m.id) as model_count 
FROM a_model_category c 
LEFT JOIN a_model m ON c.id = m.category_id AND m.delete_time IS NULL
WHERE c.delete_time IS NULL 
GROUP BY c.id, c.name 
ORDER BY c.sort_order;

-- 查看供应商模型分布
SELECT p.provider_name, COUNT(m.id) as model_count
FROM a_model_provider p
LEFT JOIN a_model m ON p.id = m.provider_id AND m.delete_time IS NULL
GROUP BY p.id, p.provider_name
ORDER BY model_count DESC;

-- 查看模型特性配置
SELECT m.name, mf.key, mf.value
FROM a_model m
JOIN a_model_feature mf ON m.id = mf.model_id
WHERE m.delete_time IS NULL AND mf.delete_time IS NULL
ORDER BY m.id, mf.key;
```

## 扩展建议

1. **添加更多模型**: 可根据实际需求添加更多AI模型
2. **完善特性配置**: 为更多模型添加详细的特性参数
3. **增加监控数据**: 可添加模型调用统计和性能监控数据
4. **权限细化**: 可进一步细化模型的访问权限控制

## 技术特点

### 1. 数据关联优化
- 使用模型编码(code)进行关联，而非硬编码ID
- 提高数据导入的可靠性和可维护性
- 支持分批导入和重复执行

### 2. 完整的数据覆盖
- 涵盖四大AI服务类型的主流模型
- 包含详细的模型特性和配置参数
- 提供完整的权限控制示例

### 3. 实用的示例数据
- API密钥配置示例（需加密处理）
- 会员等级权限控制
- 多种输出格式支持

## 更新记录
- 2024-12-04: 初始版本，包含28个AI模型的完整数据
- 2024-12-04: 优化数据关联方式，使用模型编码替代硬编码ID
