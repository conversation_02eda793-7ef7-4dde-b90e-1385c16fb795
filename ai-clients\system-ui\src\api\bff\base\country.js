import request from '@/utils/request'

// 查询国家地区列表
export function listBaseCountry(query) {
  return request({
    url: '/system/base/country/list',
    method: 'get',
    params: query
  })
}

// 查询国家地区详细
export function getBaseCountry(id) {
  return request({
    url: '/system/base/country/' + id,
    method: 'get'
  })
}

// 新增国家地区
export function addBaseCountry(data) {
  return request({
    url: '/system/base/country',
    method: 'post',
    data: data
  })
}

// 修改国家地区
export function updateBaseCountry(data) {
  return request({
    url: '/system/base/country',
    method: 'put',
    data: data
  })
}

// 删除国家地区
export function delBaseCountry(id) {
  return request({
    url: '/system/base/country/' + id,
    method: 'delete'
  })
}
