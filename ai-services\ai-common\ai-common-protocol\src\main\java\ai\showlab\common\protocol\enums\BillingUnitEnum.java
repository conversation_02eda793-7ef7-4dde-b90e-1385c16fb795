package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计费单位枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BillingUnitEnum {
    
    /** Token */
    TOKEN(1, "Token"),
    
    /** 次 */
    COUNT(2, "次"),
    
    /** 张(图) */
    IMAGE(3, "张(图)"),
    
    /** 秒(音频) */
    AUDIO_SECOND(4, "秒(音频)");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static BillingUnitEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BillingUnitEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 