package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审计动作枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditActionEnum {
    
    /** 会员登录 */
    MEMBER_LOGIN(1, "会员登录"),
    
    /** 会员登出 */
    MEMBER_LOGOUT(2, "会员登出"),
    
    /** 角色创建 */
    ROLE_CREATE(10, "角色创建"),
    
    /** 权限授予 */
    PERMISSION_GRANT(11, "权限授予"),
    
    /** 余额充值 */
    BALANCE_RECHARGE(20, "余额充值"),
    
    /** 余额消耗 */
    BALANCE_CONSUME(21, "余额消耗");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static AuditActionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AuditActionEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 