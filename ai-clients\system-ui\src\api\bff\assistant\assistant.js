import request from '@/utils/request'

// 查询助手定义列表
export function listAssistant(query) {
  return request({
    url: '/system/assistant/assistants/list',
    method: 'get',
    params: query
  })
}

// 查询助手定义详细
export function getAssistant(id) {
  return request({
    url: '/system/assistant/assistants/' + id,
    method: 'get'
  })
}

// 新增助手定义
export function addAssistant(data) {
  return request({
    url: '/system/assistant/assistants',
    method: 'post',
    data: data
  })
}

// 修改助手定义
export function updateAssistant(data) {
  return request({
    url: '/system/assistant/assistants',
    method: 'put',
    data: data
  })
}

// 删除助手定义
export function delAssistant(id) {
  return request({
    url: '/system/assistant/assistants/' + id,
    method: 'delete'
  })
}
