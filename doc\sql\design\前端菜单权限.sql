
CREATE TABLE a_func_menu (
	id bigserial NOT NULL, -- 功能ID
	pid int8 NULL, -- 父ID
	"name" varchar NOT NULL, -- 名称
	code varchar NOT NULL, -- 编码
	"type" int2 DEFAULT 1 NOT NULL, -- 功能类型，1-菜单，2-按钮
	sort_order int4 DEFAULT 1 NOT NULL, -- 排序
	status int2 DEFAULT 1 NOT NULL, -- 状态，1-启用，0-隐藏
    icon VARCHAR(100),
    component VARCHAR(255), -- 组件路径，用于指定前端Vue组件的位置，如views/system/user/index
    path VARCHAR(255), -- 路由路径
    redirect VARCHAR(255), -- 重定向路径
    affix BOOLEAN DEFAULT FALSE, -- 是否固定
    breadcrumb BOOLEAN DEFAULT TRUE, -- 是否显示面包屑
    is_cache BOOLEAN DEFAULT TRUE, -- 是否缓存
	create_by varchar NULL, -- 创建人
	update_by varchar NULL, -- 修改人
	create_time timestamptz DEFAULT now() NOT NULL, -- 创建时间
	update_time timestamptz DEFAULT now() NOT NULL, -- 修改时间
	CONSTRAINT a_func_menu_pk PRIMARY KEY (id),
	CONSTRAINT a_func_menu_unique UNIQUE (id, pid, code)
);
COMMENT ON TABLE public.a_func_menu IS 'app端 UI 导航结构';
COMMENT ON COLUMN public.a_func_menu.id IS '功能ID';
COMMENT ON COLUMN public.a_func_menu.pid IS '父ID，关联 a_func_menu.id';
COMMENT ON COLUMN public.a_func_menu."name" IS '名称';
COMMENT ON COLUMN public.a_func_menu.code IS '编码';
COMMENT ON COLUMN public.a_func_menu."type" IS '功能类型，1-菜单，2-按钮';
COMMENT ON COLUMN public.a_func_menu.sort_order IS '排序';
COMMENT ON COLUMN public.a_func_menu.status IS '状态，1-启用，0-隐藏';
COMMENT ON COLUMN public.a_func_menu.icon IS '图标';
COMMENT ON COLUMN public.a_func_menu.component IS '组件路径';
COMMENT ON COLUMN public.a_func_menu.path IS '路由路径';
COMMENT ON COLUMN public.a_func_menu.redirect IS '重定向路径';
COMMENT ON COLUMN public.a_func_menu.affix IS '是否固定';
COMMENT ON COLUMN public.a_func_menu.breadcrumb IS '是否显示面包屑';
COMMENT ON COLUMN public.a_func_menu.is_cache IS '是否缓存';
COMMENT ON COLUMN public.a_func_menu.create_by IS '创建人';
COMMENT ON COLUMN public.a_func_menu.update_by IS '修改人';
COMMENT ON COLUMN public.a_func_menu.create_time IS '创建时间';
COMMENT ON COLUMN public.a_func_menu.update_time IS '修改时间';

create trigger set_func_menu_update_time before
update
    on
    a_func_menu for each row execute function trigger_set_update_time();




-- ==================================================
-- 角色定义表 (a_func_role)
-- 定义系统中的所有角色。
-- ==================================================
CREATE TABLE a_func_role (
    id BIGSERIAL PRIMARY KEY,
    pid BIGINT,
    code VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    sort_order      INTEGER DEFAULT 0,
    description TEXT,
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    delete_time     TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time TIMESTAMPTZ DEFAULT NOW(),
    update_time TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_func_role_parent FOREIGN KEY (pid)
        REFERENCES a_func_role(id) ON DELETE SET NULL
);
COMMENT ON TABLE a_func_role IS '角色定义表，用于定义系统中的所有可用角色及其基本信息，如角色编码、名称和描述。';
COMMENT ON COLUMN a_func_role.id IS '主键ID';
COMMENT ON COLUMN a_func_role.pid IS '父角色ID，关联 a_func_role.id';
COMMENT ON COLUMN a_func_role.code IS '角色编码，程序中使用的唯一标识';
COMMENT ON COLUMN a_func_role.name IS '角色名称，用于展示';
COMMENT ON COLUMN a_func_role.sort_order IS '排序，desc';
COMMENT ON COLUMN a_func_role.description IS '角色详细描述';
COMMENT ON COLUMN a_func_role.is_system IS '是否为系统内置角色，内置角色通常不允许删除';
COMMENT ON COLUMN a_func_role.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_func_role.create_by IS '创建人';
COMMENT ON COLUMN a_func_role.update_by IS '修改人';
COMMENT ON COLUMN a_func_role.create_time IS '创建时间';
COMMENT ON COLUMN a_func_role.update_time IS '更新时间';

-- 为角色编码添加软删除感知的唯一索引
CREATE UNIQUE INDEX idx_func_role_code_unique ON a_func_role(code) WHERE delete_time IS NULL;


-- ==================================================
-- 权限组定义表 (a_func_permission_group)
-- 用于定义权限组，方便管理权限。
-- ==================================================
CREATE TABLE a_func_permission_group (
    id BIGSERIAL PRIMARY KEY,
    pid BIGINT,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    delete_time TIMESTAMPTZ,
    create_by VARCHAR(64),
    update_by VARCHAR(64),
    create_time TIMESTAMPTZ DEFAULT NOW(),
    update_time TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_func_permission_group_parent FOREIGN KEY (pid)
        REFERENCES a_func_permission_group(id) ON DELETE SET NULL
);
COMMENT ON TABLE a_func_permission_group IS '权限组定义表，用于定义权限组，方便管理权限。';
COMMENT ON COLUMN a_func_permission_group.id IS '主键ID';
COMMENT ON COLUMN a_func_permission_group.pid IS '父权限组ID，关联a_func_permission_group.id';
COMMENT ON COLUMN a_func_permission_group.code IS '权限组编码，程序中使用的唯一标识';
COMMENT ON COLUMN a_func_permission_group.name IS '权限组名称，用于界面展示';
COMMENT ON COLUMN a_func_permission_group.description IS '权限组详细描述';
COMMENT ON COLUMN a_func_permission_group.sort_order IS '排序值，用于排序';
COMMENT ON COLUMN a_func_permission_group.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_func_permission_group.create_by IS '创建人';
COMMENT ON COLUMN a_func_permission_group.update_by IS '修改人';
COMMENT ON COLUMN a_func_permission_group.create_time IS '创建时间';
COMMENT ON COLUMN a_func_permission_group.update_time IS '更新时间';




-- ==================================================
-- 权限定义表 (a_func_permission)
-- 定义系统中的所有权限点。
-- ==================================================
CREATE TABLE a_func_permission (
    id              BIGSERIAL PRIMARY KEY,
    group_id        BIGINT NOT NULL,
    code            VARCHAR(100) NOT NULL,  -- 权限唯一标识，如 "model.chat.use"
    name            VARCHAR(100) NOT NULL,         -- 权限名称，如 "使用聊天模型"
    description     TEXT,                          -- 详细描述
    is_system       BOOLEAN DEFAULT TRUE,          -- 是否系统内置权限
    action_type     smallint,                   -- 操作类型，如 "read", "write", "delete"
    resource_type   smallint,                   -- 资源类型，如 "model", "billing"
    delete_time     TIMESTAMPTZ,                   -- 软删除标记
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_func_permission_group FOREIGN KEY (group_id) REFERENCES a_func_permission_group(id) ON DELETE SET NULL
);
COMMENT ON TABLE a_func_permission IS '权限定义表，存储前端功能中所有可用的权限点，仅负责权限控制和访问授权';
COMMENT ON COLUMN a_func_permission.id IS '主键ID';
COMMENT ON COLUMN a_func_permission.group_id IS '权限组ID，关联a_func_permission_group.id';
COMMENT ON COLUMN a_func_permission.code IS '权限唯一标识，如 "model.chat.use"';
COMMENT ON COLUMN a_func_permission.name IS '权限名称，用于界面展示';
COMMENT ON COLUMN a_func_permission.description IS '权限的详细描述';
COMMENT ON COLUMN a_func_permission.is_system IS '是否为系统内置权限，内置权限通常不允许删除';
COMMENT ON COLUMN a_func_permission.action_type IS '操作类型，字典：func_permission_action_type, 如 "read", "write", "delete"';
COMMENT ON COLUMN a_func_permission.resource_type IS '资源类型，字典：func_permission_resource_type, 如 "model", "billing"';
COMMENT ON COLUMN a_func_permission.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_func_permission.create_by IS '创建人';
COMMENT ON COLUMN a_func_permission.update_by IS '修改人';
COMMENT ON COLUMN a_func_permission.create_time IS '创建时间';
COMMENT ON COLUMN a_func_permission.update_time IS '更新时间';

-- 索引
CREATE INDEX idx_func_permission_module ON a_func_permission(module);
-- 添加软删除感知的唯一索引
CREATE UNIQUE INDEX idx_func_permission_code_unique ON a_func_permission(code) WHERE delete_time IS NULL;



-- ==================================================
-- 菜单与权限关联表 (a_func_menu_permission)
-- 用于建立UI菜单项与权限控制之间的映射关系
-- ==================================================
CREATE TABLE a_func_menu_permission (
    id BIGSERIAL PRIMARY KEY,
    menu_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    required BOOLEAN DEFAULT TRUE, -- 是否必须拥有此权限才能看到菜单
    delete_time TIMESTAMPTZ,
    create_by VARCHAR(64),
    update_by VARCHAR(64),
    create_time TIMESTAMPTZ DEFAULT NOW(),
    update_time TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_menu_permission_menu FOREIGN KEY (menu_id) 
        REFERENCES a_func_menu(id) ON DELETE CASCADE,
    CONSTRAINT fk_menu_permission_permission FOREIGN KEY (permission_id) 
        REFERENCES a_func_permission(id) ON DELETE CASCADE
);

-- 添加软删除感知的唯一索引
CREATE UNIQUE INDEX idx_menu_permission_unique ON a_func_menu_permission(menu_id, permission_id) 
    WHERE delete_time IS NULL;

-- 添加索引提高查询性能
CREATE INDEX idx_menu_permission_menu_id ON a_func_menu_permission(menu_id);
CREATE INDEX idx_menu_permission_permission_id ON a_func_menu_permission(permission_id);

-- 添加更新时间触发器
CREATE TRIGGER set_menu_permission_update_time
BEFORE UPDATE ON a_func_menu_permission
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

COMMENT ON TABLE a_func_menu_permission IS '菜单与权限的关联表，用于建立UI菜单项与权限控制之间的映射关系';
COMMENT ON COLUMN a_func_menu_permission.menu_id IS '菜单ID，关联a_func_menu.id';
COMMENT ON COLUMN a_func_menu_permission.permission_id IS '权限ID，关联a_func_permission.id';
COMMENT ON COLUMN a_func_menu_permission.required IS '是否必须拥有此权限才能看到/访问菜单项';
COMMENT ON COLUMN a_func_menu_permission.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_func_menu_permission.create_by IS '创建人';
COMMENT ON COLUMN a_func_menu_permission.update_by IS '修改人';
COMMENT ON COLUMN a_func_menu_permission.create_time IS '创建时间';
COMMENT ON COLUMN a_func_menu_permission.update_time IS '更新时间';


-- ==================================================
-- 权限依赖关系表 (a_func_permission_dependency)
-- 用于定义权限之间的依赖关系
-- ==================================================
CREATE TABLE a_func_permission_dependency (
    id                  BIGSERIAL PRIMARY KEY,
    permission_id       BIGINT NOT NULL,
    dependency_id       BIGINT NOT NULL,
    is_required         BOOLEAN DEFAULT TRUE NOT NULL, -- TRUE=必须依赖，FALSE=可选依赖
    delete_time         TIMESTAMPTZ,
    create_by           VARCHAR(64),
    update_by           VARCHAR(64),
    create_time         TIMESTAMPTZ DEFAULT NOW(),
    update_time         TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_permission_dependency_permission FOREIGN KEY (permission_id)
        REFERENCES a_func_permission(id) ON DELETE CASCADE,
    CONSTRAINT fk_permission_dependency_dependency FOREIGN KEY (dependency_id)
        REFERENCES a_func_permission(id) ON DELETE CASCADE
);

COMMENT ON TABLE a_func_permission_dependency IS '权限依赖关系表，用于定义权限之间的依赖关系';
COMMENT ON COLUMN a_func_permission_dependency.id IS '主键ID';
COMMENT ON COLUMN a_func_permission_dependency.permission_id IS '权限ID，关联a_func_permission.id';
COMMENT ON COLUMN a_func_permission_dependency.dependency_id IS '依赖权限ID，关联a_func_permission.id';
COMMENT ON COLUMN a_func_permission_dependency.is_required IS 'TRUE表示必须依赖，FALSE表示可选依赖';
COMMENT ON COLUMN a_func_permission_dependency.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_func_permission_dependency.create_by IS '创建人';
COMMENT ON COLUMN a_func_permission_dependency.update_by IS '修改人';
COMMENT ON COLUMN a_func_permission_dependency.create_time IS '创建时间';
COMMENT ON COLUMN a_func_permission_dependency.update_time IS '更新时间';

-- 索引
CREATE INDEX idx_permission_dependency_permission_id ON a_func_permission_dependency(permission_id);
CREATE INDEX idx_permission_dependency_dependency_id ON a_func_permission_dependency(dependency_id);
-- 软删除感知的唯一索引
CREATE UNIQUE INDEX idx_permission_dependency_unique ON a_func_permission_dependency(permission_id, dependency_id) WHERE delete_time IS NULL;

-- 添加触发器
CREATE TRIGGER set_permission_dependency_update_time
BEFORE UPDATE ON a_func_permission_dependency
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();


-- ==================================================
-- 角色-权限映射表 (a_func_role_permission)
-- 用于将权限分配给角色。
-- ==================================================
CREATE TABLE a_func_role_permission (
    id              BIGSERIAL PRIMARY KEY,
    role_id         BIGINT NOT NULL,
    permission_id   BIGINT NOT NULL,
    valid_from      TIMESTAMPTZ,
    valid_to        TIMESTAMPTZ,
    scope           JSONB,                         -- 权限范围限制
    delete_time     TIMESTAMPTZ,                   -- 软删除标记
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_func_role_permission_role FOREIGN KEY (role_id) REFERENCES a_func_role(id) ON DELETE CASCADE,
    CONSTRAINT fk_func_role_permission_permission FOREIGN KEY (permission_id) REFERENCES a_func_permission(id) ON DELETE CASCADE
);
COMMENT ON TABLE a_func_role_permission IS '角色-权限映射表，用于将权限分配给角色';
COMMENT ON COLUMN a_func_role_permission.id IS '主键ID';
COMMENT ON COLUMN a_func_role_permission.role_id IS '角色ID，关联a_func_role.id';
COMMENT ON COLUMN a_func_role_permission.permission_id IS '权限ID，关联a_func_permission.id';
COMMENT ON COLUMN a_func_role_permission.scope IS '权限范围，使用JSONB存储，例如: {"models": ["gpt-4", "claude-3"]}';
COMMENT ON COLUMN a_func_role_permission.valid_from IS '权限生效时间';
COMMENT ON COLUMN a_func_role_permission.valid_to IS '权限失效时间';
COMMENT ON COLUMN a_func_role_permission.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_func_role_permission.create_by IS '创建人';
COMMENT ON COLUMN a_func_role_permission.update_by IS '修改人';
COMMENT ON COLUMN a_func_role_permission.create_time IS '创建时间';
COMMENT ON COLUMN a_func_role_permission.update_time IS '更新时间';

-- 索引
CREATE INDEX idx_func_role_permission_role_id ON a_func_role_permission(role_id);
CREATE INDEX idx_func_role_permission_permission_id ON a_func_role_permission(permission_id);
CREATE INDEX idx_func_role_permission_scope_gin ON a_func_role_permission USING GIN (scope);
-- 修改唯一约束为软删除感知的
CREATE UNIQUE INDEX idx_func_role_permission_unique ON a_func_role_permission(role_id, permission_id) WHERE delete_time IS NULL;


-- ==================================================
-- 会员-权限映射表 (a_member_permission)
-- 用于为特定会员授予或拒绝特定权限，覆盖角色权限。
-- 这个表用于处理特例情况，允许为特定会员直接授予或拒绝某些权限，覆盖其角色所带的权限。
-- ==================================================
CREATE TABLE a_member_permission (
    id              BIGSERIAL PRIMARY KEY,
    member_id       BIGINT NOT NULL,
    permission_id   BIGINT NOT NULL,
    is_granted      BOOLEAN NOT NULL DEFAULT TRUE,  -- TRUE=授予, FALSE=明确拒绝
    scope           JSONB,                          -- 权限范围限制
    valid_from      TIMESTAMPTZ,
    valid_to        TIMESTAMPTZ,
    delete_time     TIMESTAMPTZ,                    -- 软删除标记
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time     TIMESTAMPTZ DEFAULT NOW(),
    update_time     TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_member_permission_member FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE,
    CONSTRAINT fk_member_permission_permission FOREIGN KEY (permission_id) REFERENCES a_base_permission(id) ON DELETE CASCADE,
    UNIQUE (member_id, permission_id)
);

COMMENT ON TABLE a_member_permission IS '会员-权限映射表，用于为特定会员授予或拒绝特定权限，覆盖角色权限';
COMMENT ON COLUMN a_member_permission.member_id IS '会员ID，关联a_member.id';
COMMENT ON COLUMN a_member_permission.permission_id IS '权限ID，关联a_base_permission.id';
COMMENT ON COLUMN a_member_permission.is_granted IS 'TRUE表示授予权限，FALSE表示明确拒绝该权限（覆盖角色权限）';
COMMENT ON COLUMN a_member_permission.scope IS '权限范围，使用JSONB存储，例如: {"models": ["gpt-4", "claude-3"]}';
COMMENT ON COLUMN a_member_permission.valid_from IS '权限生效时间';
COMMENT ON COLUMN a_member_permission.valid_to IS '权限失效时间';
COMMENT ON COLUMN a_member_permission.delete_time IS '软删除标记时间';
COMMENT ON COLUMN a_member_permission.create_by IS '创建人';
COMMENT ON COLUMN a_member_permission.update_by IS '修改人';
COMMENT ON COLUMN a_member_permission.create_time IS '创建时间';
COMMENT ON COLUMN a_member_permission.update_time IS '更新时间';

-- 索引
CREATE INDEX idx_member_permission_member_id ON a_member_permission(member_id);
CREATE INDEX idx_member_permission_permission_id ON a_member_permission(permission_id);
CREATE INDEX idx_member_permission_scope_gin ON a_member_permission USING GIN (scope);
-- 修改唯一约束为软删除感知的
CREATE UNIQUE INDEX idx_member_permission_unique ON a_member_permission(member_id, permission_id) WHERE delete_time IS NULL;


-- ==================================================
-- 会员与角色映射表 (a_member_role)
-- 连接会员和角色，实现多对多关系。
-- ==================================================
CREATE TABLE a_member_role (
    id BIGSERIAL PRIMARY KEY,
    member_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    delete_time TIMESTAMPTZ,
    create_by       VARCHAR(64),
    update_by       VARCHAR(64),
    create_time TIMESTAMPTZ DEFAULT NOW(),
    update_time TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE (member_id, role_id)
);
COMMENT ON TABLE a_member_role IS '会员与角色的映射表，通过建立会员ID和角色ID的关联，实现会员与角色的多对多关系。';
COMMENT ON COLUMN a_member_role.id IS '主键ID';
COMMENT ON COLUMN a_member_role.member_id IS '会员ID（关联 a_member.id）';
COMMENT ON COLUMN a_member_role.role_id IS '角色ID（关联 a_func_role.id）';
COMMENT ON COLUMN a_member_role.create_time IS '创建时间';

-- 添加以下外键约束
ALTER TABLE a_member_role ADD CONSTRAINT fk_member_role_member
    FOREIGN KEY (member_id) REFERENCES a_member(id) ON DELETE CASCADE;
ALTER TABLE a_member_role ADD CONSTRAINT fk_member_role_role
    FOREIGN KEY (role_id) REFERENCES a_base_role(id) ON DELETE CASCADE;

-- 修改唯一约束为软删除感知的
CREATE UNIQUE INDEX idx_member_role_unique ON a_member_role(member_id, role_id) WHERE delete_time IS NULL;


CREATE TRIGGER set_func_role_update_time
BEFORE UPDATE ON a_func_role
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_func_role_permission_update_time
BEFORE UPDATE ON a_func_role_permission
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_func_permission_group_update_time
BEFORE UPDATE ON a_func_permission_group
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_permission_update_time
BEFORE UPDATE ON a_func_permission
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_menu_permission_update_time
BEFORE UPDATE ON a_func_menu_permission
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();

CREATE TRIGGER set_permission_dependency_update_time
BEFORE UPDATE ON a_func_permission_dependency
FOR EACH ROW
EXECUTE FUNCTION trigger_set_update_time();






