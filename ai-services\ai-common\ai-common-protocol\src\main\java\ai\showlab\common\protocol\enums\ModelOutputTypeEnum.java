package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型输出类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModelOutputTypeEnum {
    
    /** JSON */
    JSON(1, "JSON"),
    
    /** 文本 */
    TEXT(2, "文本"),
    
    /** 图片 */
    IMAGE(3, "图片"),
    
    /** 音频 */
    AUDIO(4, "音频");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ModelOutputTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelOutputTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 