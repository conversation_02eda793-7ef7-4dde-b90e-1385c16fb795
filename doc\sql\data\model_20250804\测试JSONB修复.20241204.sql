-- ====================================================================================
-- AIShowLab - JSONB格式修复测试脚本
-- 文件名: 测试JSONB修复.20241204.sql
-- 作者: AI Assistant
-- 描述: 测试模型特性表中JSONB字段的格式是否正确
-- 创建时间: 2024-12-04
-- ====================================================================================

-- ==================================================
-- 1. 测试单个JSONB值的插入
-- ==================================================

-- 测试字符串值（需要双引号）
SELECT '"high"'::jsonb as 字符串值测试;

-- 测试数字值（需要双引号作为字符串）
SELECT '"128000"'::jsonb as 数字字符串测试;

-- 测试布尔值（不需要引号）
SELECT 'true'::jsonb as 布尔值测试;

-- 测试JSON对象
SELECT '{"min": 0, "max": 2}'::jsonb as JSON对象测试;

-- 测试JSON数组
SELECT '["zh", "en", "ja", "ko"]'::jsonb as JSON数组测试;

-- ==================================================
-- 2. 测试模型特性表的JSONB插入
-- ==================================================

-- 创建临时测试表
CREATE TEMP TABLE test_model_feature (
    id BIGSERIAL PRIMARY KEY,
    model_id BIGINT,
    key VARCHAR(50),
    value JSONB,
    description TEXT
);

-- 测试各种JSONB格式的插入
INSERT INTO test_model_feature (model_id, key, value, description) VALUES
(1, 'context_length', '"128000"', '上下文长度（字符串格式）'),
(1, 'max_tokens', '"4096"', '最大token数（字符串格式）'),
(1, 'supports_stream', 'true', '是否支持流式（布尔值）'),
(1, 'temperature_range', '{"min": 0, "max": 2}', '温度范围（JSON对象）'),
(1, 'languages', '["zh", "en", "ja", "ko"]', '支持语言（JSON数组）'),
(1, 'safety_level', '"high"', '安全级别（字符串）'),
(1, 'image_sizes', '["1024x1024", "1024x1792"]', '图像尺寸（字符串数组）'),
(1, 'video_duration', '{"min": 4, "max": 10}', '视频时长（数字对象）');

-- 查询测试结果
SELECT 
    key as 特性键,
    value as 特性值,
    jsonb_typeof(value) as JSON类型,
    description as 描述
FROM test_model_feature
ORDER BY id;

-- ==================================================
-- 3. 测试JSONB查询操作
-- ==================================================

-- 测试JSON对象字段访问
SELECT 
    key,
    value,
    value->>'min' as 最小值,
    value->>'max' as 最大值
FROM test_model_feature 
WHERE jsonb_typeof(value) = 'object';

-- 测试JSON数组元素访问
SELECT 
    key,
    value,
    value->>0 as 第一个元素,
    jsonb_array_length(value) as 数组长度
FROM test_model_feature 
WHERE jsonb_typeof(value) = 'array';

-- 测试字符串值查询
SELECT 
    key,
    value,
    value #>> '{}' as 字符串值
FROM test_model_feature 
WHERE jsonb_typeof(value) = 'string';

-- ==================================================
-- 4. 验证修复后的数据格式
-- ==================================================

-- 如果模型特性表已存在数据，验证其格式
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'a_model_feature') THEN
        -- 检查是否有无效的JSONB值
        PERFORM 1 FROM a_model_feature WHERE value IS NULL;
        
        IF FOUND THEN
            RAISE NOTICE '发现NULL值的JSONB字段';
        ELSE
            RAISE NOTICE '所有JSONB字段都有有效值';
        END IF;
        
        -- 统计不同JSON类型的数量
        RAISE NOTICE '=== JSONB类型统计 ===';
        
        FOR rec IN 
            SELECT 
                jsonb_typeof(value) as json_type,
                COUNT(*) as count
            FROM a_model_feature 
            WHERE delete_time IS NULL
            GROUP BY jsonb_typeof(value)
            ORDER BY count DESC
        LOOP
            RAISE NOTICE '类型: %, 数量: %', rec.json_type, rec.count;
        END LOOP;
        
    ELSE
        RAISE NOTICE '模型特性表不存在，请先创建表结构';
    END IF;
END $$;

-- ==================================================
-- 5. 清理测试数据
-- ==================================================
DROP TABLE IF EXISTS test_model_feature;

-- ==================================================
-- 测试完成
-- ==================================================
SELECT '✓ JSONB格式测试完成' as 测试结果;
