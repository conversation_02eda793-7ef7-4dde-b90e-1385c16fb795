package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 使用记录列表查询参数
 * 
 * <AUTHOR>
 */
@Data
public class BillingUsageListParam {
    
    /**
     * 模型ID，可选
     */
    private Long modelId;
    
    /**
     * 计费方案ID，可选
     */
    private Long planId;
    
    /**
     * 计费单位，可选 (字典: billing_unit), 1-Token, 2-次, 3-张(图), 4-秒(音频)
     */
    private Integer unit;
    
    /**
     * 开始时间，可选
     */
    private OffsetDateTime startTime;
    
    /**
     * 结束时间，可选
     */
    private OffsetDateTime endTime;
    
    /**
     * 最小使用量，可选
     */
    private java.math.BigDecimal minAmount;
    
    /**
     * 最大使用量，可选
     */
    private java.math.BigDecimal maxAmount;
    
    /**
     * 排序字段，可选 (used_time-使用时间, amount-使用量, duration_ms-耗时)
     */
    private String sortBy = "used_time";
    
    /**
     * 排序方向，可选 (asc-升序, desc-降序)
     */
    private String sortOrder = "desc";
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
