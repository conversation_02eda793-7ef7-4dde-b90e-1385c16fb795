-- ====================================================================================
-- AIShowLab - 模型管理模块模拟数据 (PostgreSQL)
-- 文件名: 模型管理模拟数据.20241204.sql
-- 作者: AI Assistant
-- 描述: 为模型管理相关表生成模拟数据，包含LLM、图像生成、视频生成、多模态四种服务类型
-- 创建时间: 2024-12-04
-- ====================================================================================

-- ==================================================
-- 1. 模型分类数据 (a_model_category)
-- ==================================================
INSERT INTO a_model_category (code, name, sort_order, create_by, update_by) VALUES
('llm', 'LLM大语言模型', 1, 'system', 'system'),
('image', '图像生成', 2, 'system', 'system'),
('video', '视频生成', 3, 'system', 'system'),
('multimodal', '多模态', 4, 'system', 'system'),
('audio', '语音处理', 5, 'system', 'system'),
('embedding', '向量嵌入', 6, 'system', 'system');

-- ==================================================
-- 2. 模型供应商数据 (a_model_provider)
-- ==================================================
INSERT INTO a_model_provider (provider_name, provider_key, channel_type, sort_order, base_url, description, create_by, update_by) VALUES
('OpenAI', 'openai', '1', 1, 'https://api.openai.com', 'OpenAI官方API服务', 'system', 'system'),
('Anthropic', 'anthropic', '1', 2, 'https://api.anthropic.com', 'Anthropic Claude系列模型', 'system', 'system'),
('Google', 'google', '1', 3, 'https://generativelanguage.googleapis.com', 'Google Gemini系列模型', 'system', 'system'),
('百度', 'baidu', '1', 4, 'https://aip.baidubce.com', '百度文心一言系列模型', 'system', 'system'),
('阿里云', 'aliyun', '1', 5, 'https://dashscope.aliyuncs.com', '阿里云通义千问系列模型', 'system', 'system'),
('腾讯云', 'tencent', '1', 6, 'https://hunyuan.tencentcloudapi.com', '腾讯云混元系列模型', 'system', 'system'),
('智谱AI', 'zhipu', '1', 7, 'https://open.bigmodel.cn', '智谱GLM系列模型', 'system', 'system'),
('Stability AI', 'stability', '1', 8, 'https://api.stability.ai', 'Stable Diffusion图像生成', 'system', 'system'),
('Midjourney', 'midjourney', '2', 9, 'https://api.midjourney.com', 'Midjourney图像生成代理', 'system', 'system'),
('Runway', 'runway', '1', 10, 'https://api.runwayml.com', 'Runway视频生成', 'system', 'system'),
('Pika Labs', 'pika', '2', 11, 'https://api.pika.art', 'Pika视频生成代理', 'system', 'system'),
('本地部署', 'local', '1', 12, 'http://localhost:8080', '本地部署的开源模型', 'system', 'system');

-- ==================================================
-- 3. AI模型主表数据 (a_model) - LLM大语言模型
-- ==================================================
INSERT INTO a_model (name, code, source, category_id, provider_id, version, api_endpoint, invoke_method, supports_stream, supports_function, sort_order, description, is_enabled, artifact_path, artifact_type, create_by, update_by) VALUES
-- OpenAI GPT系列
('GPT-4 Turbo', 'gpt-4-turbo', 1, 1, 1, '2024-04', '/v1/chat/completions', 1, true, true, 1, 'OpenAI最新的GPT-4 Turbo模型，支持128K上下文，具备强大的推理和代码能力', true, null, null, 'system', 'system'),
('GPT-4', 'gpt-4', 1, 1, 1, '2024-03', '/v1/chat/completions', 1, true, true, 2, 'OpenAI GPT-4模型，8K上下文，适合复杂推理任务', true, null, null, 'system', 'system'),
('GPT-3.5 Turbo', 'gpt-3.5-turbo', 1, 1, 1, '2024-01', '/v1/chat/completions', 1, true, false, 3, 'OpenAI GPT-3.5 Turbo模型，性价比高，适合日常对话', true, null, null, 'system', 'system'),

-- Anthropic Claude系列
('Claude 3.5 Sonnet', 'claude-3-5-sonnet-20241022', 1, 1, 2, '2024-10', '/v1/messages', 1, true, true, 4, 'Anthropic最新的Claude 3.5 Sonnet模型，200K上下文，擅长分析和创作', true, null, null, 'system', 'system'),
('Claude 3 Opus', 'claude-3-opus-20240229', 1, 1, 2, '2024-02', '/v1/messages', 1, true, true, 5, 'Anthropic最强的Claude 3 Opus模型，适合复杂任务', true, null, null, 'system', 'system'),
('Claude 3 Haiku', 'claude-3-haiku-20240307', 1, 1, 2, '2024-03', '/v1/messages', 1, true, false, 6, 'Anthropic最快的Claude 3 Haiku模型，适合快速响应', true, null, null, 'system', 'system'),

-- Google Gemini系列
('Gemini 1.5 Pro', 'gemini-1.5-pro', 1, 1, 3, '2024-05', '/v1beta/models/gemini-1.5-pro:generateContent', 1, true, true, 7, 'Google最新的Gemini 1.5 Pro模型，支持2M上下文，多模态能力强', true, null, null, 'system', 'system'),
('Gemini Pro', 'gemini-pro', 1, 1, 3, '2024-02', '/v1beta/models/gemini-pro:generateContent', 1, true, true, 8, 'Google Gemini Pro模型，平衡性能和成本', true, null, null, 'system', 'system'),

-- 国产LLM
('文心一言 4.0', 'ernie-4.0-8k', 1, 1, 4, '2024-06', '/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro', 1, true, true, 9, '百度文心一言4.0模型，中文理解能力强', true, null, null, 'system', 'system'),
('通义千问 Max', 'qwen-max', 1, 1, 5, '2024-07', '/compatible-mode/v1/chat/completions', 1, true, true, 10, '阿里云通义千问Max模型，支持长文本处理', true, null, null, 'system', 'system'),
('混元大模型', 'hunyuan-pro', 1, 1, 6, '2024-08', '/v1/chat/completions', 1, true, true, 11, '腾讯云混元大模型，适合企业级应用', true, null, null, 'system', 'system'),
('GLM-4', 'glm-4', 1, 1, 7, '2024-09', '/api/paas/v4/chat/completions', 1, true, true, 12, '智谱AI GLM-4模型，支持多轮对话', true, null, null, 'system', 'system'),

-- 本地开源模型
('Llama 3.1 70B', 'llama-3.1-70b-instruct', 2, 1, 12, '2024-07', '/v1/chat/completions', 1, true, false, 13, 'Meta开源的Llama 3.1 70B模型，本地部署', true, '/data/models/llama-3.1-70b', 'gguf', 'system', 'system'),
('Qwen2.5 72B', 'qwen2.5-72b-instruct', 2, 1, 12, '2024-09', '/v1/chat/completions', 1, true, false, 14, '阿里开源的Qwen2.5 72B模型，本地部署', true, '/data/models/qwen2.5-72b', 'gguf', 'system', 'system');

-- ==================================================
-- 4. AI模型主表数据 (a_model) - 图像生成模型
-- ==================================================
INSERT INTO a_model (name, code, source, category_id, provider_id, version, api_endpoint, invoke_method, supports_stream, supports_function, sort_order, description, is_enabled, artifact_path, artifact_type, create_by, update_by) VALUES
-- OpenAI DALL-E系列
('DALL-E 3', 'dall-e-3', 1, 2, 1, '2024-01', '/v1/images/generations', 1, false, false, 1, 'OpenAI DALL-E 3图像生成模型，支持高质量图像生成', true, null, null, 'system', 'system'),
('DALL-E 2', 'dall-e-2', 1, 2, 1, '2023-11', '/v1/images/generations', 1, false, false, 2, 'OpenAI DALL-E 2图像生成模型，性价比较高', true, null, null, 'system', 'system'),

-- Stability AI系列
('Stable Diffusion XL', 'stable-diffusion-xl-1024-v1-0', 1, 2, 8, '2024-01', '/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image', 1, false, false, 3, 'Stability AI的SDXL模型，支持1024x1024高分辨率', true, null, null, 'system', 'system'),
('Stable Diffusion 3', 'stable-diffusion-3-medium', 1, 2, 8, '2024-06', '/v1/generation/stable-diffusion-3-medium/text-to-image', 1, false, false, 4, 'Stability AI最新的SD3模型，图像质量更高', true, null, null, 'system', 'system'),

-- Midjourney
('Midjourney V6', 'midjourney-v6', 1, 2, 9, '2024-03', '/api/v1/imagine', 1, false, false, 5, 'Midjourney V6模型，艺术风格图像生成专家', true, null, null, 'system', 'system'),

-- 本地部署图像模型
('Stable Diffusion WebUI', 'sd-webui-local', 2, 2, 12, '2024-05', '/sdapi/v1/txt2img', 1, false, false, 6, '本地部署的Stable Diffusion WebUI', true, '/data/models/stable-diffusion-webui', 'safetensors', 'system', 'system');

-- ==================================================
-- 5. AI模型主表数据 (a_model) - 视频生成模型
-- ==================================================
INSERT INTO a_model (name, code, source, category_id, provider_id, version, api_endpoint, invoke_method, supports_stream, supports_function, sort_order, description, is_enabled, artifact_path, artifact_type, create_by, update_by) VALUES
-- Runway系列
('Runway Gen-3', 'runway-gen3-alpha', 1, 3, 10, '2024-08', '/v1/tasks', 1, false, false, 1, 'Runway最新的Gen-3视频生成模型，支持高质量视频生成', true, null, null, 'system', 'system'),
('Runway Gen-2', 'runway-gen2', 1, 3, 10, '2024-03', '/v1/tasks', 1, false, false, 2, 'Runway Gen-2视频生成模型，稳定可靠', true, null, null, 'system', 'system'),

-- Pika Labs
('Pika 1.0', 'pika-1.0', 1, 3, 11, '2024-01', '/api/v1/generate', 1, false, false, 3, 'Pika Labs视频生成模型，支持多种风格', true, null, null, 'system', 'system'),

-- 本地视频模型
('AnimateDiff', 'animatediff-local', 2, 3, 12, '2024-04', '/api/v1/animate', 1, false, false, 4, '本地部署的AnimateDiff视频生成模型', true, '/data/models/animatediff', 'safetensors', 'system', 'system');

-- ==================================================
-- 6. AI模型主表数据 (a_model) - 多模态模型
-- ==================================================
INSERT INTO a_model (name, code, source, category_id, provider_id, version, api_endpoint, invoke_method, supports_stream, supports_function, sort_order, description, is_enabled, artifact_path, artifact_type, create_by, update_by) VALUES
-- OpenAI多模态
('GPT-4 Vision', 'gpt-4-vision-preview', 1, 4, 1, '2024-04', '/v1/chat/completions', 1, true, true, 1, 'OpenAI GPT-4 Vision模型，支持图像理解和分析', true, null, null, 'system', 'system'),

-- Google多模态
('Gemini 1.5 Pro Vision', 'gemini-1.5-pro-vision', 1, 4, 3, '2024-05', '/v1beta/models/gemini-1.5-pro-vision:generateContent', 1, true, true, 2, 'Google Gemini 1.5 Pro Vision，强大的多模态理解能力', true, null, null, 'system', 'system'),

-- Anthropic多模态
('Claude 3.5 Sonnet Vision', 'claude-3-5-sonnet-vision', 1, 4, 2, '2024-10', '/v1/messages', 1, true, true, 3, 'Anthropic Claude 3.5 Sonnet Vision，支持图像分析', true, null, null, 'system', 'system'),

-- 本地多模态
('LLaVA 1.6', 'llava-1.6-34b', 2, 4, 12, '2024-06', '/v1/chat/completions', 1, true, false, 4, '本地部署的LLaVA多模态模型', true, '/data/models/llava-1.6-34b', 'gguf', 'system', 'system');

-- ==================================================
-- 7. 模型特性数据 (a_model_feature)
-- ==================================================
-- 注意：使用模型编码关联，避免硬编码ID依赖
-- LLM模型特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'context_length', '128000', 'GPT-4 Turbo支持128K上下文长度', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'max_tokens', '4096', '最大输出token数', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'temperature_range', '{"min": 0, "max": 2}', '温度参数范围', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'languages', '["zh", "en", "ja", "ko", "fr", "de", "es", "ru"]', '支持的语言列表', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

-- Claude 3.5 Sonnet特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'context_length', '200000', 'Claude 3.5 Sonnet支持200K上下文长度', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'max_tokens', '8192', '最大输出token数', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'safety_level', 'high', '安全级别较高', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

-- Gemini 1.5 Pro特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'context_length', '2000000', 'Gemini 1.5 Pro支持2M上下文长度', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'multimodal', 'true', '支持多模态输入', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'file_upload', '["image", "video", "audio", "text"]', '支持的文件类型', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro' AND m.delete_time IS NULL;

-- DALL-E 3特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'image_sizes', '["1024x1024", "1024x1792", "1792x1024"]', 'DALL-E 3支持的图像尺寸', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-3' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'style_options', '["vivid", "natural"]', '支持的风格选项', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-3' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'quality_options', '["standard", "hd"]', '支持的质量选项', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-3' AND m.delete_time IS NULL;

-- Stable Diffusion XL特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'image_sizes', '["1024x1024", "1152x896", "896x1152", "1216x832", "832x1216"]', 'SDXL支持的图像尺寸', 'system', 'system'
FROM a_model m WHERE m.code = 'stable-diffusion-xl-1024-v1-0' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'steps_range', '{"min": 10, "max": 150}', '生成步数范围', 'system', 'system'
FROM a_model m WHERE m.code = 'stable-diffusion-xl-1024-v1-0' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'cfg_scale_range', '{"min": 1, "max": 20}', 'CFG Scale范围', 'system', 'system'
FROM a_model m WHERE m.code = 'stable-diffusion-xl-1024-v1-0' AND m.delete_time IS NULL;

-- Runway Gen-3特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'video_duration', '{"min": 4, "max": 10}', 'Runway Gen-3视频时长范围（秒）', 'system', 'system'
FROM a_model m WHERE m.code = 'runway-gen3-alpha' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'video_resolution', '["1280x768", "768x1280", "1024x576", "576x1024"]', '支持的视频分辨率', 'system', 'system'
FROM a_model m WHERE m.code = 'runway-gen3-alpha' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'frame_rate', '24', '视频帧率', 'system', 'system'
FROM a_model m WHERE m.code = 'runway-gen3-alpha' AND m.delete_time IS NULL;

-- GPT-4 Vision特性
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'vision_capabilities', '["image_analysis", "chart_reading", "ocr", "scene_understanding"]', 'GPT-4 Vision视觉能力', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-vision-preview' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'supported_formats', '["jpg", "jpeg", "png", "gif", "webp"]', '支持的图像格式', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-vision-preview' AND m.delete_time IS NULL;

INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by)
SELECT m.id, 'max_image_size', '20971520', '最大图像文件大小（字节）', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-vision-preview' AND m.delete_time IS NULL;

-- ==================================================
-- 8. 模型输出格式数据 (a_model_output_format)
-- ==================================================
-- 使用模型编码关联，避免硬编码ID依赖

-- GPT-4 Turbo输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 4096, 'GPT-4 Turbo JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 4096, 'GPT-4 Turbo文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

-- GPT-4输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 8192, 'GPT-4 JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 8192, 'GPT-4文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4' AND m.delete_time IS NULL;

-- GPT-3.5 Turbo输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 4096, 'GPT-3.5 Turbo JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-3.5-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 4096, 'GPT-3.5 Turbo文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-3.5-turbo' AND m.delete_time IS NULL;

-- Claude 3.5 Sonnet输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 8192, 'Claude 3.5 Sonnet JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 8192, 'Claude 3.5 Sonnet文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

-- Claude 3 Opus输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 4096, 'Claude 3 Opus JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-opus-20240229' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 4096, 'Claude 3 Opus文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-opus-20240229' AND m.delete_time IS NULL;

-- Gemini 1.5 Pro输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 8192, 'Gemini 1.5 Pro JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 8192, 'Gemini 1.5 Pro文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro' AND m.delete_time IS NULL;

-- 图像生成模型输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 3, false, 1, 'DALL-E 3图像输出', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-3' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 3, false, 1, 'DALL-E 2图像输出', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-2' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 3, false, 1, 'Stable Diffusion XL图像输出', 'system', 'system'
FROM a_model m WHERE m.code = 'stable-diffusion-xl-1024-v1-0' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 3, false, 1, 'Stable Diffusion 3图像输出', 'system', 'system'
FROM a_model m WHERE m.code = 'stable-diffusion-3-medium' AND m.delete_time IS NULL;

-- 视频生成模型输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 4, false, 1, 'Runway Gen-3视频输出', 'system', 'system'
FROM a_model m WHERE m.code = 'runway-gen3-alpha' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 4, false, 1, 'Runway Gen-2视频输出', 'system', 'system'
FROM a_model m WHERE m.code = 'runway-gen2' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 4, false, 1, 'Pika 1.0视频输出', 'system', 'system'
FROM a_model m WHERE m.code = 'pika-1.0' AND m.delete_time IS NULL;

-- 多模态模型输出格式
INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 4096, 'GPT-4 Vision JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-vision-preview' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 4096, 'GPT-4 Vision文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-vision-preview' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 1, true, 8192, 'Gemini 1.5 Pro Vision JSON格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro-vision' AND m.delete_time IS NULL;

INSERT INTO a_model_output_format (model_id, output_type, supports_stream, max_tokens, description, create_by, update_by)
SELECT m.id, 2, true, 8192, 'Gemini 1.5 Pro Vision文本格式输出', 'system', 'system'
FROM a_model m WHERE m.code = 'gemini-1.5-pro-vision' AND m.delete_time IS NULL;

-- ==================================================
-- 9. 模型API密钥数据 (a_model_api_key)
-- ==================================================
-- 注意：实际环境中API密钥应该加密存储，这里仅为演示
INSERT INTO a_model_api_key (provider_id, api_key, api_endpoint_override, priority, weight, quota, used_quota, currency_id, status, description, create_by, update_by) VALUES
-- OpenAI API密钥
(1, 'sk-demo1234567890abcdef1234567890abcdef', null, 1, 10, 1000.0000, 150.2500, 2, 'active', 'OpenAI主要API密钥', 'system', 'system'),
(1, 'sk-demo2234567890abcdef1234567890abcdef', null, 2, 5, 500.0000, 75.1200, 2, 'active', 'OpenAI备用API密钥', 'system', 'system'),

-- Anthropic API密钥
(2, 'sk-ant-demo1234567890abcdef1234567890abcdef', null, 1, 10, 800.0000, 120.3400, 2, 'active', 'Anthropic主要API密钥', 'system', 'system'),

-- Google API密钥
(3, 'AIzaSyDemo1234567890abcdef1234567890abcdef', null, 1, 10, 600.0000, 89.5600, 2, 'active', 'Google Gemini API密钥', 'system', 'system'),

-- 百度API密钥
(4, 'demo1234567890abcdef1234567890abcdef', 'https://aip.baidubce.com', 1, 10, 5000.0000, 1200.7800, 1, 'active', '百度文心一言API密钥', 'system', 'system'),

-- 阿里云API密钥
(5, 'sk-demo1234567890abcdef1234567890abcdef', null, 1, 10, 3000.0000, 450.2300, 1, 'active', '阿里云通义千问API密钥', 'system', 'system'),

-- 腾讯云API密钥
(6, 'demo1234567890abcdef1234567890abcdef', null, 1, 10, 2000.0000, 300.1500, 1, 'active', '腾讯云混元API密钥', 'system', 'system'),

-- 智谱AI API密钥
(7, 'demo1234567890abcdef1234567890abcdef.demo', null, 1, 10, 1500.0000, 225.4500, 1, 'active', '智谱AI GLM API密钥', 'system', 'system'),

-- Stability AI API密钥
(8, 'sk-demo1234567890abcdef1234567890abcdef', null, 1, 10, 400.0000, 60.7800, 2, 'active', 'Stability AI API密钥', 'system', 'system'),

-- Runway API密钥
(10, 'rw_demo1234567890abcdef1234567890abcdef', null, 1, 10, 300.0000, 45.2300, 2, 'active', 'Runway API密钥', 'system', 'system');

-- ==================================================
-- 10. 模型可见性控制数据 (a_model_visibility)
-- ==================================================
-- 使用模型编码关联，设置部分模型对特定会员等级可见

-- GPT-4 Turbo对VIP会员可见
INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 2, true, 'GPT-4 Turbo对VIP会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 3, true, 'GPT-4 Turbo对企业会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-4-turbo' AND m.delete_time IS NULL;

-- Claude 3.5 Sonnet对VIP会员可见
INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 2, true, 'Claude 3.5 Sonnet对VIP会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 3, true, 'Claude 3.5 Sonnet对企业会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'claude-3-5-sonnet-20241022' AND m.delete_time IS NULL;

-- 免费模型对所有会员可见
INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 1, true, 'GPT-3.5 Turbo对免费会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-3.5-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 2, true, 'GPT-3.5 Turbo对VIP会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-3.5-turbo' AND m.delete_time IS NULL;

INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 3, true, 'GPT-3.5 Turbo对企业会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'gpt-3.5-turbo' AND m.delete_time IS NULL;

-- 图像生成模型对VIP以上会员可见
INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 2, true, 'DALL-E 3对VIP会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-3' AND m.delete_time IS NULL;

INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 3, true, 'DALL-E 3对企业会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'dall-e-3' AND m.delete_time IS NULL;

-- 视频生成模型仅对企业会员可见
INSERT INTO a_model_visibility (model_id, visibility_type, reference_id, is_enabled, description, create_by, update_by)
SELECT m.id, 2, 3, true, 'Runway Gen-3仅对企业会员开放', 'system', 'system'
FROM a_model m WHERE m.code = 'runway-gen3-alpha' AND m.delete_time IS NULL;

-- ==================================================
-- 数据插入完成
-- ==================================================
