package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 会员助手使用统计视图对象
 * 用于向前端展示会员对特定助手的使用统计信息
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantUsageStatsVo {
    
    /**
     * 会员ID
     */
    private Long memberId;
    
    /**
     * 助手实例ID
     */
    private Long memberAssistantId;
    
    /**
     * 助手模板ID
     */
    private Long assistantId;
    
    /**
     * 助手名称
     */
    private String assistantName;
    
    /**
     * 总使用次数
     */
    private Long totalUsageCount;
    
    /**
     * 今日使用次数
     */
    private Long todayUsageCount;
    
    /**
     * 本周使用次数
     */
    private Long weekUsageCount;
    
    /**
     * 本月使用次数
     */
    private Long monthUsageCount;
    
    /**
     * 首次使用时间
     */
    private OffsetDateTime firstUsedTime;
    
    /**
     * 最后使用时间
     */
    private OffsetDateTime lastUsedTime;
    
    /**
     * 平均每日使用次数
     */
    private Double avgDailyUsage;
    
    /**
     * 连续使用天数
     */
    private Long consecutiveDays;
    
    /**
     * 最长连续使用天数
     */
    private Long maxConsecutiveDays;
    
    /**
     * 使用频率排名（在该会员的所有助手中）
     */
    private Integer usageRank;
    
    /**
     * 统计时间
     */
    private OffsetDateTime statsTime;
}
