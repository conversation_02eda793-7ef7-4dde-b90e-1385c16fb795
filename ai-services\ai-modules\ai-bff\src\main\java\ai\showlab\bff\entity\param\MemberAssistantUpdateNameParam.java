package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新会员助手实例名称参数
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantUpdateNameParam {
    
    /**
     * 会员助手实例ID
     */
    @NotNull(message = "会员助手实例ID不能为空")
    private Long memberAssistantId;
    
    /**
     * 新的自定义名称
     */
    @NotBlank(message = "自定义名称不能为空")
    private String customName;
}
