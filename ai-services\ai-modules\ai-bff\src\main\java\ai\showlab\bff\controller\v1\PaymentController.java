package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.constant.PaymentConstants;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.param.PaymentCallbackParam;
import ai.showlab.bff.entity.param.PaymentInitiateParam;
import ai.showlab.bff.entity.param.PaymentStatusQueryParam;
import ai.showlab.bff.entity.vo.v1.PaymentInitiateVo;
import ai.showlab.bff.entity.vo.v1.PaymentStatusVo;
import ai.showlab.bff.service.v1.payment.IPaymentService;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 支付控制器
 * <p>
 * 提供支付发起、回调处理、状态查询等API接口。
 * 支持多种支付网关，与现有的billing系统集成。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/payment")
@Tag(name = "支付管理", description = "支付相关API接口")
public class PaymentController extends BaseController {

    @Autowired
    private IPaymentService paymentService;

    /**
     * 发起支付
     * <p>
     * 基于已创建的订单发起支付，返回支付所需的信息。
     * 支持多种支付方式：重定向、二维码、表单提交、JSAPI等。
     * </p>
     *
     * @param requestParams 支付发起参数
     * @return 支付发起结果
     */
    @PostMapping("/initiate")
    @Operation(summary = "发起支付", description = "基于订单发起支付，返回支付URL、二维码等信息")
    @ApiAuth
    @ApiParamValidate(bizParamClass = PaymentInitiateParam.class)
    public ResponseEntity<RestResult> initiatePayment(RequestParams<PaymentInitiateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            PaymentInitiateVo result = paymentService.initiatePayment(requestParams);
            return RestResult.ok("支付发起成功", result);
        }, "支付发起失败，请稍后重试");
    }

    /**
     * 查询支付状态
     * <p>
     * 查询订单的支付状态，可选择是否强制从支付网关查询最新状态。
     * 默认优先使用缓存数据，提高查询性能。
     * </p>
     *
     * @param requestParams 支付状态查询参数
     * @return 支付状态信息
     */
    @PostMapping("/status")
    @Operation(summary = "查询支付状态", description = "查询订单的支付状态")
    @ApiAuth
    @ApiParamValidate(bizParamClass = PaymentStatusQueryParam.class)
    public ResponseEntity<RestResult> queryPaymentStatus(RequestParams<PaymentStatusQueryParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            PaymentStatusVo result = paymentService.queryPaymentStatus(requestParams);
            return RestResult.ok("查询成功", result);
        }, "查询支付状态失败，请稍后重试");
    }

    /**
     * 取消支付
     * <p>
     * 取消未支付的订单，释放冻结资源。
     * 只能取消状态为"待支付"的订单。
     * </p>
     *
     * @param orderNo 订单号
     * @return 取消结果
     */
    @PostMapping("/cancel/{orderNo}")
    @Operation(summary = "取消支付", description = "取消未支付的订单")
    @ApiAuth
    public ResponseEntity<RestResult> cancelPayment(
            @Parameter(description = "订单号", required = true)
            @PathVariable String orderNo) {
        return executeWithTryCatch(() -> {
            Long currentMemberId = BffKit.getCurrentMemberId();
            boolean result = paymentService.cancelPayment(orderNo, currentMemberId);
            if (result) {
                return RestResult.ok("支付取消成功");
            }
            throw new BusinessException("支付取消失败");
        }, "支付取消失败，请稍后重试");
    }

    /**
     * 支付回调接口（支付宝）
     * <p>
     * 处理支付宝的异步通知回调。
     * 此接口不需要认证，由支付网关直接调用。
     * </p>
     *
     * @param request HTTP请求对象
     * @return 处理结果，返回"success"表示处理成功
     */
    @PostMapping("/callback/alipay")
    @Operation(summary = "支付宝回调", description = "处理支付宝异步通知")
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    public String alipayCallback(HttpServletRequest request) {
        try {
            // 构建回调参数
            PaymentCallbackParam callbackParam = buildCallbackParam(request, "alipay");
            // 处理回调
            boolean result = paymentService.handlePaymentCallback(callbackParam);
            if (result) {
                log.info("支付宝回调处理成功，订单号: {}", callbackParam.getOrderNo());
                return "success";
            }
            log.error("支付宝回调处理失败，订单号: {}", callbackParam.getOrderNo());
            return "fail";
        } catch (Exception e) {
            log.error("支付宝回调处理异常", e);
            return "fail";
        }
    }

    /**
     * 支付回调接口（微信支付）
     * <p>
     * 处理微信支付的异步通知回调。
     * 此接口不需要认证，由支付网关直接调用。
     * </p>
     *
     * @param request HTTP请求对象
     * @return 处理结果，返回XML格式的响应
     */
    @PostMapping("/callback/wechat")
    @Operation(summary = "微信支付回调", description = "处理微信支付异步通知")
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    public String wechatCallback(HttpServletRequest request) {
        try {
            // 构建回调参数
            PaymentCallbackParam callbackParam = buildCallbackParam(request, "wechat_pay");
            // 处理回调
            boolean result = paymentService.handlePaymentCallback(callbackParam);
            if (result) {
                log.info("微信支付回调处理成功，订单号: {}", callbackParam.getOrderNo());
                return PaymentConstants.WECHAT_CALLBACK_OK;
            } else {
                log.error("微信支付回调处理失败，订单号: {}", callbackParam.getOrderNo());
                return PaymentConstants.WECHAT_CALLBACK_FAIL;
            }
        } catch (Exception e) {
            log.error("微信支付回调处理异常", e);
            return PaymentConstants.WECHAT_CALLBACK_EXCEPTION;
        }
    }

    /**
     * 支付回调接口（PayPal）
     * <p>
     * 处理PayPal的异步通知回调。
     * 此接口不需要认证，由支付网关直接调用。
     * </p>
     *
     * @param request HTTP请求对象
     * @return 处理结果
     */
    @PostMapping("/callback/paypal")
    @Operation(summary = "PayPal回调", description = "处理PayPal异步通知")
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    public ResponseEntity<RestResult> paypalCallback(HttpServletRequest request) {
        try {
            // 构建回调参数
            PaymentCallbackParam callbackParam = buildCallbackParam(request, "paypal");

            // 处理回调
            boolean result = paymentService.handlePaymentCallback(callbackParam);

            if (result) {
                log.info("PayPal回调处理成功，订单号: {}", callbackParam.getOrderNo());
                return ResponseEntity.ok(RestResult.ok("处理成功"));
            } else {
                log.error("PayPal回调处理失败，订单号: {}", callbackParam.getOrderNo());
                return ResponseEntity.ok(RestResult.error("处理失败"));
            }
        } catch (Exception e) {
            log.error("PayPal回调处理异常", e);
            return ResponseEntity.ok(RestResult.error("系统异常"));
        }
    }


    /**
     * 构建支付回调参数
     *
     * @param request     HTTP请求
     * @param gatewayCode 支付网关编码
     * @return 回调参数
     */
    private PaymentCallbackParam buildCallbackParam(HttpServletRequest request, String gatewayCode) {
        PaymentCallbackParam param = new PaymentCallbackParam();

        // TODO: 根据不同支付网关解析回调参数
        // 这里是示例实现，实际需要根据各支付网关的回调格式进行解析

        // 获取所有请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();

        // 根据支付网关类型解析参数
        switch (gatewayCode) {
            case "alipay":
                parseAlipayCallback(param, parameterMap);
                break;
            case "wechat_pay":
                parseWechatCallback(param, request);
                break;
            case "paypal":
                parsePaypalCallback(param, parameterMap);
                break;
            default:
                throw new BusinessException("不支持的支付网关: " + gatewayCode);
        }

        return param;
    }

    /**
     * 解析支付宝回调参数
     */
    private void parseAlipayCallback(PaymentCallbackParam param, Map<String, String[]> parameterMap) {
        // TODO: 实现支付宝回调参数解析
        // 示例实现
        param.setOrderNo(getParameterValue(parameterMap, "out_trade_no"));
        param.setGatewayTransactionId(getParameterValue(parameterMap, "trade_no"));
        param.setPaymentStatus("success".equals(getParameterValue(parameterMap, "trade_status")) ? "success" : "failed");
    }

    /**
     * 解析微信支付回调参数
     */
    private void parseWechatCallback(PaymentCallbackParam param, HttpServletRequest request) {
        // TODO: 实现微信支付回调参数解析（需要解析XML）
        // 示例实现
        param.setOrderNo("example_order_no");
        param.setGatewayTransactionId("example_transaction_id");
        param.setPaymentStatus("success");
    }

    /**
     * 解析PayPal回调参数
     */
    private void parsePaypalCallback(PaymentCallbackParam param, Map<String, String[]> parameterMap) {
        // TODO: 实现PayPal回调参数解析
        // 示例实现
        param.setOrderNo(getParameterValue(parameterMap, "custom"));
        param.setGatewayTransactionId(getParameterValue(parameterMap, "txn_id"));
        param.setPaymentStatus("Completed".equals(getParameterValue(parameterMap, "payment_status")) ? "success" : "failed");
    }

    /**
     * 获取参数值
     */
    private String getParameterValue(Map<String, String[]> parameterMap, String key) {
        String[] values = parameterMap.get(key);
        return values != null && values.length > 0 ? values[0] : null;
    }
}
