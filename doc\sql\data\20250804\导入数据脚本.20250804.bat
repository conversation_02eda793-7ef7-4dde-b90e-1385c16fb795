@echo off
REM ====================================================================================
REM AIShowLab - AI助手和计费模块数据导入脚本 (Windows版本)
REM 文件名: 导入数据脚本.20250804.bat
REM 作者: AI Assistant
REM 描述: 用于导入AI助手和计费与消耗模块模拟数据的Windows批处理脚本
REM 创建时间: 2025-08-04
REM ====================================================================================

setlocal enabledelayedexpansion

REM 默认配置
set DEFAULT_DB_HOST=localhost
set DEFAULT_DB_PORT=5432
set DEFAULT_DB_NAME=aishowlab
set DEFAULT_DB_USER=postgres

REM 设置默认值
set DB_HOST=%DEFAULT_DB_HOST%
set DB_PORT=%DEFAULT_DB_PORT%
set DB_NAME=%DEFAULT_DB_NAME%
set DB_USER=%DEFAULT_DB_USER%
set DB_PASSWORD=
set DRY_RUN=false
set ASSISTANT_ONLY=false
set BILLING_ONLY=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-h" (
    set DB_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--host" (
    set DB_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-p" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set DB_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--database" (
    set DB_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-u" (
    set DB_USER=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--user" (
    set DB_USER=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-w" (
    set DB_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--password" (
    set DB_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--assistant-only" (
    set ASSISTANT_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--billing-only" (
    set BILLING_ONLY=true
    shift
    goto :parse_args
)
if "%~1"=="--dry-run" (
    set DRY_RUN=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
echo 未知参数: %~1
goto :show_help

:args_done

REM 检查互斥参数
if "%ASSISTANT_ONLY%"=="true" if "%BILLING_ONLY%"=="true" (
    echo [错误] --assistant-only 和 --billing-only 不能同时使用
    exit /b 1
)

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set ASSISTANT_SQL=%SCRIPT_DIR%AI助手模拟数据.20250804.sql
set BILLING_SQL=%SCRIPT_DIR%计费与消耗模拟数据.20250804.sql
set VALIDATION_SQL=%SCRIPT_DIR%数据验证脚本.20250804.sql

REM 检查SQL文件是否存在
if not "%BILLING_ONLY%"=="true" (
    if not exist "%ASSISTANT_SQL%" (
        echo [错误] 找不到AI助手数据文件: %ASSISTANT_SQL%
        exit /b 1
    )
)

if not "%ASSISTANT_ONLY%"=="true" (
    if not exist "%BILLING_SQL%" (
        echo [错误] 找不到计费数据文件: %BILLING_SQL%
        exit /b 1
    )
)

REM 构建psql连接字符串
set PSQL_CONN=postgresql://%DB_USER%
if not "%DB_PASSWORD%"=="" (
    set PSQL_CONN=!PSQL_CONN!:%DB_PASSWORD%
)
set PSQL_CONN=!PSQL_CONN!@%DB_HOST%:%DB_PORT%/%DB_NAME%

echo ====================================================
echo AIShowLab AI助手和计费模块数据导入脚本
echo ====================================================
echo 数据库连接信息:
echo   主机: %DB_HOST%
echo   端口: %DB_PORT%
echo   数据库: %DB_NAME%
echo   用户: %DB_USER%

if "%DRY_RUN%"=="true" (
    echo   模式: 仅验证SQL语法（不执行导入）
) else if "%ASSISTANT_ONLY%"=="true" (
    echo   模式: 仅导入AI助手模块数据
    echo   SQL文件: %ASSISTANT_SQL%
) else if "%BILLING_ONLY%"=="true" (
    echo   模式: 仅导入计费与消耗模块数据
    echo   SQL文件: %BILLING_SQL%
) else (
    echo   模式: 导入所有模块数据
    echo   AI助手文件: %ASSISTANT_SQL%
    echo   计费文件: %BILLING_SQL%
)

echo ====================================================

REM 验证数据库连接
echo 正在验证数据库连接...
psql "%PSQL_CONN%" -c "SELECT version();" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法连接到数据库
    echo 请检查连接参数或确保数据库服务正在运行
    exit /b 1
)
echo [成功] 数据库连接成功

REM 检查必要的表是否存在
echo 正在检查数据库表结构...

REM AI助手相关表
if not "%BILLING_ONLY%"=="true" (
    set ASSISTANT_TABLES=a_assistant_category a_assistant a_assistant_param a_member_assistant a_assistant_favorite
    for %%t in (!ASSISTANT_TABLES!) do (
        psql "%PSQL_CONN%" -c "SELECT 1 FROM %%t LIMIT 1;" >nul 2>&1
        if errorlevel 1 (
            echo [错误] AI助手表 %%t 不存在
            echo 请先执行数据库架构脚本创建表结构
            exit /b 1
        )
    )
    echo [成功] AI助手相关表都存在
)

REM 计费相关表
if not "%ASSISTANT_ONLY%"=="true" (
    set BILLING_TABLES=a_billing_plan a_billing_package a_billing_price a_billing_balance a_billing_usage a_billing_transaction a_billing_order
    for %%t in (!BILLING_TABLES!) do (
        psql "%PSQL_CONN%" -c "SELECT 1 FROM %%t LIMIT 1;" >nul 2>&1
        if errorlevel 1 (
            echo [错误] 计费表 %%t 不存在
            echo 请先执行数据库架构脚本创建表结构
            exit /b 1
        )
    )
    echo [成功] 计费相关表都存在
)

if "%DRY_RUN%"=="true" (
    REM 仅验证SQL语法
    echo 正在验证SQL语法...
    
    if not "%BILLING_ONLY%"=="true" (
        psql "%PSQL_CONN%" --single-transaction --set ON_ERROR_STOP=on -f "%ASSISTANT_SQL%" --echo-errors >nul 2>&1
        if errorlevel 1 (
            echo [失败] AI助手数据SQL语法验证失败
            exit /b 1
        )
        echo [成功] AI助手数据SQL语法验证通过
    )
    
    if not "%ASSISTANT_ONLY%"=="true" (
        psql "%PSQL_CONN%" --single-transaction --set ON_ERROR_STOP=on -f "%BILLING_SQL%" --echo-errors >nul 2>&1
        if errorlevel 1 (
            echo [失败] 计费数据SQL语法验证失败
            exit /b 1
        )
        echo [成功] 计费数据SQL语法验证通过
    )
) else (
    REM 执行数据导入
    if not "%BILLING_ONLY%"=="true" (
        echo 正在导入AI助手模块数据...
        psql "%PSQL_CONN%" -f "%ASSISTANT_SQL%" --single-transaction --set ON_ERROR_STOP=on
        if errorlevel 1 (
            echo [失败] AI助手数据导入失败
            exit /b 1
        ) else (
            echo [成功] AI助手数据导入成功
        )
    )
    
    if not "%ASSISTANT_ONLY%"=="true" (
        echo 正在导入计费与消耗模块数据...
        psql "%PSQL_CONN%" -f "%BILLING_SQL%" --single-transaction --set ON_ERROR_STOP=on
        if errorlevel 1 (
            echo [失败] 计费数据导入失败
            exit /b 1
        ) else (
            echo [成功] 计费数据导入成功
        )
    )
    
    REM 执行验证脚本（如果存在）
    if exist "%VALIDATION_SQL%" (
        echo 正在执行数据验证...
        for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
        set "timestamp=%dt:~0,8%_%dt:~8,6%"
        psql "%PSQL_CONN%" -f "%VALIDATION_SQL%" > "%TEMP%\validation_result_!timestamp!.txt" 2>&1
        echo [成功] 数据验证完成，结果已保存到 %TEMP%\validation_result_!timestamp!.txt
    )
)

echo ====================================================
echo 操作完成！

if not "%DRY_RUN%"=="true" (
    echo.
    if not "%BILLING_ONLY%"=="true" (
        echo AI助手模块导入的数据包括:
        echo   - 6个助手分类（含二级分类）
        echo   - 10个AI助手定义
        echo   - 助手参数配置
        echo   - 会员助手实例
        echo   - 助手收藏记录
        echo.
    )
    
    if not "%ASSISTANT_ONLY%"=="true" (
        echo 计费模块导入的数据包括:
        echo   - 6个计费方案
        echo   - 12个计费套餐
        echo   - 模型价格配置
        echo   - 会员余额记录
        echo   - 使用记录和交易流水
        echo   - 订单数据
        echo.
    )
    
    echo 建议执行验证脚本检查数据完整性:
    echo   psql -d %DB_NAME% -f "%VALIDATION_SQL%"
)

echo ====================================================
goto :eof

:show_help
echo AIShowLab AI助手和计费模块数据导入脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -h, --host HOST        数据库主机地址 (默认: %DEFAULT_DB_HOST%)
echo   -p, --port PORT        数据库端口 (默认: %DEFAULT_DB_PORT%)
echo   -d, --database DB      数据库名称 (默认: %DEFAULT_DB_NAME%)
echo   -u, --user USER        数据库用户名 (默认: %DEFAULT_DB_USER%)
echo   -w, --password PASS    数据库密码 (可选，建议使用 .pgpass 文件)
echo   --assistant-only       仅导入AI助手模块数据
echo   --billing-only         仅导入计费与消耗模块数据
echo   --dry-run              仅验证SQL语法，不执行导入
echo   --help                 显示此帮助信息
echo.
echo 示例:
echo   %~nx0 -h localhost -d aishowlab -u postgres
echo   %~nx0 --assistant-only    # 仅导入AI助手数据
echo   %~nx0 --billing-only      # 仅导入计费数据
echo   %~nx0 --dry-run           # 仅验证SQL语法
echo.
exit /b 0
