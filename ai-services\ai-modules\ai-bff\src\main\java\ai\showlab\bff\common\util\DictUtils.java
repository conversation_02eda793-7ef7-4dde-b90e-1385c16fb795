package ai.showlab.bff.common.util;

import ai.showlab.bff.entity.domain.v1.sys.SysDictData;
import ai.showlab.bff.service.v1.sys.ISysDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 数据字典工具类
 * <p>
 * 提供数据字典值转换功能，将字典值转换为显示标签。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DictUtils {

    @Autowired
    private ISysDictService sysDictService;

    /**
     * 根据字典类型和字典值获取字典标签
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签，如果未找到则返回原值的字符串形式
     */
    public String getDictLabel(String dictType, Object dictValue) {
        if (dictType == null || dictValue == null) {
            return dictValue != null ? dictValue.toString() : "";
        }

        try {
            List<SysDictData> dictDataList = sysDictService.getDictData(dictType);
            if (CollectionUtils.isEmpty(dictDataList)) {
                log.warn("字典类型 {} 没有找到数据", dictType);
                return dictValue.toString();
            }

            String valueStr = dictValue.toString();
            for (SysDictData dictData : dictDataList) {
                if (valueStr.equals(dictData.getDictValue())) {
                    return dictData.getDictLabel();
                }
            }

            log.debug("字典类型 {} 中未找到值 {} 对应的标签", dictType, dictValue);
            return dictValue.toString();
        } catch (Exception e) {
            log.error("获取字典标签失败，dictType: {}, dictValue: {}", dictType, dictValue, e);
            return dictValue.toString();
        }
    }

    /**
     * 获取助手交互模式显示名称
     *
     * @param interactionMode 交互模式值
     * @return 交互模式显示名称
     */
    public String getInteractionModeName(Integer interactionMode) {
        return getDictLabel("assistant_interaction_mode", interactionMode);
    }

    /**
     * 获取助手状态显示名称
     *
     * @param status 状态值
     * @return 状态显示名称
     */
    public String getAssistantStatusName(Integer status) {
        return getDictLabel("assistant_status", status);
    }

    /**
     * 批量设置交互模式名称
     * <p>
     * 为对象列表批量设置交互模式显示名称。
     * 使用反射调用setInteractionModeName方法。
     * </p>
     *
     * @param list 对象列表
     * @param <T> 对象类型
     */
    public <T> void setInteractionModeNames(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        try {
            // 预加载字典数据，避免在循环中重复查询
            List<SysDictData> dictDataList = sysDictService.getDictData("assistant_interaction_mode");
            
            for (T item : list) {
                setInteractionModeName(item, dictDataList);
            }
        } catch (Exception e) {
            log.error("批量设置交互模式名称失败", e);
        }
    }

    /**
     * 批量设置助手状态名称
     * <p>
     * 为对象列表批量设置助手状态显示名称。
     * 使用反射调用setStatusName方法。
     * </p>
     *
     * @param list 对象列表
     * @param <T> 对象类型
     */
    public <T> void setAssistantStatusNames(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        try {
            // 预加载字典数据，避免在循环中重复查询
            List<SysDictData> dictDataList = sysDictService.getDictData("assistant_status");
            
            for (T item : list) {
                setAssistantStatusName(item, dictDataList);
            }
        } catch (Exception e) {
            log.error("批量设置助手状态名称失败", e);
        }
    }

    /**
     * 为单个对象设置交互模式名称
     *
     * @param item 对象
     * @param dictDataList 字典数据列表
     * @param <T> 对象类型
     */
    private <T> void setInteractionModeName(T item, List<SysDictData> dictDataList) {
        try {
            // 使用反射获取interactionMode值
            java.lang.reflect.Method getMethod = item.getClass().getMethod("getInteractionMode");
            Object interactionMode = getMethod.invoke(item);
            
            if (interactionMode != null) {
                String modeName = findDictLabel(dictDataList, interactionMode.toString());
                
                // 使用反射设置interactionModeName值
                try {
                    java.lang.reflect.Method setMethod = item.getClass().getMethod("setInteractionModeName", String.class);
                    setMethod.invoke(item, modeName);
                } catch (NoSuchMethodException e) {
                    // 如果没有setInteractionModeName方法，忽略
                    log.debug("对象 {} 没有setInteractionModeName方法", item.getClass().getSimpleName());
                }
            }
        } catch (Exception e) {
            log.debug("设置交互模式名称失败", e);
        }
    }

    /**
     * 为单个对象设置助手状态名称
     *
     * @param item 对象
     * @param dictDataList 字典数据列表
     * @param <T> 对象类型
     */
    private <T> void setAssistantStatusName(T item, List<SysDictData> dictDataList) {
        try {
            // 使用反射获取status值
            java.lang.reflect.Method getMethod = item.getClass().getMethod("getStatus");
            Object status = getMethod.invoke(item);
            
            if (status != null) {
                String statusName = findDictLabel(dictDataList, status.toString());
                
                // 使用反射设置statusName值
                try {
                    java.lang.reflect.Method setMethod = item.getClass().getMethod("setStatusName", String.class);
                    setMethod.invoke(item, statusName);
                } catch (NoSuchMethodException e) {
                    // 如果没有setStatusName方法，忽略
                    log.debug("对象 {} 没有setStatusName方法", item.getClass().getSimpleName());
                }
            }
        } catch (Exception e) {
            log.debug("设置助手状态名称失败", e);
        }
    }

    /**
     * 在字典数据列表中查找指定值对应的标签
     *
     * @param dictDataList 字典数据列表
     * @param dictValue 字典值
     * @return 字典标签
     */
    private String findDictLabel(List<SysDictData> dictDataList, String dictValue) {
        if (CollectionUtils.isEmpty(dictDataList) || dictValue == null) {
            return dictValue;
        }

        for (SysDictData dictData : dictDataList) {
            if (dictValue.equals(dictData.getDictValue())) {
                return dictData.getDictLabel();
            }
        }

        return dictValue;
    }
}
