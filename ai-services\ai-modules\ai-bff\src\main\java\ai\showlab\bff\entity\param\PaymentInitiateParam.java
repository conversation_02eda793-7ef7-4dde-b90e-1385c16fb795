package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 支付发起参数
 * 
 * <AUTHOR>
 */
@Data
public class PaymentInitiateParam {
    
    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    
    /**
     * 支付网关ID
     */
    @NotNull(message = "支付网关ID不能为空")
    private Long paymentGatewayId;
    
    /**
     * 支付成功后的回调地址（可选，使用默认配置）
     */
    private String returnUrl;
    
    /**
     * 支付取消后的回调地址（可选，使用默认配置）
     */
    private String cancelUrl;
    
    /**
     * 客户端IP地址（用于风控）
     */
    private String clientIp;
    
    /**
     * 客户端类型 (web, mobile, app)
     */
    private String clientType = "web";
}
