package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 性别枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GenderEnum {
    
    /** 未知 */
    UNKNOWN(0, "未知"),
    
    /** 男 */
    MALE(1, "男"),
    
    /** 女 */
    FEMALE(2, "女");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static GenderEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GenderEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 