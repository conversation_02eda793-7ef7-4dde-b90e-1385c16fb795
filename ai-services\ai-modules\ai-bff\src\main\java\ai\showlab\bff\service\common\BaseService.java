package ai.showlab.bff.service.common;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.bo.LoginMember;
import ai.showlab.common.core.constant.CodeConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 服务层基本方法
 * <AUTHOR>
 */
public class BaseService {
    private static final Logger log = LoggerFactory.getLogger(BaseService.class);

    /**
     * 从 Spring Security 上下文获取当前会员信息
     *
     * @return LoginMember
     */
    public static LoginMember getLoginMember() {
        return BffKit.getLoginMember();
    }

    /**
     * 获取当前登录会员ID
     *
     * @return 会员ID
     */
    public static Long getCurrentMemberId() {
        return BffKit.getCurrentMemberId();
    }

    /**
     * Service层异常处理工具方法
     * <p>
     * 用于Service层方法中统一处理异常，将各种异常转换为BusinessException。
     * 这样可以让Service层方法只需要声明throws BusinessException。
     * </p>
     *
     * @param operation 业务操作
     * @param errorMsg  发生异常时的错误信息
     * @param <T>       返回类型
     * @return 业务操作结果
     * @throws BusinessException 统一的业务异常
     */
    public static <T> T executeServiceOperation(BaseController.ServiceOperation<T> operation, String errorMsg) throws BusinessException {
        try {
            return operation.execute();
        } catch (BusinessException e) {
            // 直接重新抛出业务异常
            throw e;
        } catch (Exception e) {
            // 将其他异常包装为业务异常
            log.error(errorMsg, e);
            throw new BusinessException(CodeConstants.SERVER_ERROR, errorMsg);
        }
    }
}
