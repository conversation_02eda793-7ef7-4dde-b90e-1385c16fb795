package ai.showlab.common.core.constant;

/**
 * 业务状态码常量
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
public class CodeConstants {

    // ========== 通用状态码 ==========
    // 成功
    public static final int SUCCESS = 200;
    // 客户端错误，请求参数有问题
    public static final int ERROR = 400;
    // 未认证（未登录）
    public static final int UNAUTHORIZED = 401;
    // 已认证，但无权限
    public static final int FORBIDDEN = 403;
    // 请求资源不存在
    public static final int NOT_FOUND = 404;
    // 服务端异常
    public static final int SERVER_ERROR = 500;

    // ========== 客户端错误 (4xxxx) ==========
    // 请求方法不被允许
    public static final int METHOD_NOT_ALLOWED = 40005;
    // 请求超时
    public static final int REQUEST_TIMEOUT = 40008;
    // 请求冲突（例如，资源已存在）
    public static final int CONFLICT = 40009;
    // 请求过于频繁
    public static final int TOO_MANY_REQUESTS = 40029;

    // ========== 服务端错误 (5xxxx) ==========
    // 功能未实现
    public static final int NOT_IMPLEMENTED = 50001;
    // 网关错误
    public static final int BAD_GATEWAY = 50002;
    // 服务不可用
    public static final int SERVICE_UNAVAILABLE = 50003;
    // 网关超时
    public static final int GATEWAY_TIMEOUT = 50004;

    // ========== 业务特定状态码 (6xxxx) ==========

    // --- 用户账户相关 ---
    // 用户不存在
    public static final int USER_NOT_FOUND = 60001;
    // 用户名或密码错误
    public static final int USER_PASSWORD_ERROR = 60002;
    // 账号已被禁用
    public static final int USER_ACCOUNT_DISABLED = 60003;
    // 账号已过期
    public static final int USER_ACCOUNT_EXPIRED = 60004;
    // 账号已被锁定
    public static final int USER_ACCOUNT_LOCKED = 60005;
    // 用户已存在
    public static final int USER_ALREADY_EXISTS = 60006;

    // --- Token 相关 ---
    // 无效的Token
    public static final int TOKEN_INVALID = 60101;
    // Token已过期
    public static final int TOKEN_EXPIRED = 60102;
    // Token签名错误
    public static final int TOKEN_SIGNATURE_ERROR = 60103;

    // --- AI助手 相关 ---
    // 已经添加过该助手
    public static final int ASSISTANT_EXIST = 60201;

    // --- 其他业务 ---
    // 可根据具体业务场景继续扩展，例如订单、支付、库存等
}
