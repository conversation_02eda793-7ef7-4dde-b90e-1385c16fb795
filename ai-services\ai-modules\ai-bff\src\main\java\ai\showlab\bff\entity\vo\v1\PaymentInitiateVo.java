package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Map;

/**
 * 支付发起结果视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "orderNo")
public class PaymentInitiateVo {

    /**
     * 订单号
     */
    private String orderNo;
    
    /**
     * 支付网关名称
     */
    private String gatewayName;
    
    /**
     * 支付网关编码
     */
    private String gatewayCode;
    
    /**
     * 支付金额
     */
    private BigDecimal amount;
    
    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;
    
    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;
    
    /**
     * 支付跳转URL（用于重定向到支付页面）
     */
    private String paymentUrl;
    
    /**
     * 支付二维码内容（如果支持扫码支付）
     */
    private String qrCodeContent;
    
    /**
     * 支付表单数据（如果需要POST提交）
     */
    private Map<String, String> formData;
    
    /**
     * 支付方式类型 (redirect, qrcode, form, jsapi)
     */
    private String paymentType;
    
    /**
     * 支付超时时间
     */
    private OffsetDateTime expireTime;
    
    /**
     * 客户端需要的额外参数（如微信JSAPI所需的参数）
     */
    private Map<String, Object> clientParams;
    
    /**
     * 支付说明
     */
    private String paymentDescription;
}
