package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Map;

/**
 * 支付回调参数
 * 
 * <AUTHOR>
 */
@Data
public class PaymentCallbackParam {
    
    /**
     * 支付网关ID
     */
    @NotNull(message = "支付网关ID不能为空")
    private Long paymentGatewayId;
    
    /**
     * 业务订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;
    
    /**
     * 支付网关交易流水号
     */
    @NotBlank(message = "网关交易流水号不能为空")
    private String gatewayTransactionId;
    
    /**
     * 支付状态 (success, failed, cancelled)
     */
    @NotBlank(message = "支付状态不能为空")
    private String paymentStatus;
    
    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;
    
    /**
     * 支付货币代码
     */
    private String currencyCode;
    
    /**
     * 支付完成时间
     */
    private OffsetDateTime paidTime;
    
    /**
     * 支付失败原因（如果失败）
     */
    private String failureReason;
    
    /**
     * 网关原始回调数据（用于验签和调试）
     */
    private Map<String, Object> rawCallbackData;
    
    /**
     * 签名（用于验证回调真实性）
     */
    private String signature;
}
