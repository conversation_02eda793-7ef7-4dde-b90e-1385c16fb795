package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

/**
 * 模型特性视图对象
 * 用于向前端展示模型特性信息
 * 
 * <AUTHOR>
 */
@Data
public class ModelFeatureVo {
    
    /**
     * 特性ID
     */
    private Long id;
    
    /**
     * 特性键，如 context_length、multilingual
     */
    private String key;
    
    /**
     * 特性值，使用 Object 以支持复杂结构
     */
    private Object value;
    
    /**
     * 特性说明
     */
    private String description;
}
