import request from '@/utils/request'

// 查询模型价格列表
export function listBillingPrice(query) {
  return request({
    url: '/system/billing/price/list',
    method: 'get',
    params: query
  })
}

// 查询模型价格详细
export function getBillingPrice(id) {
  return request({
    url: '/system/billing/price/' + id,
    method: 'get'
  })
}

// 新增模型价格
export function addBillingPrice(data) {
  return request({
    url: '/system/billing/price',
    method: 'post',
    data: data
  })
}

// 修改模型价格
export function updateBillingPrice(data) {
  return request({
    url: '/system/billing/price',
    method: 'put',
    data: data
  })
}

// 删除模型价格
export function delBillingPrice(id) {
  return request({
    url: '/system/billing/price/' + id,
    method: 'delete'
  })
}
