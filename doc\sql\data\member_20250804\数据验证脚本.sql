-- ====================================================================================
-- AIShowLab - 会员功能和前端菜单权限模块数据验证脚本
-- 文件名: 数据验证脚本.sql
-- 作者: AI Assistant
-- 描述: 验证会员功能和前端菜单权限模块模拟数据的完整性和正确性
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. 会员功能模块数据统计
-- ==================================================

-- 1.1 会员类型分布
SELECT 
    '会员类型分布' as 检查项目,
    CASE m.member_type
        WHEN 1 THEN '普通会员'
        WHEN 2 THEN '管理员'
        WHEN 3 THEN '访客'
        ELSE '未知'
    END as 会员类型,
    COUNT(*) as 数量,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as 占比
FROM a_member m
WHERE m.delete_time IS NULL
GROUP BY m.member_type
ORDER BY m.member_type;

-- 1.2 会员等级分布
SELECT 
    '会员等级分布' as 检查项目,
    CASE m.member_level
        WHEN 1 THEN '免费会员'
        WHEN 2 THEN 'VIP会员'
        WHEN 3 THEN '企业会员'
        ELSE '未知'
    END as 会员等级,
    COUNT(*) as 数量,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as 占比
FROM a_member m
WHERE m.delete_time IS NULL
GROUP BY m.member_level
ORDER BY m.member_level;

-- 1.3 会员状态分布
SELECT 
    '会员状态分布' as 检查项目,
    CASE m.status
        WHEN 1 THEN '正常'
        WHEN 2 THEN '禁用'
        WHEN 3 THEN '待激活'
        ELSE '未知'
    END as 状态,
    COUNT(*) as 数量,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as 占比
FROM a_member m
WHERE m.delete_time IS NULL
GROUP BY m.status
ORDER BY m.status;

-- 1.4 认证方式统计
SELECT 
    '认证方式统计' as 检查项目,
    CASE ma.auth_type
        WHEN 1 THEN '用户名密码'
        WHEN 2 THEN '手机号'
        WHEN 3 THEN '邮箱'
        WHEN 4 THEN '微信'
        WHEN 5 THEN 'GitHub'
        ELSE '未知'
    END as 认证方式,
    COUNT(*) as 数量,
    COUNT(CASE WHEN ma.is_verified THEN 1 END) as 已验证数量,
    ROUND(COUNT(CASE WHEN ma.is_verified THEN 1 END) * 100.0 / COUNT(*), 2) as 验证率
FROM a_member_auth ma
WHERE ma.delete_time IS NULL
GROUP BY ma.auth_type
ORDER BY ma.auth_type;

-- 1.5 会话设备类型统计
SELECT 
    '会话设备类型' as 检查项目,
    CASE ms.device_type
        WHEN 1 THEN 'PC端'
        WHEN 2 THEN '移动端'
        WHEN 3 THEN '平板端'
        ELSE '未知'
    END as 设备类型,
    COUNT(*) as 会话数量,
    COUNT(CASE WHEN ms.is_active THEN 1 END) as 活跃会话,
    ROUND(COUNT(CASE WHEN ms.is_active THEN 1 END) * 100.0 / COUNT(*), 2) as 活跃率
FROM a_member_session ms
WHERE ms.delete_time IS NULL
GROUP BY ms.device_type
ORDER BY ms.device_type;

-- 1.6 邀请关系统计
SELECT 
    '邀请关系统计' as 检查项目,
    '邀请人数' as 统计类型,
    COUNT(DISTINCT m.inviter_id) as 邀请人数,
    COUNT(*) as 被邀请人数,
    ROUND(COUNT(*) * 1.0 / COUNT(DISTINCT m.inviter_id), 2) as 平均邀请数
FROM a_member m
WHERE m.delete_time IS NULL AND m.inviter_id IS NOT NULL;

-- ==================================================
-- 2. 前端菜单权限模块数据统计
-- ==================================================

-- 2.1 菜单层级统计
SELECT
    '菜单层级统计' as 检查项目,
    CASE
        WHEN fm.pid IS NULL THEN '一级菜单'
        ELSE '二级菜单'
    END as 菜单层级,
    COUNT(*) as 数量,
    COUNT(CASE WHEN fm.status = 1 THEN 1 END) as 启用数量,
    COUNT(CASE WHEN fm.status = 0 THEN 1 END) as 隐藏数量
FROM a_func_menu fm
GROUP BY (fm.pid IS NULL)
ORDER BY (fm.pid IS NULL) DESC;

-- 2.2 权限类型分布
SELECT 
    '权限类型分布' as 检查项目,
    CASE fp.type
        WHEN 1 THEN '页面权限'
        WHEN 2 THEN '功能权限'
        WHEN 3 THEN '数据权限'
        ELSE '未知'
    END as 权限类型,
    COUNT(*) as 数量
FROM a_func_permission fp
WHERE fp.delete_time IS NULL
GROUP BY fp.type
ORDER BY fp.type;

-- 2.3 角色权限统计
SELECT
    '角色权限统计' as 检查项目,
    r.name as 角色名称,
    r.code as 角色编码,
    COUNT(rp.permission_id) as 权限数量,
    CASE WHEN r.is_system THEN '系统角色' ELSE '自定义角色' END as 角色类型
FROM a_func_role r
LEFT JOIN a_func_role_permission rp ON r.id = rp.role_id AND rp.delete_time IS NULL
WHERE r.delete_time IS NULL
GROUP BY r.id, r.name, r.code, r.is_system
ORDER BY COUNT(rp.permission_id) DESC;

-- 2.4 权限组统计
SELECT
    '权限组统计' as 检查项目,
    pg.name as 权限组名称,
    pg.code as 权限组编码,
    COUNT(fp.id) as 包含权限数量
FROM a_func_permission_group pg
LEFT JOIN a_func_permission fp ON pg.id = fp.group_id AND fp.delete_time IS NULL
WHERE pg.delete_time IS NULL
GROUP BY pg.id, pg.name, pg.code
ORDER BY pg.sort_order;

-- 2.5 会员角色分布
SELECT
    '会员角色分布' as 检查项目,
    r.name as 角色名称,
    COUNT(mr.member_id) as 会员数量,
    ROUND(COUNT(mr.member_id) * 100.0 / SUM(COUNT(mr.member_id)) OVER(), 2) as 占比
FROM a_func_role r
LEFT JOIN a_member_role mr ON r.id = mr.role_id AND mr.delete_time IS NULL
WHERE r.delete_time IS NULL
GROUP BY r.id, r.name
ORDER BY COUNT(mr.member_id) DESC;

-- ==================================================
-- 3. 数据完整性检查
-- ==================================================

-- 3.1 检查会员是否都有认证信息
SELECT 
    '数据完整性检查' as 检查项目,
    '会员认证信息检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_member m
LEFT JOIN a_member_auth ma ON m.id = ma.member_id AND ma.delete_time IS NULL
WHERE m.delete_time IS NULL AND m.member_type != 3 AND ma.id IS NULL;

-- 3.2 检查会员是否都有角色分配
SELECT 
    '数据完整性检查' as 检查项目,
    '会员角色分配检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_member m
LEFT JOIN a_member_role mr ON m.id = mr.member_id AND mr.delete_time IS NULL
WHERE m.delete_time IS NULL AND mr.id IS NULL;

-- 3.3 检查角色是否都有权限分配
SELECT
    '数据完整性检查' as 检查项目,
    '角色权限分配检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_func_role r
LEFT JOIN a_func_role_permission rp ON r.id = rp.role_id AND rp.delete_time IS NULL
WHERE r.delete_time IS NULL AND rp.id IS NULL;

-- 3.4 检查权限组是否都有权限关联
SELECT
    '数据完整性检查' as 检查项目,
    '权限组关联检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_func_permission_group pg
LEFT JOIN a_func_permission fp ON pg.id = fp.group_id AND fp.delete_time IS NULL
WHERE pg.delete_time IS NULL AND fp.id IS NULL;

-- ==================================================
-- 4. 业务逻辑检查
-- ==================================================

-- 4.1 检查邀请关系是否存在循环
WITH RECURSIVE invite_chain AS (
    SELECT id, inviter_id, username, 1 as level, ARRAY[id] as path
    FROM a_member 
    WHERE delete_time IS NULL AND inviter_id IS NOT NULL
    
    UNION ALL
    
    SELECT m.id, m.inviter_id, m.username, ic.level + 1, ic.path || m.id
    FROM a_member m
    JOIN invite_chain ic ON m.id = ic.inviter_id
    WHERE m.delete_time IS NULL AND m.id != ALL(ic.path) AND ic.level < 10
)
SELECT 
    '业务逻辑检查' as 检查项目,
    '邀请关系循环检查' as 检查内容,
    COUNT(*) as 问题数量
FROM invite_chain
WHERE level > 5; -- 检查是否有超过5层的邀请关系

-- 4.2 检查会话是否有重复的活跃token
SELECT 
    '业务逻辑检查' as 检查项目,
    '重复活跃会话检查' as 检查内容,
    COUNT(*) - COUNT(DISTINCT token) as 问题数量
FROM a_member_session
WHERE delete_time IS NULL AND is_active = true;

-- 4.3 检查是否有过期但仍标记为活跃的会话
SELECT 
    '业务逻辑检查' as 检查项目,
    '过期活跃会话检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_member_session
WHERE delete_time IS NULL AND is_active = true AND expire_time < NOW();

-- ==================================================
-- 5. 总体数据概览
-- ==================================================
SELECT 
    '总体数据概览' as 检查项目,
    '会员信息' as 数据类型,
    COUNT(*) as 记录数
FROM a_member
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '会员认证' as 数据类型,
    COUNT(*) as 记录数
FROM a_member_auth
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '会员会话' as 数据类型,
    COUNT(*) as 记录数
FROM a_member_session
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '功能菜单' as 数据类型,
    COUNT(*) as 记录数
FROM a_func_menu
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '功能权限' as 数据类型,
    COUNT(*) as 记录数
FROM a_func_permission
WHERE delete_time IS NULL
UNION ALL
SELECT
    '总体数据概览' as 检查项目,
    '角色信息' as 数据类型,
    COUNT(*) as 记录数
FROM a_func_role
WHERE delete_time IS NULL
UNION ALL
SELECT
    '总体数据概览' as 检查项目,
    '权限组' as 数据类型,
    COUNT(*) as 记录数
FROM a_func_permission_group
WHERE delete_time IS NULL
UNION ALL
SELECT
    '总体数据概览' as 检查项目,
    '角色权限关联' as 数据类型,
    COUNT(*) as 记录数
FROM a_func_role_permission
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '会员角色关联' as 数据类型,
    COUNT(*) as 记录数
FROM a_member_role
WHERE delete_time IS NULL
ORDER BY 数据类型;
