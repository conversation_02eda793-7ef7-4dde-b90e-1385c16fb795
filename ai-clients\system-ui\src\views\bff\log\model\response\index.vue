<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="响应类型" prop="format">
        <el-select v-model="queryParams.format" placeholder="请选择响应类型" clearable>
          <el-option
            v-for="dict in response_format"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态码" prop="responseCode">
        <el-input
          v-model="queryParams.responseCode"
          placeholder="请输入状态码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求耗时" prop="durationMs">
        <el-input
          v-model="queryParams.durationMs"
          placeholder="请输入请求耗时"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="响应完成时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeResponseTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:logModelResponse:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:logModelResponse:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:logModelResponse:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:logModelResponse:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logModelResponseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="请求ID" align="center" prop="requestId" />
      <el-table-column label="响应类型" align="center" prop="format">
        <template #default="scope">
          <dict-tag :options="response_format" :value="scope.row.format"/>
        </template>
      </el-table-column>
      <el-table-column label="流式响应" align="center" prop="isStream" />
      <el-table-column label="状态码" align="center" prop="responseCode" />
      <el-table-column label="请求耗时" align="center" prop="durationMs" />
      <el-table-column label="响应完成时间" align="center" prop="responseTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.responseTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:logModelResponse:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:logModelResponse:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改模型调用响应日志对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="logModelResponseRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="响应内容">
          <editor v-model="form.responseContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="响应类型" prop="format">
          <el-select v-model="form.format" placeholder="请选择响应类型">
            <el-option
              v-for="dict in response_format"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态码" prop="responseCode">
          <el-input v-model="form.responseCode" placeholder="请输入状态码" />
        </el-form-item>
        <el-form-item label="请求耗时" prop="durationMs">
          <el-input v-model="form.durationMs" placeholder="请输入请求耗时" />
        </el-form-item>
        <el-form-item label="响应完成时间" prop="responseTime">
          <el-date-picker clearable
            v-model="form.responseTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择响应完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LogModelResponse">
import { listLogModelResponse, getLogModelResponse, delLogModelResponse, addLogModelResponse, updateLogModelResponse } from "@/api/bff/log/model/response"

const { proxy } = getCurrentInstance()
const { response_format } = proxy.useDict('response_format')

const logModelResponseList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeResponseTime = ref([])
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    requestId: null,
    format: null,
    isStream: null,
    responseCode: null,
    durationMs: null,
    responseTime: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询模型调用响应日志列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeResponseTime && '' != daterangeResponseTime) {
    queryParams.value.params["beginResponseTime"] = daterangeResponseTime.value[0]
    queryParams.value.params["endResponseTime"] = daterangeResponseTime.value[1]
  }
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listLogModelResponse(queryParams.value).then(response => {
    logModelResponseList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    requestId: null,
    responseContent: null,
    format: null,
    isStream: null,
    responseCode: null,
    durationMs: null,
    responseTime: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("logModelResponseRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeResponseTime.value = []
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加模型调用响应日志"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getLogModelResponse(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改模型调用响应日志"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["logModelResponseRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateLogModelResponse(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addLogModelResponse(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除模型调用响应日志编号为"' + _ids + '"的数据项？').then(function() {
    return delLogModelResponse(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/log/model/response/export', {
    ...queryParams.value
  }, `logModelResponse_${new Date().getTime()}.xlsx`)
}

getList()
</script>
