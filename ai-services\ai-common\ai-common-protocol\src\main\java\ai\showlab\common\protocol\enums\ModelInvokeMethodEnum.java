package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型调用方式枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModelInvokeMethodEnum {
    
    /** HTTP */
    HTTP(1, "HTTP"),
    
    /** WebSocket */
    WEBSOCKET(2, "WebSocket"),
    
    /** SDK */
    SDK(3, "SDK");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ModelInvokeMethodEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelInvokeMethodEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 