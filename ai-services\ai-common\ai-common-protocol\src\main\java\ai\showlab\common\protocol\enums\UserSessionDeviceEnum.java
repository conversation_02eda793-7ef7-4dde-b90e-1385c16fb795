package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户会话设备枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UserSessionDeviceEnum {
    
    /** Web端 */
    WEB(1, "Web端"),
    
    /** iOS客户端 */
    IOS(2, "iOS客户端"),
    
    /** Android客户端 */
    ANDROID(3, "Android客户端"),
    
    /** 未知 */
    UNKNOWN(99, "未知");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static UserSessionDeviceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserSessionDeviceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 