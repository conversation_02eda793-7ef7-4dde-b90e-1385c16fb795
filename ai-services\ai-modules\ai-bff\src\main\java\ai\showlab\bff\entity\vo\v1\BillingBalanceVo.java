package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 会员余额视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingBalanceVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 可用余额
     */
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;
    
    /**
     * 总余额（可用余额 + 冻结金额）
     */
    private BigDecimal totalBalance;

    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;
    
    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;

    /**
     * 余额预警阈值
     */
    private BigDecimal lowThreshold;
    
    /**
     * 是否余额不足（余额低于预警阈值）
     */
    private Boolean isLowBalance;
    
    /**
     * 余额状态描述
     */
    private String balanceStatusDesc;
}
