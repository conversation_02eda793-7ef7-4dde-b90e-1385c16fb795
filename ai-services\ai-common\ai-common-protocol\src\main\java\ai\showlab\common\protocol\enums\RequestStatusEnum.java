package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求状态枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RequestStatusEnum {
    
    /** 成功 */
    SUCCESS(1, "成功"),
    
    /** 失败 */
    FAILED(2, "失败"),
    
    /** 处理中 */
    PROCESSING(3, "处理中");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static RequestStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RequestStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 