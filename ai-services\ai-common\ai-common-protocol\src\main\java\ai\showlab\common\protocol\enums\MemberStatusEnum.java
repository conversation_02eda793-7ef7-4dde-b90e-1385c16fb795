package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员状态枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MemberStatusEnum {
    
    /** 正常 */
    NORMAL(1, "正常"),
    
    /** 禁用 */
    DISABLED(2, "禁用"),
    
    /** 待激活 */
    PENDING(3, "待激活");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static MemberStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MemberStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 