import request from '@/utils/request'

// 查询错误日志列表
export function listLogError(query) {
  return request({
    url: '/system/log/error/list',
    method: 'get',
    params: query
  })
}

// 查询错误日志详细
export function getLogError(id) {
  return request({
    url: '/system/log/error/' + id,
    method: 'get'
  })
}

// 新增错误日志
export function addLogError(data) {
  return request({
    url: '/system/log/error',
    method: 'post',
    data: data
  })
}

// 修改错误日志
export function updateLogError(data) {
  return request({
    url: '/system/log/error',
    method: 'put',
    data: data
  })
}

// 删除错误日志
export function delLogError(id) {
  return request({
    url: '/system/log/error/' + id,
    method: 'delete'
  })
}
