package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.util.List;

/**
 * 助手分类视图对象
 * 用于向前端展示助手分类信息
 * 
 * <AUTHOR>
 */
@Data
public class AssistantCategoryVo {
    
    /**
     * 分类ID
     */
    private Long id;
    
    /**
     * 父分类ID
     */
    private Long pid;
    
    /**
     * 分类唯一标识 (字典: assistant_category_type)
     * 1-生活娱乐, 2-办公效率, 3-创意趣味, 99-其他
     */
    private Integer type;
    
    /**
     * 分类名称，用于前端展示
     */
    private String name;
    
    /**
     * 分类的详细描述
     */
    private String description;
    
    /**
     * 前端展示排序值
     */
    private Integer sortOrder;
    
    /**
     * 子分类列表
     */
    private List<AssistantCategoryVo> children;
    
    /**
     * 该分类下的助手数量
     */
    private Long assistantCount;
}
