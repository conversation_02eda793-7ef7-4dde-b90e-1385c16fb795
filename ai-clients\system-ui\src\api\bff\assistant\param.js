import request from '@/utils/request'

// 查询助手参数列表
export function listAssistantParam(query) {
  return request({
    url: '/system/assistant/param/list',
    method: 'get',
    params: query
  })
}

// 查询助手参数详细
export function getAssistantParam(id) {
  return request({
    url: '/system/assistant/param/' + id,
    method: 'get'
  })
}

// 新增助手参数
export function addAssistantParam(data) {
  return request({
    url: '/system/assistant/param',
    method: 'post',
    data: data
  })
}

// 修改助手参数
export function updateAssistantParam(data) {
  return request({
    url: '/system/assistant/param',
    method: 'put',
    data: data
  })
}

// 删除助手参数
export function delAssistantParam(id) {
  return request({
    url: '/system/assistant/param/' + id,
    method: 'delete'
  })
}
