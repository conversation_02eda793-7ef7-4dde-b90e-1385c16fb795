-- ====================================================================================
-- AIShowLab - AI助手和计费模块数据验证脚本
-- 文件名: 数据验证脚本.20250804.sql
-- 作者: AI Assistant
-- 描述: 验证AI助手和计费与消耗模块模拟数据的完整性和正确性
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. AI助手模块数据统计
-- ==================================================

-- 1.1 助手分类统计
SELECT 
    '助手分类统计' as 检查项目,
    CASE 
        WHEN pid IS NULL THEN '一级分类'
        ELSE '二级分类'
    END as 分类级别,
    type as 分类类型,
    name as 分类名称,
    COUNT(CASE WHEN pid = ac.id THEN 1 END) as 子分类数量
FROM a_assistant_category ac
WHERE ac.delete_time IS NULL
GROUP BY ac.id, ac.pid, ac.type, ac.name
ORDER BY ac.type;

-- 1.2 助手数量统计
SELECT 
    '助手数量统计' as 检查项目,
    ac.name as 分类名称,
    COUNT(a.id) as 助手数量,
    SUM(a.usage_count) as 总使用次数,
    AVG(a.usage_count) as 平均使用次数
FROM a_assistant_category ac
LEFT JOIN a_assistant a ON ac.id = a.category_id AND a.delete_time IS NULL
WHERE ac.delete_time IS NULL AND ac.pid IS NULL
GROUP BY ac.id, ac.name
ORDER BY COUNT(a.id) DESC;

-- 1.3 助手状态分布
SELECT 
    '助手状态分布' as 检查项目,
    CASE a.status
        WHEN 1 THEN '草稿'
        WHEN 2 THEN '审核中'
        WHEN 3 THEN '已发布'
        WHEN 4 THEN '已下架'
        ELSE '未知'
    END as 状态,
    COUNT(*) as 数量,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as 占比
FROM a_assistant a
WHERE a.delete_time IS NULL
GROUP BY a.status
ORDER BY a.status;

-- 1.4 助手参数配置统计
SELECT 
    '助手参数配置' as 检查项目,
    a.name as 助手名称,
    COUNT(ap.id) as 参数数量,
    STRING_AGG(ap.key, ', ') as 参数列表
FROM a_assistant a
LEFT JOIN a_assistant_param ap ON a.id = ap.assistant_id AND ap.delete_time IS NULL
WHERE a.delete_time IS NULL
GROUP BY a.id, a.name
ORDER BY COUNT(ap.id) DESC;

-- 1.5 会员助手实例统计
SELECT 
    '会员助手实例' as 检查项目,
    ma.member_id as 会员ID,
    COUNT(ma.id) as 实例数量,
    COUNT(CASE WHEN ma.is_favorite THEN 1 END) as 收藏数量,
    COUNT(CASE WHEN ma.is_active THEN 1 END) as 活跃数量,
    SUM(ma.usage_count) as 总使用次数
FROM a_member_assistant ma
WHERE ma.delete_time IS NULL
GROUP BY ma.member_id
ORDER BY ma.member_id;

-- 1.6 助手收藏排行
SELECT 
    '助手收藏排行' as 检查项目,
    a.name as 助手名称,
    COUNT(af.id) as 收藏数量,
    a.usage_count as 总使用次数
FROM a_assistant a
LEFT JOIN a_assistant_favorite af ON a.id = af.assistant_id AND af.delete_time IS NULL
WHERE a.delete_time IS NULL
GROUP BY a.id, a.name, a.usage_count
ORDER BY COUNT(af.id) DESC, a.usage_count DESC
LIMIT 10;

-- ==================================================
-- 2. 计费模块数据统计
-- ==================================================

-- 2.1 计费方案统计
SELECT 
    '计费方案统计' as 检查项目,
    bp.name as 方案名称,
    CASE bp.unit
        WHEN 1 THEN 'Token'
        WHEN 2 THEN '次数'
        WHEN 3 THEN '图片'
        WHEN 4 THEN '秒'
        ELSE '未知'
    END as 计费单位,
    COUNT(bpr.id) as 关联价格数量
FROM a_billing_plan bp
LEFT JOIN a_billing_price bpr ON bp.id = bpr.plan_id AND bpr.delete_time IS NULL
WHERE bp.delete_time IS NULL
GROUP BY bp.id, bp.name, bp.unit
ORDER BY bp.sort_order;

-- 2.2 套餐类型分布
SELECT 
    '套餐类型分布' as 检查项目,
    CASE bpk.type
        WHEN 1 THEN '一次性'
        WHEN 2 THEN '订阅'
        ELSE '未知'
    END as 套餐类型,
    COUNT(*) as 数量,
    AVG(bpk.price) as 平均价格,
    SUM(bpk.credits_granted) as 总授予额度
FROM a_billing_package bpk
WHERE bpk.delete_time IS NULL
GROUP BY bpk.type
ORDER BY bpk.type;

-- 2.3 会员余额统计
SELECT 
    '会员余额统计' as 检查项目,
    COUNT(*) as 会员数量,
    SUM(bb.balance) as 总余额,
    AVG(bb.balance) as 平均余额,
    MAX(bb.balance) as 最高余额,
    MIN(bb.balance) as 最低余额,
    SUM(bb.frozen_amount) as 总冻结金额
FROM a_billing_balance bb
WHERE bb.delete_time IS NULL;

-- 2.4 使用记录统计（按模型）
SELECT 
    '使用记录统计' as 检查项目,
    bu.model_id as 模型ID,
    COUNT(*) as 使用次数,
    SUM(bu.amount) as 总使用量,
    AVG(bu.amount) as 平均使用量,
    SUM(bu.duration_ms) as 总耗时毫秒,
    AVG(bu.duration_ms) as 平均耗时毫秒
FROM a_billing_usage bu
WHERE bu.delete_time IS NULL
GROUP BY bu.model_id
ORDER BY COUNT(*) DESC;

-- 2.5 交易流水统计
SELECT 
    '交易流水统计' as 检查项目,
    CASE bt.type
        WHEN 1 THEN '消费'
        WHEN 2 THEN '充值'
        WHEN 3 THEN '退款'
        WHEN 4 THEN '赠送'
        ELSE '未知'
    END as 交易类型,
    COUNT(*) as 交易笔数,
    SUM(ABS(bt.amount)) as 总金额,
    AVG(ABS(bt.amount)) as 平均金额
FROM a_billing_transaction bt
WHERE bt.delete_time IS NULL
GROUP BY bt.type
ORDER BY bt.type;

-- 2.6 订单状态统计
SELECT 
    '订单状态统计' as 检查项目,
    CASE bo.status
        WHEN 1 THEN '待支付'
        WHEN 2 THEN '已支付'
        WHEN 3 THEN '已取消'
        WHEN 4 THEN '支付失败'
        WHEN 5 THEN '已退款'
        ELSE '未知'
    END as 订单状态,
    COUNT(*) as 订单数量,
    SUM(bo.amount) as 总金额,
    AVG(bo.amount) as 平均金额
FROM a_billing_order bo
WHERE bo.delete_time IS NULL
GROUP BY bo.status
ORDER BY bo.status;

-- ==================================================
-- 3. 数据完整性检查
-- ==================================================

-- 3.1 检查助手是否都有对应的分类
SELECT 
    '数据完整性检查' as 检查项目,
    '助手分类关联检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_assistant a
LEFT JOIN a_assistant_category ac ON a.category_id = ac.id AND ac.delete_time IS NULL
WHERE a.delete_time IS NULL AND ac.id IS NULL;

-- 3.2 检查会员助手实例是否都有对应的助手
SELECT 
    '数据完整性检查' as 检查项目,
    '会员助手实例关联检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_member_assistant ma
LEFT JOIN a_assistant a ON ma.assistant_id = a.id AND a.delete_time IS NULL
WHERE ma.delete_time IS NULL AND a.id IS NULL;

-- 3.3 检查计费价格是否都有对应的方案
SELECT 
    '数据完整性检查' as 检查项目,
    '计费价格方案关联检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_billing_price bp
LEFT JOIN a_billing_plan bpl ON bp.plan_id = bpl.id AND bpl.delete_time IS NULL
WHERE bp.delete_time IS NULL AND bpl.id IS NULL;

-- 3.4 检查订单是否都有对应的套餐
SELECT 
    '数据完整性检查' as 检查项目,
    '订单套餐关联检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_billing_order bo
LEFT JOIN a_billing_package bpk ON bo.package_id = bpk.id AND bpk.delete_time IS NULL
WHERE bo.delete_time IS NULL AND bpk.id IS NULL;

-- ==================================================
-- 4. 业务逻辑检查
-- ==================================================

-- 4.1 检查余额是否为负数
SELECT 
    '业务逻辑检查' as 检查项目,
    '负余额检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_billing_balance bb
WHERE bb.delete_time IS NULL AND bb.balance < 0;

-- 4.2 检查冻结金额是否超过余额
SELECT 
    '业务逻辑检查' as 检查项目,
    '冻结金额超额检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_billing_balance bb
WHERE bb.delete_time IS NULL AND bb.frozen_amount > bb.balance;

-- 4.3 检查已支付订单是否有支付时间
SELECT 
    '业务逻辑检查' as 检查项目,
    '已支付订单时间检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_billing_order bo
WHERE bo.delete_time IS NULL AND bo.status = 2 AND bo.paid_time IS NULL;

-- ==================================================
-- 5. 总体数据概览
-- ==================================================
SELECT 
    '总体数据概览' as 检查项目,
    '助手分类' as 数据类型,
    COUNT(*) as 记录数
FROM a_assistant_category
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    'AI助手' as 数据类型,
    COUNT(*) as 记录数
FROM a_assistant
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '助手参数' as 数据类型,
    COUNT(*) as 记录数
FROM a_assistant_param
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '会员助手实例' as 数据类型,
    COUNT(*) as 记录数
FROM a_member_assistant
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '助手收藏' as 数据类型,
    COUNT(*) as 记录数
FROM a_assistant_favorite
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '计费方案' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_plan
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '计费套餐' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_package
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '模型价格' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_price
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '会员余额' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_balance
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '使用记录' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_usage
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '交易流水' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_transaction
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '订单记录' as 数据类型,
    COUNT(*) as 记录数
FROM a_billing_order
WHERE delete_time IS NULL
ORDER BY 数据类型;
