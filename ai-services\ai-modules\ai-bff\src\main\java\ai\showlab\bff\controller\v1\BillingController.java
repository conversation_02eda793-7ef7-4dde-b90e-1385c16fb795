package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.docs.BillingApiAnnotations;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.*;
import ai.showlab.bff.service.v1.billing.IBillingService;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.core.web.page.PageResult;
import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 计费接口 (面向外部普通用户)
 * 封装了计费套餐、订单、余额、交易记录等相关操作
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/billing")
@Tag(name = "计费接口", description = "提供了计费套餐查询、订单管理、余额查询、交易记录等核心功能。")
public class BillingController extends BaseController {

    @Autowired
    private IBillingService billingService;

    /**
     * 获取可用的计费套餐列表
     * @param requestParams 通用请求参数
     * @return 套餐列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @BillingApiAnnotations.GetPackageListApiDoc()
    @ApiParamValidate(bizParamClass = BillingPackageListParam.class)
    @PostMapping("/packages")
    public ResponseEntity<RestResult> getPackageList(RequestParams<BillingPackageListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务层获取套餐列表
            PageResult<BillingPackageVo> result = billingService.getPackageList(requestParams);
            return RestResult.ok("获取套餐列表成功", result);
        }, "获取套餐列表失败，请稍后重试");
    }

    /**
     * 创建订单
     * @param requestParams 通用请求参数
     * @return 订单信息
     */
    @BillingApiAnnotations.CreateOrderApiDoc()
    @ApiParamValidate(bizParamClass = BillingOrderCreateParam.class)
    @PostMapping("/orders/create")
    public ResponseEntity<RestResult> createOrder(RequestParams<BillingOrderCreateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务层创建订单
            BillingOrderVo result = billingService.createOrderForMember(requestParams);
            return RestResult.ok("订单创建成功", result);
        }, "订单创建失败，请稍后重试");
    }

    /**
     * 获取当前会员的订单列表
     * @param requestParams 通用请求参数
     * @return 订单列表
     */
    @BillingApiAnnotations.GetOrderListApiDoc()
    @ApiParamValidate(bizParamClass = BillingOrderListParam.class)
    @PostMapping("/orders")
    public ResponseEntity<RestResult> getOrderList(RequestParams<BillingOrderListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务层获取订单列表
            PageResult<BillingOrderVo> result = billingService.getMemberOrderList(requestParams);
            return RestResult.ok("获取订单列表成功", result);
        }, "获取订单列表失败，请稍后重试");
    }

    /**
     * 获取当前会员的余额信息
     * @return 余额信息
     */
    @BillingApiAnnotations.GetBalanceApiDoc()
    @PostMapping("/balance")
    public ResponseEntity<RestResult> getBalance() {
        return executeWithTryCatch(() -> {
            // 调用服务层获取余额信息
            BillingBalanceVo result = billingService.getCurrentMemberBalance();
            return checkNotNull(result, "余额信息不存在");
        }, "获取余额信息失败，请稍后重试");
    }

    /**
     * 获取当前会员的交易记录列表
     * @param requestParams 通用请求参数
     * @return 交易记录列表
     */
    @BillingApiAnnotations.GetTransactionListApiDoc()
    @ApiParamValidate(bizParamClass = BillingTransactionListParam.class)
    @PostMapping("/transactions")
    public ResponseEntity<RestResult> getTransactionList(RequestParams<BillingTransactionListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务层获取交易记录列表
            PageResult<BillingTransactionVo> result = billingService.getMemberTransactionList(requestParams);
            return RestResult.ok("获取交易记录成功", result);
        }, "获取交易记录失败，请稍后重试");
    }

    /**
     * 获取当前会员的使用记录列表
     * @param requestParams 通用请求参数
     * @return 使用记录列表
     */
    @BillingApiAnnotations.GetUsageListApiDoc()
    @ApiParamValidate(bizParamClass = BillingUsageListParam.class)
    @PostMapping("/usage")
    public ResponseEntity<RestResult> getUsageList(RequestParams<BillingUsageListParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            // 调用服务层获取使用记录列表
            PageResult<BillingUsageVo> result = billingService.getMemberUsageList(requestParams);
            return RestResult.ok("获取使用记录成功", result);
        }, "获取使用记录失败，请稍后重试");
    }

    /**
     * 获取计费方案列表
     * @return 计费方案列表
     */
    @ApiAuth(type = ApiAuthTypeEnum.ANONYMOUS)
    @BillingApiAnnotations.GetPlanListApiDoc()
    @PostMapping("/plans")
    public ResponseEntity<RestResult> getPlanList() {
        return executeWithTryCatch(() -> {
            // 调用服务层获取计费方案列表
            java.util.List<BillingPlanVo> result = billingService.getAllPlans();
            return RestResult.ok("获取计费方案成功", result);
        }, "获取计费方案失败，请稍后重试");
    }
}
