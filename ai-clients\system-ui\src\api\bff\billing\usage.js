import request from '@/utils/request'

// 查询会员使用模型记录列表
export function listBillingUsage(query) {
  return request({
    url: '/system/billing/usage/list',
    method: 'get',
    params: query
  })
}

// 查询会员使用模型记录详细
export function getBillingUsage(id) {
  return request({
    url: '/system/billing/usage/' + id,
    method: 'get'
  })
}

// 新增会员使用模型记录
export function addBillingUsage(data) {
  return request({
    url: '/system/billing/usage',
    method: 'post',
    data: data
  })
}

// 修改会员使用模型记录
export function updateBillingUsage(data) {
  return request({
    url: '/system/billing/usage',
    method: 'put',
    data: data
  })
}

// 删除会员使用模型记录
export function delBillingUsage(id) {
  return request({
    url: '/system/billing/usage/' + id,
    method: 'delete'
  })
}
