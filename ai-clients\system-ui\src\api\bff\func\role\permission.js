import request from '@/utils/request'

// 查询角色-权限映射，用于将权限分配给角色列表
export function listFuncRolePermission(query) {
  return request({
    url: '/system/func/role/permission/list',
    method: 'get',
    params: query
  })
}

// 查询角色-权限映射，用于将权限分配给角色详细
export function getFuncRolePermission(id) {
  return request({
    url: '/system/func/role/permission/' + id,
    method: 'get'
  })
}

// 新增角色-权限映射，用于将权限分配给角色
export function addFuncRolePermission(data) {
  return request({
    url: '/system/func/role/permission',
    method: 'post',
    data: data
  })
}

// 修改角色-权限映射，用于将权限分配给角色
export function updateFuncRolePermission(data) {
  return request({
    url: '/system/func/role/permission',
    method: 'put',
    data: data
  })
}

// 删除角色-权限映射，用于将权限分配给角色
export function delFuncRolePermission(id) {
  return request({
    url: '/system/func/role/permission/' + id,
    method: 'delete'
  })
}
