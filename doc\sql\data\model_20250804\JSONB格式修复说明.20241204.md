# JSONB格式修复说明

## 问题描述

在导入模型管理模拟数据时，遇到了以下错误：

```
ERROR: invalid input syntax for type json
详细：Token "high" is invalid.
位置：117
在位置：JSON data, line 1: high
```

## 问题原因

`a_model_feature` 表中的 `value` 字段定义为 `JSONB` 类型，但在插入数据时，某些值没有使用正确的JSON格式：

### 错误的格式：
```sql
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by) 
SELECT m.id, 'safety_level', 'high', '安全级别较高', 'system', 'system'
```

### 正确的格式：
```sql
INSERT INTO a_model_feature (model_id, key, value, description, create_by, update_by) 
SELECT m.id, 'safety_level', '"high"', '安全级别较高', 'system', 'system'
```

## JSONB格式规则

在PostgreSQL的JSONB字段中，所有值都必须是有效的JSON格式：

### 1. 字符串值
- **错误**: `'high'`
- **正确**: `'"high"'`

### 2. 数字字符串
- **错误**: `'128000'`
- **正确**: `'"128000"'`

### 3. 布尔值
- **正确**: `'true'` 或 `'false'`
- **注意**: 布尔值不需要引号

### 4. JSON对象
- **正确**: `'{"min": 0, "max": 2}'`

### 5. JSON数组
- **正确**: `'["zh", "en", "ja", "ko"]'`

## 修复内容

已修复的字段包括：

### GPT-4 Turbo特性
- `context_length`: `'128000'` → `'"128000"'`
- `max_tokens`: `'4096'` → `'"4096"'`

### Claude 3.5 Sonnet特性
- `context_length`: `'200000'` → `'"200000"'`
- `max_tokens`: `'8192'` → `'"8192"'`
- `safety_level`: `'high'` → `'"high"'`

### Gemini 1.5 Pro特性
- `context_length`: `'2000000'` → `'"2000000"'`

### 视频模型特性
- `frame_rate`: `'24'` → `'"24"'`

### 多模态模型特性
- `max_image_size`: `'20971520'` → `'"20971520"'`

## 修复后的文件

1. **`模型管理模拟数据修复版.20241204.sql`** - 完整的修复版本
2. **`测试JSONB修复.20241204.sql`** - JSONB格式测试脚本
3. **原始文件已更新** - `模型管理模拟数据.20241204.sql` 已应用修复

## 验证方法

### 1. 使用测试脚本
```bash
psql -d aishowlab -f 测试JSONB修复.20241204.sql
```

### 2. 手动验证
```sql
-- 测试单个值
SELECT '"high"'::jsonb;  -- 应该成功

-- 测试插入
INSERT INTO a_model_feature (model_id, key, value, description) 
VALUES (1, 'test', '"test_value"', '测试');
```

### 3. 查询现有数据
```sql
-- 检查JSONB类型分布
SELECT 
    jsonb_typeof(value) as json_type,
    COUNT(*) as count
FROM a_model_feature 
WHERE delete_time IS NULL
GROUP BY jsonb_typeof(value);
```

## 最佳实践

### 1. 数据类型选择
- 如果值总是字符串，考虑使用 `TEXT` 类型
- 如果需要JSON查询功能，使用 `JSONB` 类型
- 如果值可能是多种类型，使用 `JSONB` 类型

### 2. 插入数据时
- 字符串值必须用双引号包围：`'"value"'`
- 数字可以不用引号：`'123'` 或作为字符串：`'"123"'`
- 布尔值不用引号：`'true'`、`'false'`
- 对象和数组使用标准JSON格式

### 3. 查询数据时
```sql
-- 获取字符串值
SELECT value #>> '{}' FROM a_model_feature WHERE key = 'safety_level';

-- 获取对象属性
SELECT value->>'min' FROM a_model_feature WHERE key = 'temperature_range';

-- 获取数组元素
SELECT value->>0 FROM a_model_feature WHERE key = 'languages';
```

## 总结

通过将所有字符串和数字字符串值用双引号包围，确保了JSONB字段的数据格式正确性。修复后的数据可以正常导入，并支持PostgreSQL的JSONB查询功能。
