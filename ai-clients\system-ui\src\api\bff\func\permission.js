import request from '@/utils/request'

// 查询权限定义列表
export function listFuncPermission(query) {
  return request({
    url: '/system/func/permission/list',
    method: 'get',
    params: query
  })
}

// 查询权限定义详细
export function getFuncPermission(id) {
  return request({
    url: '/system/func/permission/' + id,
    method: 'get'
  })
}

// 新增权限定义
export function addFuncPermission(data) {
  return request({
    url: '/system/func/permission',
    method: 'post',
    data: data
  })
}

// 修改权限定义
export function updateFuncPermission(data) {
  return request({
    url: '/system/func/permission',
    method: 'put',
    data: data
  })
}

// 删除权限定义
export function delFuncPermission(id) {
  return request({
    url: '/system/func/permission/' + id,
    method: 'delete'
  })
}
