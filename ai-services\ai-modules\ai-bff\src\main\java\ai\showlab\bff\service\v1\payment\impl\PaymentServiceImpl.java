package ai.showlab.bff.service.v1.payment.impl;

import ai.showlab.bff.common.annotation.CustomCache;
import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.entity.domain.v1.billing.BillingOrder;
import ai.showlab.bff.entity.domain.v1.payment.PaymentGateway;
import ai.showlab.bff.entity.param.PaymentCallbackParam;
import ai.showlab.bff.entity.param.PaymentInitiateParam;
import ai.showlab.bff.entity.param.PaymentStatusQueryParam;
import ai.showlab.bff.entity.vo.v1.PaymentInitiateVo;
import ai.showlab.bff.entity.vo.v1.PaymentStatusVo;
import ai.showlab.bff.mapper.v1.billing.BillingOrderMapper;
import ai.showlab.bff.mapper.v1.payment.PaymentGatewayMapper;
import ai.showlab.bff.service.common.BaseService;
import ai.showlab.bff.service.v1.billing.IBillingService;
import ai.showlab.bff.service.v1.payment.IPaymentService;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.protocol.enums.BillingOrderStatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 支付服务实现类
 * <p>
 * 实现支付发起、回调处理、状态查询等核心支付功能。
 * 与billing服务集成，处理订单支付流程。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaymentServiceImpl extends BaseService implements IPaymentService {

    @Autowired
    private BillingOrderMapper billingOrderMapper;
    
    @Autowired
    private PaymentGatewayMapper paymentGatewayMapper;
    
    @Autowired
    private IBillingService billingService;

    @Override
    public PaymentInitiateVo initiatePayment(RequestParams<PaymentInitiateParam> requestParams) {
        PaymentInitiateParam param = requestParams.getBizParam();
        Long currentMemberId = BffKit.getCurrentMemberId();
        
        // 1. 验证订单
        BillingOrder order = billingOrderMapper.selectOrderByOrderNo(param.getOrderNo());
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 2. 验证订单所有权
        if (!Objects.equals(order.getMemberId(), currentMemberId)) {
            throw new BusinessException("无权限操作此订单");
        }
        
        // 3. 验证订单状态
        if (!BillingOrderStatusEnum.canPay(order.getStatus())) {
            throw new BusinessException("订单状态不允许支付");
        }
        
        // 4. 获取支付网关信息
        PaymentGateway gateway = paymentGatewayMapper.selectPaymentGatewayById(param.getPaymentGatewayId());
        if (gateway == null || !Objects.equals(gateway.getStatus(), 1)) {
            throw new BusinessException("支付方式不可用");
        }
        
        // 5. 更新订单的支付网关ID
        if (!Objects.equals(order.getPaymentGatewayId(), param.getPaymentGatewayId())) {
            order.setPaymentGatewayId(param.getPaymentGatewayId());
            billingOrderMapper.updateOrder(order);
        }
        
        // 6. 构建支付发起结果
        PaymentInitiateVo vo = new PaymentInitiateVo();
        vo.setOrderNo(order.getOrderNo());
        vo.setGatewayName(gateway.getName());
        vo.setGatewayCode(gateway.getCode());
        vo.setAmount(order.getAmount());
        
        // TODO: 根据不同支付网关生成相应的支付信息
        // 这里需要根据具体的支付网关实现相应的支付发起逻辑
        generatePaymentInfo(vo, order, gateway, param);
        
        log.info("支付发起成功，订单号: {}, 支付网关: {}", order.getOrderNo(), gateway.getCode());
        return vo;
    }

    @Override
    @Transactional
    @CacheEvict(value = CacheConstants.BFF_MEMBER_BALANCE_KEY, key = "#callbackParam.orderNo")
    public boolean handlePaymentCallback(PaymentCallbackParam callbackParam) {
        try {
            // 1. 验证签名
            if (!verifyCallbackSignature(callbackParam)) {
                log.error("支付回调签名验证失败，订单号: {}", callbackParam.getOrderNo());
                return false;
            }
            
            // 2. 获取订单信息
            BillingOrder order = billingOrderMapper.selectOrderByOrderNo(callbackParam.getOrderNo());
            if (order == null) {
                log.error("支付回调订单不存在，订单号: {}", callbackParam.getOrderNo());
                return false;
            }
            
            // 3. 检查订单状态，避免重复处理
            if (!BillingOrderStatusEnum.canPay(order.getStatus())) {
                log.warn("订单状态不是待支付，可能已处理，订单号: {}, 状态: {}",
                    callbackParam.getOrderNo(), order.getStatus());
                return true; // 返回true避免支付网关重复通知
            }
            
            // 4. 处理支付结果
            if ("success".equals(callbackParam.getPaymentStatus())) {
                // 支付成功，调用billing服务处理
                billingService.handlePaymentSuccess(
                    callbackParam.getOrderNo(),
                    callbackParam.getPaymentGatewayId(),
                    callbackParam.getGatewayTransactionId()
                );
                log.info("支付成功处理完成，订单号: {}", callbackParam.getOrderNo());
            } else {
                // 支付失败，更新订单状态
                order.setStatus(BillingOrderStatusEnum.PAYMENT_FAILED.getCode());
                order.setGatewayTransactionId(callbackParam.getGatewayTransactionId());
                billingOrderMapper.updateOrder(order);
                log.info("支付失败处理完成，订单号: {}, 失败原因: {}",
                    callbackParam.getOrderNo(), callbackParam.getFailureReason());
            }
            
            return true;
        } catch (Exception e) {
            log.error("处理支付回调异常，订单号: {}", callbackParam.getOrderNo(), e);
            return false;
        }
    }

    @Override
    @CustomCache(value = CacheConstants.BFF_PAYMENT_GATEWAY_KEY, 
                key = "'payment_status:' + #requestParams.bizParam.orderNo", 
                unless = "#result == null")
    public PaymentStatusVo queryPaymentStatus(RequestParams<PaymentStatusQueryParam> requestParams) {
        PaymentStatusQueryParam param = requestParams.getBizParam();
        Long currentMemberId = BffKit.getCurrentMemberId();
        
        // 1. 获取订单信息
        BillingOrder order = billingOrderMapper.selectOrderByOrderNo(param.getOrderNo());
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 2. 验证订单所有权
        if (!Objects.equals(order.getMemberId(), currentMemberId)) {
            throw new BusinessException("无权限查询此订单");
        }
        
        // 3. 如果需要强制刷新，从支付网关查询最新状态
        if (Boolean.TRUE.equals(param.getForceRefresh())) {
            PaymentStatusVo gatewayStatus = queryPaymentStatusFromGateway(
                param.getOrderNo(), order.getPaymentGatewayId());
            if (gatewayStatus != null) {
                return gatewayStatus;
            }
        }
        
        // 4. 构建支付状态VO
        return buildPaymentStatusVo(order);
    }

    @Override
    public PaymentStatusVo queryPaymentStatusFromGateway(String orderNo, Long paymentGatewayId) {
        // TODO: 实现从支付网关查询状态的逻辑
        // 这里需要根据不同的支付网关实现相应的状态查询逻辑
        log.info("从支付网关查询支付状态，订单号: {}, 网关ID: {}", orderNo, paymentGatewayId);
        
        // 暂时返回null，表示无法从网关查询
        return null;
    }

    @Override
    @Transactional
    public boolean cancelPayment(String orderNo, Long memberId) {
        // 1. 获取订单信息
        BillingOrder order = billingOrderMapper.selectOrderByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        
        // 2. 验证订单所有权
        if (!Objects.equals(order.getMemberId(), memberId)) {
            throw new BusinessException("无权限操作此订单");
        }
        
        // 3. 验证订单状态
        if (!BillingOrderStatusEnum.canCancel(order.getStatus())) {
            throw new BusinessException("只能取消待支付的订单");
        }

        // 4. 更新订单状态为已取消
        order.setStatus(BillingOrderStatusEnum.CANCELLED.getCode());
        int result = billingOrderMapper.updateOrder(order);
        
        if (result > 0) {
            log.info("订单取消成功，订单号: {}", orderNo);
            return true;
        } else {
            log.error("订单取消失败，订单号: {}", orderNo);
            return false;
        }
    }

    @Override
    public boolean verifyCallbackSignature(PaymentCallbackParam callbackParam) {
        // TODO: 实现签名验证逻辑
        // 这里需要根据不同的支付网关实现相应的签名验证逻辑
        log.debug("验证支付回调签名，订单号: {}", callbackParam.getOrderNo());
        
        // 暂时返回true，实际应该根据支付网关的签名算法进行验证
        return true;
    }

    @Override
    @Transactional
    public boolean handlePaymentTimeout(String orderNo) {
        // 1. 获取订单信息
        BillingOrder order = billingOrderMapper.selectOrderByOrderNo(orderNo);
        if (order == null) {
            log.warn("处理支付超时时订单不存在，订单号: {}", orderNo);
            return false;
        }
        
        // 2. 检查订单状态
        if (!BillingOrderStatusEnum.canCancel(order.getStatus())) {
            log.info("订单状态不是待支付，无需处理超时，订单号: {}, 状态: {}", orderNo, order.getStatus());
            return true;
        }

        // 3. 更新订单状态为已取消
        order.setStatus(BillingOrderStatusEnum.CANCELLED.getCode());
        int result = billingOrderMapper.updateOrder(order);
        
        if (result > 0) {
            log.info("支付超时订单处理成功，订单号: {}", orderNo);
            return true;
        } else {
            log.error("支付超时订单处理失败，订单号: {}", orderNo);
            return false;
        }
    }

    /**
     * 生成支付信息
     * 
     * @param vo 支付发起VO
     * @param order 订单信息
     * @param gateway 支付网关信息
     * @param param 支付参数
     */
    private void generatePaymentInfo(PaymentInitiateVo vo, BillingOrder order, 
                                   PaymentGateway gateway, PaymentInitiateParam param) {
        // TODO: 根据不同支付网关生成相应的支付信息
        // 这里是示例实现，实际需要根据具体的支付网关API进行实现
        
        switch (gateway.getCode()) {
            case "alipay":
                generateAlipayInfo(vo, order, gateway, param);
                break;
            case "wechat_pay":
                generateWechatPayInfo(vo, order, gateway, param);
                break;
            case "paypal":
                generatePaypalInfo(vo, order, gateway, param);
                break;
            default:
                throw new BusinessException("不支持的支付方式: " + gateway.getCode());
        }
        
        // 设置支付超时时间（默认30分钟）
        vo.setExpireTime(OffsetDateTime.now().plusMinutes(30));
    }

    /**
     * 生成支付宝支付信息
     */
    private void generateAlipayInfo(PaymentInitiateVo vo, BillingOrder order, 
                                  PaymentGateway gateway, PaymentInitiateParam param) {
        // TODO: 实现支付宝支付信息生成
        vo.setPaymentType("redirect");
        vo.setPaymentUrl("https://example.com/alipay/pay?orderNo=" + order.getOrderNo());
        vo.setPaymentDescription("支付宝支付");
    }

    /**
     * 生成微信支付信息
     */
    private void generateWechatPayInfo(PaymentInitiateVo vo, BillingOrder order, 
                                     PaymentGateway gateway, PaymentInitiateParam param) {
        // TODO: 实现微信支付信息生成
        vo.setPaymentType("qrcode");
        vo.setQrCodeContent("weixin://wxpay/bizpayurl?pr=" + order.getOrderNo());
        vo.setPaymentDescription("微信支付");
    }

    /**
     * 生成PayPal支付信息
     */
    private void generatePaypalInfo(PaymentInitiateVo vo, BillingOrder order, 
                                  PaymentGateway gateway, PaymentInitiateParam param) {
        // TODO: 实现PayPal支付信息生成
        vo.setPaymentType("redirect");
        vo.setPaymentUrl("https://example.com/paypal/pay?orderNo=" + order.getOrderNo());
        vo.setPaymentDescription("PayPal支付");
    }

    /**
     * 构建支付状态VO
     */
    private PaymentStatusVo buildPaymentStatusVo(BillingOrder order) {
        PaymentStatusVo vo = new PaymentStatusVo();
        vo.setOrderNo(order.getOrderNo());
        vo.setOrderStatus(order.getStatus());
        vo.setAmount(order.getAmount());
        vo.setCreateTime(order.getCreateTime());
        vo.setPaidTime(order.getPaidTime());
        vo.setGatewayTransactionId(order.getGatewayTransactionId());
        
        // 设置状态描述
        BillingOrderStatusEnum statusEnum = BillingOrderStatusEnum.getByCode(order.getStatus());
        if (statusEnum != null) {
            vo.setOrderStatusDesc(statusEnum.getType());
            switch (statusEnum) {
                case PENDING_PAYMENT:
                    vo.setPaymentStatus("pending");
                    vo.setPaymentStatusDesc("等待支付");
                    break;
                case COMPLETED:
                    vo.setPaymentStatus("success");
                    vo.setPaymentStatusDesc("支付成功");
                    break;
                case CANCELLED:
                    vo.setPaymentStatus("cancelled");
                    vo.setPaymentStatusDesc("支付取消");
                    break;
                case PAYMENT_FAILED:
                    vo.setPaymentStatus("failed");
                    vo.setPaymentStatusDesc("支付失败");
                    break;
                default:
                    vo.setPaymentStatus("unknown");
                    vo.setPaymentStatusDesc("未知状态");
            }
        } else {
            vo.setOrderStatusDesc("未知状态");
            vo.setPaymentStatus("unknown");
            vo.setPaymentStatusDesc("未知状态");
        }
        
        return vo;
    }
}
