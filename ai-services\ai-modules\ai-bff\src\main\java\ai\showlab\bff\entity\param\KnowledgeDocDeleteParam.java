package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库文档删除参数
 * <p>
 * 用于删除知识库中的文档。
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class KnowledgeDocDeleteParam {

    /**
     * 文档ID
     */
    @NotNull(message = "文档ID不能为空")
    private Long docId;
    
    /**
     * 知识库ID（用于权限校验）
     */
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;
}
