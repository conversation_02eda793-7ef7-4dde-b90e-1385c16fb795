package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.OffsetDateTime;

/**
 * 知识库文档视图对象
 * <p>
 * 用于向前端展示文档信息，包含处理状态和统计数据。
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class KnowledgeDocVo {

    /**
     * 文档ID
     */
    private Long id;
    
    /**
     * 知识库ID
     */
    private Long knowledgeId;
    
    /**
     * 原始文件名
     */
    private String fileName;
    
    /**
     * 文件类型 (字典: file_mime_type)
     */
    private Integer fileType;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 处理状态 (字典: doc_processing_status)
     * 1-上传中, 2-待处理, 3-切分中, 4-向量化, 5-就绪, 6-错误
     */
    private Integer processingStatus;
    
    /**
     * 错误信息（如果处理失败）
     */
    private String errorMessage;
    
    /**
     * 文本块数量
     */
    private Integer chunkCount;
    
    /**
     * 总字符数
     */
    private Long charCount;
    
    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
    
    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
}
