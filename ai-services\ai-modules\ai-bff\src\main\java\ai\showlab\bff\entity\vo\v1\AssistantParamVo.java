package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 助手参数视图对象
 * 用于向前端展示助手参数配置信息
 * 
 * <AUTHOR>
 */
@Data
public class AssistantParamVo {
    
    /**
     * 参数ID
     */
    private Long id;
    
    /**
     * 助手ID
     */
    private Long assistantId;
    
    /**
     * 参数的英文标识 (如: wake_word)
     */
    private String key;
    
    /**
     * 参数在界面上显示的名称 (如: "唤醒词")
     */
    private String label;
    
    /**
     * 参数控件类型 (字典: param_type)
     * 1-文本, 2-数字, 3-开关, 4-下拉选择, 5-多行文本
     */
    private Integer paramType;
    
    /**
     * 参数控件类型名称
     */
    private String paramTypeName;
    
    /**
     * 参数的默认值
     */
    private String defaultValue;
    
    /**
     * 若为select类型，存储选项列表
     * 格式: [{"value": "v1", "label": "选项1"}, {"value": "v2", "label": "选项2"}]
     */
    private List<Map<String, Object>> options;
    
    /**
     * 参数的提示或说明文字
     */
    private String description;
    
    /**
     * 参数在界面上的排序
     */
    private Integer sortOrder;
    
    /**
     * 当前会员设置的值（如果有会员助手实例）
     */
    private String currentValue;
}
