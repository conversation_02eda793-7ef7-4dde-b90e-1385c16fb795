package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 会员助手实例列表查询参数
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantListParam {
    
    /**
     * 助手分类ID，可选
     */
    private Long categoryId;
    
    /**
     * 交互模式，可选 (字典: assistant_interaction_mode)
     */
    private Integer interactionMode;
    
    /**
     * 是否收藏，可选
     */
    private Boolean isFavorite;
    
    /**
     * 是否激活，可选
     */
    private Boolean isActive;
    
    /**
     * 关键词搜索，可选（搜索助手名称和自定义名称）
     */
    private String keyword;
    
    /**
     * 排序字段，可选 (create_time-创建时间, update_time-更新时间, custom_name-名称)
     */
    private String sortBy = "update_time";
    
    /**
     * 排序方向，可选 (asc-升序, desc-降序)
     */
    private String sortOrder = "desc";
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
