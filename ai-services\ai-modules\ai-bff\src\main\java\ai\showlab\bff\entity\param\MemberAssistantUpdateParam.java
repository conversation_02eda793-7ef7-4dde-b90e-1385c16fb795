package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 更新会员助手实例参数
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantUpdateParam {
    
    /**
     * 会员助手实例ID
     */
    @NotNull(message = "会员助手实例ID不能为空")
    private Long memberAssistantId;
    
    /**
     * 选择的模型ID，可选
     */
    private Long modelId;
    
    /**
     * 自定义名称，可选
     */
    private String customName;
    
    /**
     * 参数配置覆盖，可选
     * 以 key-value 形式存储，如 {"wake_word": "你好AI", "response_style": "formal"}
     */
    private Map<String, Object> settingsOverride;
    
    /**
     * 是否收藏，可选
     */
    private Boolean isFavorite;
    
    /**
     * 是否激活，可选
     */
    private Boolean isActive;
}
