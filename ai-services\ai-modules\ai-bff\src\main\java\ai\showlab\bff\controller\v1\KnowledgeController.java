package ai.showlab.bff.controller.v1;

import ai.showlab.bff.common.annotation.ApiParamValidate;
import ai.showlab.bff.common.docs.KnowledgeApiAnnotations;
import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.common.util.ParamValidateUtil;
import ai.showlab.bff.controller.BaseController;
import ai.showlab.bff.entity.param.*;
import ai.showlab.bff.entity.vo.v1.KnowledgeBaseDetailVo;
import ai.showlab.bff.entity.vo.v1.KnowledgeBaseVo;
import ai.showlab.bff.entity.vo.v1.KnowledgeDocVo;
import ai.showlab.bff.service.v1.knowledge.IKnowledgeService;
import ai.showlab.common.core.web.domain.RestResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库接口 (面向外部普通用户)
 * <p>
 * 提供知识库的CRUD操作、文档管理等功能。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/knowledge")
@Tag(name = "知识库接口", description = "提供了知识库的创建、管理、文档上传等功能。")
public class KnowledgeController extends BaseController {

    @Autowired
    private IKnowledgeService knowledgeService;

    /**
     * 获取当前会员的知识库列表
     *
     * @param requestParams 请求参数（可选）
     * @return 知识库列表
     */
    @KnowledgeApiAnnotations.GetKnowledgeListApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeListParam.class)
    @PostMapping("/list")
    public ResponseEntity<RestResult> getKnowledgeList(RequestParams<KnowledgeListParam> requestParams) {
        return executeWithTryCatch(() -> {
            // 对于可选参数，只在存在时进行验证
            if (requestParams.getBizParam() != null) {
                ParamValidateUtil.validate(requestParams.getBizParam());
            }
            List<KnowledgeBaseVo> knowledgeBases = knowledgeService.getMemberKnowledgeBases(requestParams);
            return RestResult.ok("获取成功", knowledgeBases);
        }, "获取知识库列表失败，请稍后重试");
    }

    /**
     * 获取知识库详情
     *
     * @param requestParams 请求参数
     * @return 知识库详情
     */
    @KnowledgeApiAnnotations.GetKnowledgeDetailApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeDetailParam.class)
    @PostMapping("/detail")
    public ResponseEntity<RestResult> getKnowledgeDetail(RequestParams<KnowledgeDetailParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            KnowledgeBaseDetailVo detail = knowledgeService.getKnowledgeBaseDetail(requestParams);
            return checkNotNull(detail, "知识库不存在");
        }, "获取知识库详情失败，请稍后重试");
    }

    /**
     * 创建知识库
     *
     * @param requestParams 请求参数
     * @return 创建结果
     */
    @KnowledgeApiAnnotations.CreateKnowledgeApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeCreateParam.class)
    @PostMapping("/create")
    public ResponseEntity<RestResult> createKnowledge(RequestParams<KnowledgeCreateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            KnowledgeBaseVo knowledge = knowledgeService.createKnowledgeBase(requestParams);
            return RestResult.ok("创建成功", knowledge);
        }, "创建知识库失败，请稍后重试");
    }

    /**
     * 更新知识库
     *
     * @param requestParams 请求参数
     * @return 更新结果
     */
    @KnowledgeApiAnnotations.UpdateKnowledgeApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeUpdateParam.class)
    @PostMapping("/update")
    public ResponseEntity<RestResult> updateKnowledge(RequestParams<KnowledgeUpdateParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            knowledgeService.updateKnowledgeBase(requestParams);
            return RestResult.ok("更新成功");
        }, "更新知识库失败，请稍后重试");
    }

    /**
     * 删除知识库
     *
     * @param requestParams 请求参数
     * @return 删除结果
     */
    @KnowledgeApiAnnotations.DeleteKnowledgeApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeDeleteParam.class)
    @PostMapping("/delete")
    public ResponseEntity<RestResult> deleteKnowledge(RequestParams<KnowledgeDeleteParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            knowledgeService.deleteKnowledgeBase(requestParams);
            return RestResult.ok("删除成功");
        }, "删除知识库失败，请稍后重试");
    }

    /**
     * 上传文档到知识库
     *
     * @param requestParams 请求参数
     * @return 上传结果
     */
    @KnowledgeApiAnnotations.UploadDocumentApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeDocUploadParam.class)
    @PostMapping("/document/upload")
    public ResponseEntity<RestResult> uploadDocument(RequestParams<KnowledgeDocUploadParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            KnowledgeDocVo doc = knowledgeService.uploadDocument(requestParams);
            return RestResult.ok("上传成功", doc);
        }, "上传文档失败，请稍后重试");
    }

    /**
     * 删除知识库中的文档
     *
     * @param requestParams 请求参数
     * @return 删除结果
     */
    @KnowledgeApiAnnotations.DeleteDocumentApiDoc()
    @ApiParamValidate(bizParamClass = KnowledgeDocDeleteParam.class)
    @PostMapping("/document/delete")
    public ResponseEntity<RestResult> deleteDocument(RequestParams<KnowledgeDocDeleteParam> requestParams) {
        return executeWithTryCatch(() -> {
            ParamValidateUtil.validate(requestParams.getBizParam());
            knowledgeService.deleteDocument(requestParams);
            return RestResult.ok("删除成功");
        }, "删除文档失败，请稍后重试");
    }
}
