-- ====================================================================================
-- AIShowLab - 前端菜单权限模块模拟数据 (PostgreSQL)
-- 文件名: 前端菜单权限模拟数据.sql
-- 作者: AI Assistant
-- 描述: 为前端菜单权限相关表生成模拟数据，包括菜单结构、角色、权限、权限组等
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. 功能菜单数据 (a_func_menu)
-- ==================================================

-- 一级菜单
INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by) VALUES
-- 工作台
(NULL, '工作台', 'dashboard', 1, 1, 1, 'dashboard', 'views/dashboard/index', '/dashboard', NULL, true, true, true, 'system', 'system'),

-- AI助手
(NULL, 'AI助手', 'assistant', 1, 2, 1, 'robot', 'Layout', '/assistant', '/assistant/chat', false, true, true, 'system', 'system'),

-- 模型管理
(NULL, '模型管理', 'model', 1, 3, 1, 'cpu', 'Layout', '/model', '/model/list', false, true, true, 'system', 'system'),

-- 计费管理
(NULL, '计费管理', 'billing', 1, 4, 1, 'money', 'Layout', '/billing', '/billing/usage', false, true, true, 'system', 'system'),

-- 会员中心
(NULL, '会员中心', 'member', 1, 5, 1, 'user', 'Layout', '/member', '/member/profile', false, true, true, 'system', 'system'),

-- 系统管理
(NULL, '系统管理', 'system', 1, 6, 1, 'setting', 'Layout', '/system', '/system/user', false, true, true, 'system', 'system');

-- 二级菜单 - AI助手
INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '智能对话', 'assistant-chat', 1, 1, 1, 'chat-dot-round', 'views/assistant/chat/index', 'chat', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'assistant' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '助手广场', 'assistant-market', 1, 2, 1, 'shop', 'views/assistant/market/index', 'market', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'assistant' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '我的助手', 'assistant-my', 1, 3, 1, 'collection', 'views/assistant/my/index', 'my', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'assistant' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '创建助手', 'assistant-create', 1, 4, 1, 'plus', 'views/assistant/create/index', 'create', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'assistant' AND m.pid IS NULL;

-- 二级菜单 - 模型管理
INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '模型列表', 'model-list', 1, 1, 1, 'list', 'views/model/list/index', 'list', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'model' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '模型分类', 'model-category', 1, 2, 1, 'folder', 'views/model/category/index', 'category', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'model' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '供应商管理', 'model-provider', 1, 3, 1, 'office-building', 'views/model/provider/index', 'provider', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'model' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, 'API密钥', 'model-apikey', 1, 4, 1, 'key', 'views/model/apikey/index', 'apikey', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'model' AND m.pid IS NULL;

-- 二级菜单 - 计费管理
INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '使用统计', 'billing-usage', 1, 1, 1, 'data-line', 'views/billing/usage/index', 'usage', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'billing' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '套餐管理', 'billing-package', 1, 2, 1, 'box', 'views/billing/package/index', 'package', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'billing' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '订单记录', 'billing-order', 1, 3, 1, 'document', 'views/billing/order/index', 'order', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'billing' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '余额管理', 'billing-balance', 1, 4, 1, 'wallet', 'views/billing/balance/index', 'balance', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'billing' AND m.pid IS NULL;

-- 二级菜单 - 会员中心
INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '个人资料', 'member-profile', 1, 1, 1, 'user', 'views/member/profile/index', 'profile', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'member' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '安全设置', 'member-security', 1, 2, 1, 'lock', 'views/member/security/index', 'security', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'member' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '邀请好友', 'member-invite', 1, 3, 1, 'share', 'views/member/invite/index', 'invite', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'member' AND m.pid IS NULL;

-- 二级菜单 - 系统管理
INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '用户管理', 'system-user', 1, 1, 1, 'user', 'views/system/user/index', 'user', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'system' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '角色管理', 'system-role', 1, 2, 1, 'user-filled', 'views/system/role/index', 'role', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'system' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '权限管理', 'system-permission', 1, 3, 1, 'key', 'views/system/permission/index', 'permission', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'system' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '菜单管理', 'system-menu', 1, 4, 1, 'menu', 'views/system/menu/index', 'menu', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'system' AND m.pid IS NULL;

INSERT INTO a_func_menu (pid, name, code, type, sort_order, status, icon, component, path, redirect, affix, breadcrumb, is_cache, create_by, update_by)
SELECT m.id, '系统日志', 'system-log', 1, 5, 1, 'document', 'views/system/log/index', 'log', NULL, false, true, true, 'system', 'system'
FROM a_func_menu m WHERE m.code = 'system' AND m.pid IS NULL;

-- ==================================================
-- 2. 权限组数据 (a_func_permission_group)
-- ==================================================
INSERT INTO a_func_permission_group (pid, code, name, description, sort_order, create_by, update_by) VALUES
(NULL, 'basic', '基础功能', '基础功能权限组，包含最基本的查看和使用权限', 1, 'system', 'system'),
(NULL, 'assistant', 'AI助手功能', 'AI助手相关功能权限组', 2, 'system', 'system'),
(NULL, 'model', '模型管理', '模型管理相关权限组', 3, 'system', 'system'),
(NULL, 'billing', '计费管理', '计费管理相关权限组', 4, 'system', 'system'),
(NULL, 'member', '会员功能', '会员中心相关权限组', 5, 'system', 'system'),
(NULL, 'system', '系统管理', '系统管理相关权限组', 6, 'system', 'system'),
(NULL, 'advanced', '高级功能', '高级功能权限组', 7, 'system', 'system');

-- ==================================================
-- 3. 功能权限数据 (a_func_permission)
-- ==================================================

-- 基础权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'dashboard:view', '工作台查看', '查看工作台页面', 1, 1, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'basic';

-- AI助手权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:view', 'AI助手查看', '查看AI助手相关页面', 1, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:chat', '智能对话', '使用智能对话功能', 2, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:market:view', '助手广场查看', '查看助手广场', 1, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:my:view', '我的助手查看', '查看我的助手', 1, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:create', '创建助手', '创建新的AI助手', 2, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:edit', '编辑助手', '编辑AI助手', 2, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:delete', '删除助手', '删除AI助手', 3, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'assistant:favorite', '收藏助手', '收藏/取消收藏助手', 2, 2, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'assistant';

-- 模型管理权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'model:view', '模型管理查看', '查看模型管理相关页面', 1, 3, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'model';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'model:list:view', '模型列表查看', '查看模型列表', 1, 3, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'model';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'model:detail:view', '模型详情查看', '查看模型详情', 1, 3, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'model';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'model:create', '模型创建', '创建新模型', 2, 3, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'model';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'model:edit', '模型编辑', '编辑模型信息', 2, 3, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'model';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'model:delete', '模型删除', '删除模型', 3, 3, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'model';

-- 计费管理权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'billing:view', '计费管理查看', '查看计费管理相关页面', 1, 4, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'billing';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'billing:usage:view', '使用统计查看', '查看使用统计', 1, 4, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'billing';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'billing:package:manage', '套餐管理', '管理计费套餐', 2, 4, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'billing';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'billing:order:view', '订单查看', '查看订单记录', 1, 4, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'billing';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'billing:balance:manage', '余额管理', '管理账户余额', 2, 4, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'billing';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'billing:recharge', '充值操作', '执行充值操作', 2, 4, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'billing';

-- 会员中心权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'member:view', '会员中心查看', '查看会员中心相关页面', 1, 5, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'member';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'member:profile:view', '个人资料查看', '查看个人资料', 1, 5, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'member';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'member:profile:edit', '个人资料编辑', '编辑个人资料', 2, 5, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'member';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'member:security:view', '安全设置查看', '查看安全设置', 1, 5, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'member';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'member:security:edit', '安全设置编辑', '修改安全设置', 2, 5, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'member';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'member:invite', '邀请好友', '邀请好友功能', 2, 5, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'member';

-- 系统管理权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:view', '系统管理查看', '查看系统管理相关页面', 1, 6, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'system';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:user:manage', '用户管理', '管理系统用户', 2, 6, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'system';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:role:manage', '角色管理', '管理系统角色', 2, 6, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'system';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:permission:manage', '权限管理', '管理系统权限', 2, 6, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'system';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:menu:manage', '菜单管理', '管理系统菜单', 2, 6, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'system';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:log:view', '系统日志查看', '查看系统日志', 1, 6, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'system';

-- 高级权限
INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'data:export', '数据导出', '导出数据功能', 2, 7, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'advanced';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'data:import', '数据导入', '导入数据功能', 2, 7, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'advanced';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'system:config', '系统配置', '修改系统配置', 2, 7, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'advanced';

INSERT INTO a_func_permission (group_id, code, name, description, action_type, resource_type, create_by, update_by)
SELECT pg.id, 'audit:manage', '审核管理', '内容审核管理', 2, 7, 'system', 'system'
FROM a_func_permission_group pg WHERE pg.code = 'advanced';

-- ==================================================
-- 4. 角色数据 (a_func_role)
-- ==================================================
INSERT INTO a_func_role (pid, code, name, sort_order, description, is_system, create_by, update_by) VALUES
(NULL, 'super_admin', '超级管理员', 1, '系统超级管理员，拥有所有权限', true, 'system', 'system'),
(NULL, 'admin', '系统管理员', 2, '系统管理员，拥有大部分管理权限', true, 'system', 'system'),
(NULL, 'auditor', '内容审核员', 3, '内容审核员，负责审核用户创建的助手和内容', true, 'system', 'system'),
(NULL, 'enterprise', '企业用户', 4, '企业级用户，拥有高级功能权限', false, 'system', 'system'),
(NULL, 'vip', 'VIP用户', 5, 'VIP用户，拥有部分高级功能权限', false, 'system', 'system'),
(NULL, 'user', '普通用户', 6, '普通注册用户，拥有基础功能权限', false, 'system', 'system'),
(NULL, 'guest', '访客用户', 7, '访客用户，仅拥有基础查看权限', false, 'system', 'system');

-- ==================================================
-- 5. 角色权限关联数据 (a_func_role_permission)
-- ==================================================

-- 超级管理员 - 拥有所有权限
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'super_admin' AND fp.delete_time IS NULL;

-- 系统管理员 - 拥有除高级功能外的大部分权限
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'admin' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:create', 'assistant:edit', 'assistant:delete', 'assistant:favorite',
    'model:view', 'model:list:view', 'model:detail:view', 'model:create', 'model:edit', 'model:delete',
    'billing:view', 'billing:usage:view', 'billing:package:manage', 'billing:order:view', 'billing:balance:manage',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view', 'member:security:edit',
    'system:view', 'system:user:manage', 'system:role:manage', 'system:permission:manage', 'system:menu:manage', 'system:log:view'
);

-- 内容审核员 - 拥有基础功能、AI助手功能和审核权限
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'auditor' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:market:view',
    'assistant:my:view', 'assistant:edit', 'assistant:delete',
    'member:view', 'member:profile:view', 'audit:manage'
);

-- 企业用户 - 拥有基础功能、AI助手功能、计费管理、会员功能
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'enterprise' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:create', 'assistant:edit', 'assistant:favorite',
    'billing:view', 'billing:usage:view', 'billing:order:view', 'billing:balance:manage', 'billing:recharge',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view',
    'member:security:edit', 'member:invite', 'data:export'
);

-- VIP用户 - 拥有基础功能、AI助手功能、部分计费功能、会员功能
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'vip' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:create', 'assistant:favorite',
    'billing:view', 'billing:usage:view', 'billing:order:view', 'billing:recharge',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view',
    'member:security:edit', 'member:invite'
);

-- 普通用户 - 拥有基础功能、基础AI助手功能、基础计费功能、会员功能
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'user' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:favorite',
    'billing:view', 'billing:usage:view', 'billing:order:view', 'billing:recharge',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view',
    'member:security:edit'
);

-- 访客用户 - 仅拥有基础查看权限
INSERT INTO a_func_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_func_role r, a_func_permission fp
WHERE r.code = 'guest' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:market:view'
);

-- ==================================================
-- 6. 会员角色关联数据 (a_member_role)
-- ==================================================

-- 管理员角色
INSERT INTO a_member_role (member_id, role_id)
SELECT 1, r.id FROM a_func_role r WHERE r.code = 'super_admin';

-- 普通会员角色分配
INSERT INTO a_member_role (member_id, role_id)
SELECT 2, r.id FROM a_func_role r WHERE r.code = 'user';  -- user001 -> 普通用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 3, r.id FROM a_func_role r WHERE r.code = 'vip';   -- user002 -> VIP用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 4, r.id FROM a_func_role r WHERE r.code = 'vip';   -- user003 -> VIP用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 5, r.id FROM a_func_role r WHERE r.code = 'enterprise'; -- user004 -> 企业用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 6, r.id FROM a_func_role r WHERE r.code = 'user';  -- user005 -> 普通用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 7, r.id FROM a_func_role r WHERE r.code = 'vip';   -- user006 -> VIP用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 8, r.id FROM a_func_role r WHERE r.code = 'enterprise'; -- user007 -> 企业用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 9, r.id FROM a_func_role r WHERE r.code = 'user';  -- user008 -> 普通用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 10, r.id FROM a_func_role r WHERE r.code = 'vip';  -- user009 -> VIP用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 11, r.id FROM a_func_role r WHERE r.code = 'enterprise'; -- user010 -> 企业用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 12, r.id FROM a_func_role r WHERE r.code = 'user'; -- user011 -> 普通用户（待激活）

INSERT INTO a_member_role (member_id, role_id)
SELECT 13, r.id FROM a_func_role r WHERE r.code = 'user'; -- user012 -> 普通用户（禁用）

-- 访客用户角色
INSERT INTO a_member_role (member_id, role_id)
SELECT 14, r.id FROM a_func_role r WHERE r.code = 'guest'; -- guest001 -> 访客用户

INSERT INTO a_member_role (member_id, role_id)
SELECT 15, r.id FROM a_func_role r WHERE r.code = 'guest'; -- guest002 -> 访客用户

-- ==================================================
-- 数据插入完成
-- ==================================================

-- ==================================================
-- 6. 角色权限关联数据 (a_role_permission)
-- ==================================================

-- 超级管理员 - 拥有所有权限
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'super_admin';

-- 系统管理员 - 拥有除高级功能外的所有权限
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'admin' AND fp.type IN (1, 2);

-- 内容审核员 - 拥有基础功能、AI助手功能和审核权限
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'auditor' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:market:view',
    'assistant:my:view', 'assistant:edit', 'assistant:delete',
    'member:view', 'member:profile:view', 'audit:manage'
);

-- 企业用户 - 拥有基础功能、AI助手功能、计费管理、会员功能
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'enterprise' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:create', 'assistant:edit', 'assistant:favorite',
    'billing:view', 'billing:usage:view', 'billing:order:view', 'billing:balance:manage', 'billing:recharge',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view',
    'member:security:edit', 'member:invite', 'data:export'
);

-- VIP用户 - 拥有基础功能、AI助手功能、部分计费功能、会员功能
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'vip' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:create', 'assistant:favorite',
    'billing:view', 'billing:usage:view', 'billing:order:view', 'billing:recharge',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view',
    'member:security:edit', 'member:invite'
);

-- 普通用户 - 拥有基础功能、基础AI助手功能、基础计费功能、会员功能
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'user' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:chat', 'assistant:market:view',
    'assistant:my:view', 'assistant:favorite',
    'billing:view', 'billing:usage:view', 'billing:order:view', 'billing:recharge',
    'member:view', 'member:profile:view', 'member:profile:edit', 'member:security:view',
    'member:security:edit'
);

-- 访客用户 - 仅拥有基础查看权限
INSERT INTO a_role_permission (role_id, permission_id)
SELECT r.id, fp.id FROM a_role r, a_func_permission fp
WHERE r.code = 'guest' AND fp.code IN (
    'dashboard:view', 'assistant:view', 'assistant:market:view'
);

-- ==================================================
-- 7. 会员角色关联数据 (a_member_role)
-- ==================================================

-- 管理员角色
INSERT INTO a_member_role (member_id, role_id) VALUES
(1, 1); -- admin用户 -> 超级管理员角色

-- 普通会员角色分配
INSERT INTO a_member_role (member_id, role_id) VALUES
(2, 6),  -- user001 -> 普通用户
(3, 5),  -- user002 -> VIP用户
(4, 5),  -- user003 -> VIP用户
(5, 4),  -- user004 -> 企业用户
(6, 6),  -- user005 -> 普通用户
(7, 5),  -- user006 -> VIP用户
(8, 4),  -- user007 -> 企业用户
(9, 6),  -- user008 -> 普通用户
(10, 5), -- user009 -> VIP用户
(11, 4), -- user010 -> 企业用户
(12, 6), -- user011 -> 普通用户（待激活）
(13, 6); -- user012 -> 普通用户（禁用）

-- 访客用户角色
INSERT INTO a_member_role (member_id, role_id) VALUES
(14, 7), -- guest001 -> 访客用户
(15, 7); -- guest002 -> 访客用户

-- ==================================================
-- 数据插入完成
-- ==================================================
