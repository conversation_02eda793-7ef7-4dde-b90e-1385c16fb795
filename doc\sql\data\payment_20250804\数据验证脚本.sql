-- ====================================================================================
-- AIShowLab - 支付网关模块数据验证脚本
-- 文件名: 数据验证脚本.sql
-- 作者: AI Assistant
-- 描述: 验证支付网关模块模拟数据的完整性和正确性
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. 支付网关模块数据统计
-- ==================================================

-- 1.1 支付网关状态分布
SELECT 
    '支付网关状态分布' as 检查项目,
    CASE pg.status
        WHEN 1 THEN '启用'
        WHEN 2 THEN '禁用'
        WHEN 3 THEN '维护中'
        ELSE '未知'
    END as 状态,
    COUNT(*) as 数量,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as 占比
FROM a_payment_gateway pg
WHERE pg.delete_time IS NULL
GROUP BY pg.status
ORDER BY pg.status;

-- 1.2 支付网关地区覆盖统计
SELECT 
    '支付网关地区覆盖' as 检查项目,
    pg.name as 支付网关名称,
    pg.code as 网关编码,
    COUNT(pgc.country_id) as 支持国家数量,
    pg.status as 状态
FROM a_payment_gateway pg
LEFT JOIN a_payment_gateway_country pgc ON pg.id = pgc.gateway_id AND pgc.delete_time IS NULL
WHERE pg.delete_time IS NULL
GROUP BY pg.id, pg.name, pg.code, pg.status
ORDER BY COUNT(pgc.country_id) DESC;

-- 1.3 各大洲支付网关覆盖情况
SELECT 
    '各大洲支付网关覆盖' as 检查项目,
    c.continent as 大洲,
    COUNT(DISTINCT c.id) as 国家总数,
    COUNT(DISTINCT pgc.country_id) as 有支付网关的国家数,
    ROUND(COUNT(DISTINCT pgc.country_id) * 100.0 / COUNT(DISTINCT c.id), 2) as 覆盖率,
    COUNT(DISTINCT pgc.gateway_id) as 可用支付网关数量
FROM a_base_country c
LEFT JOIN a_payment_gateway_country pgc ON c.id = pgc.country_id AND pgc.delete_time IS NULL
LEFT JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.status = 1 AND pg.delete_time IS NULL
WHERE c.is_enabled = true
GROUP BY c.continent
ORDER BY 覆盖率 DESC;

-- 1.4 热门国家支付网关配置
SELECT 
    '热门国家支付网关' as 检查项目,
    c.name_native as 国家名称,
    c.iso2_code as 国家代码,
    COUNT(pgc.gateway_id) as 可用支付网关数量,
    STRING_AGG(pg.name, ', ' ORDER BY pg.sort_order) as 支付网关列表
FROM a_base_country c
LEFT JOIN a_payment_gateway_country pgc ON c.id = pgc.country_id AND pgc.delete_time IS NULL
LEFT JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.status = 1 AND pg.delete_time IS NULL
WHERE c.iso2_code IN ('CN', 'US', 'JP', 'GB', 'FR', 'DE', 'KR', 'SG', 'AU', 'CA', 'IN', 'BR')
GROUP BY c.id, c.name_native, c.iso2_code
ORDER BY COUNT(pgc.gateway_id) DESC;

-- 1.5 支付网关配置参数统计
SELECT 
    '支付网关配置参数' as 检查项目,
    pg.name as 支付网关名称,
    CASE 
        WHEN pg.config_params::text LIKE '%sandbox%' THEN '包含沙箱配置'
        ELSE '生产环境配置'
    END as 环境类型,
    CASE 
        WHEN pg.config_params::text LIKE '%notify_url%' THEN '包含回调配置'
        ELSE '无回调配置'
    END as 回调配置,
    CASE 
        WHEN pg.config_params::text LIKE '%webhook%' THEN '包含Webhook配置'
        ELSE '无Webhook配置'
    END as Webhook配置,
    LENGTH(pg.config_params::text) as 配置参数长度
FROM a_payment_gateway pg
WHERE pg.delete_time IS NULL
ORDER BY pg.sort_order;

-- ==================================================
-- 2. 数据完整性检查
-- ==================================================

-- 2.1 检查支付网关是否都有国家映射
SELECT 
    '数据完整性检查' as 检查项目,
    '支付网关国家映射检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_payment_gateway pg
LEFT JOIN a_payment_gateway_country pgc ON pg.id = pgc.gateway_id AND pgc.delete_time IS NULL
WHERE pg.delete_time IS NULL AND pg.status = 1 AND pgc.id IS NULL;

-- 2.2 检查国家映射是否都有对应的支付网关
SELECT 
    '数据完整性检查' as 检查项目,
    '国家映射支付网关检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_payment_gateway_country pgc
LEFT JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.delete_time IS NULL
WHERE pgc.delete_time IS NULL AND pg.id IS NULL;

-- 2.3 检查国家映射是否都有对应的国家
SELECT 
    '数据完整性检查' as 检查项目,
    '国家映射国家检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_payment_gateway_country pgc
LEFT JOIN a_base_country c ON pgc.country_id = c.id
WHERE pgc.delete_time IS NULL AND c.id IS NULL;

-- 2.4 检查是否有重复的支付网关编码
SELECT 
    '数据完整性检查' as 检查项目,
    '重复支付网关编码检查' as 检查内容,
    COUNT(*) - COUNT(DISTINCT code) as 问题数量
FROM a_payment_gateway
WHERE delete_time IS NULL;

-- ==================================================
-- 3. 业务逻辑检查
-- ==================================================

-- 3.1 检查是否有支付网关没有配置参数
SELECT 
    '业务逻辑检查' as 检查项目,
    '支付网关配置参数检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_payment_gateway pg
WHERE pg.delete_time IS NULL AND pg.status = 1 AND (pg.config_params IS NULL OR pg.config_params::text = '{}');

-- 3.2 检查是否有国家没有任何支付网关支持
SELECT 
    '业务逻辑检查' as 检查项目,
    '无支付网关支持的国家检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_base_country c
LEFT JOIN a_payment_gateway_country pgc ON c.id = pgc.country_id AND pgc.delete_time IS NULL
LEFT JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.status = 1 AND pg.delete_time IS NULL
WHERE c.is_enabled = true AND pg.id IS NULL;

-- 3.3 检查主要国家是否有足够的支付网关选择
SELECT 
    '业务逻辑检查' as 检查项目,
    '主要国家支付网关数量检查' as 检查内容,
    COUNT(*) as 问题数量
FROM (
    SELECT c.id, c.name_native, COUNT(pgc.gateway_id) as gateway_count
    FROM a_base_country c
    LEFT JOIN a_payment_gateway_country pgc ON c.id = pgc.country_id AND pgc.delete_time IS NULL
    LEFT JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.status = 1 AND pg.delete_time IS NULL
    WHERE c.iso2_code IN ('CN', 'US', 'JP', 'GB', 'FR', 'DE', 'KR', 'SG', 'AU', 'CA')
    GROUP BY c.id, c.name_native
    HAVING COUNT(pgc.gateway_id) < 2
) insufficient_gateways;

-- ==================================================
-- 4. 支付网关功能特性分析
-- ==================================================

-- 4.1 支付网关类型分析
SELECT 
    '支付网关功能特性' as 检查项目,
    CASE 
        WHEN pg.code IN ('wechat_pay', 'alipay', 'unionpay') THEN '国内支付'
        WHEN pg.code IN ('paypal', 'stripe', 'apple_pay', 'google_pay') THEN '国际支付'
        WHEN pg.code IN ('line_pay', 'kakao_pay', 'grab_pay', 'upi', 'pix') THEN '地区特色支付'
        WHEN pg.code IN ('sepa') THEN '银行转账'
        WHEN pg.code IN ('test_gateway') THEN '测试支付'
        ELSE '其他'
    END as 支付类型,
    COUNT(*) as 数量,
    AVG(LENGTH(pg.config_params::text)) as 平均配置复杂度
FROM a_payment_gateway pg
WHERE pg.delete_time IS NULL
GROUP BY 
    CASE 
        WHEN pg.code IN ('wechat_pay', 'alipay', 'unionpay') THEN '国内支付'
        WHEN pg.code IN ('paypal', 'stripe', 'apple_pay', 'google_pay') THEN '国际支付'
        WHEN pg.code IN ('line_pay', 'kakao_pay', 'grab_pay', 'upi', 'pix') THEN '地区特色支付'
        WHEN pg.code IN ('sepa') THEN '银行转账'
        WHEN pg.code IN ('test_gateway') THEN '测试支付'
        ELSE '其他'
    END
ORDER BY COUNT(*) DESC;

-- ==================================================
-- 5. 总体数据概览
-- ==================================================
SELECT 
    '总体数据概览' as 检查项目,
    '支付网关' as 数据类型,
    COUNT(*) as 记录数
FROM a_payment_gateway
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '支付网关国家映射' as 数据类型,
    COUNT(*) as 记录数
FROM a_payment_gateway_country
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '启用的支付网关' as 数据类型,
    COUNT(*) as 记录数
FROM a_payment_gateway
WHERE delete_time IS NULL AND status = 1
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '有支付网关的国家' as 数据类型,
    COUNT(DISTINCT pgc.country_id) as 记录数
FROM a_payment_gateway_country pgc
JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.status = 1 AND pg.delete_time IS NULL
WHERE pgc.delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '总映射关系数' as 数据类型,
    COUNT(*) as 记录数
FROM a_payment_gateway_country pgc
JOIN a_payment_gateway pg ON pgc.gateway_id = pg.id AND pg.status = 1 AND pg.delete_time IS NULL
WHERE pgc.delete_time IS NULL
ORDER BY 数据类型;
