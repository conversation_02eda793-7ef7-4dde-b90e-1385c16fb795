import request from '@/utils/request'

// 查询模型调用响应日志列表
export function listLogModelResponse(query) {
  return request({
    url: '/system/log/model/response/list',
    method: 'get',
    params: query
  })
}

// 查询模型调用响应日志详细
export function getLogModelResponse(id) {
  return request({
    url: '/system/log/model/response/' + id,
    method: 'get'
  })
}

// 新增模型调用响应日志
export function addLogModelResponse(data) {
  return request({
    url: '/system/log/model/response',
    method: 'post',
    data: data
  })
}

// 修改模型调用响应日志
export function updateLogModelResponse(data) {
  return request({
    url: '/system/log/model/response',
    method: 'put',
    data: data
  })
}

// 删除模型调用响应日志
export function delLogModelResponse(id) {
  return request({
    url: '/system/log/model/response/' + id,
    method: 'delete'
  })
}
