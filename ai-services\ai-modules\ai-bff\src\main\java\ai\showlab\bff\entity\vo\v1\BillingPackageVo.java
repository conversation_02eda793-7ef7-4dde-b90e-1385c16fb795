package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 计费套餐视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingPackageVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 套餐唯一标识
     */
    private String code;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 套餐详细描述
     */
    private String description;

    /**
     * 套餐类型
     */
    private Integer type;
    
    /**
     * 套餐类型描述
     */
    private String typeDesc;

    /**
     * 套餐标价
     */
    private BigDecimal price;

    /**
     * 货币符号 (如 ¥, $)
     */
    private String currencySymbol;
    
    /**
     * 货币代码 (如 CNY, USD)
     */
    private String currencyCode;

    /**
     * 购买后授予的点数/积分/Token数量
     */
    private BigDecimal creditsGranted;

    /**
     * 授予的点数/积分的有效期（天）
     */
    private Integer validityDays;
    
    /**
     * 有效期描述（如：永久有效、30天有效）
     */
    private String validityDesc;

    /**
     * (针对订阅类型) 续订周期单位
     */
    private Integer renewalIntervalUnit;
    
    /**
     * 续订周期描述（如：月度订阅、年度订阅）
     */
    private String renewalIntervalDesc;

    /**
     * 购买此套餐后授予的会员等级
     */
    private Integer memberLevelGrant;
    
    /**
     * 会员等级描述
     */
    private String memberLevelDesc;

    /**
     * 套餐在前端的展示排序
     */
    private Integer sortOrder;
    
    /**
     * 是否为推荐套餐
     */
    private Boolean isRecommended;
    
    /**
     * 是否为热门套餐
     */
    private Boolean isPopular;
}
