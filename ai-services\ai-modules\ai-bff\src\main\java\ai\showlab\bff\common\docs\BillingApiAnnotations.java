package ai.showlab.bff.common.docs;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 计费相关接口的文档注解
 * 
 * <AUTHOR>
 */
public class BillingApiAnnotations {

    /**
     * 定义常用API响应的JSON示例常量，避免重复编写和提高可维护性。
     */
    public interface ExampleResponses {
        String SUCCESS_NULL_DATA = "{\"code\": 200, \"msg\": \"操作成功\", \"data\": null}";
        String UNAUTHORIZED = "{\"code\": 401, \"msg\": \"未授权\"}";
        String INTERNAL_SERVER_ERROR = "{\"code\": 500, \"msg\": \"服务器内部错误，请稍后重试\"}";
        String BAD_REQUEST_PACKAGE_ID_EMPTY = "{\"code\": 400, \"msg\": \"套餐ID不能为空\"}";
        String PACKAGE_NOT_FOUND = "{\"code\": 404, \"msg\": \"套餐不存在\"}";
        String INSUFFICIENT_BALANCE = "{\"code\": 400, \"msg\": \"余额不足\"}";

        // 套餐列表相关
        String GET_PACKAGE_LIST_SUCCESS = """
                {
                  "code": 200,
                  "msg": "获取套餐列表成功",
                  "data": {
                    "total": 10,
                    "pageNum": 1,
                    "pageSize": 20,
                    "pages": 1,
                    "list": [
                      {
                        "id": 1,
                        "code": "basic_monthly",
                        "name": "基础月度套餐",
                        "description": "适合轻度使用的用户",
                        "type": 2,
                        "typeDesc": "订阅",
                        "price": 29.99,
                        "currencySymbol": "¥",
                        "currencyCode": "CNY",
                        "creditsGranted": 1000.0000,
                        "validityDays": 30,
                        "validityDesc": "30天有效",
                        "renewalIntervalUnit": 2,
                        "renewalIntervalDesc": "月度订阅",
                        "memberLevelGrant": 2,
                        "memberLevelDesc": "VIP版",
                        "sortOrder": 1,
                        "isRecommended": true,
                        "isPopular": false
                      }
                    ],
                    "hasNext": false,
                    "hasPrevious": false
                  }
                }
                """;

        // 创建订单相关
        String CREATE_ORDER_SUCCESS = """
                {
                  "code": 200,
                  "msg": "订单创建成功",
                  "data": {
                    "id": 123,
                    "orderNo": "ORD_20240727123456789",
                    "packageName": "基础月度套餐",
                    "amount": 29.99,
                    "currencySymbol": "¥",
                    "status": 1,
                    "statusDesc": "待支付",
                    "paidTime": null,
                    "createTime": "2024-07-27T10:30:00+08:00"
                  }
                }
                """;

        // 订单列表相关
        String GET_ORDER_LIST_SUCCESS = """
                {
                  "code": 200,
                  "msg": "获取订单列表成功",
                  "data": {
                    "total": 5,
                    "pageNum": 1,
                    "pageSize": 20,
                    "pages": 1,
                    "list": [
                      {
                        "id": 123,
                        "orderNo": "ORD_20240727123456789",
                        "packageName": "基础月度套餐",
                        "amount": 29.99,
                        "currencySymbol": "¥",
                        "status": 2,
                        "statusDesc": "已完成",
                        "paidTime": "2024-07-27T10:35:00+08:00",
                        "createTime": "2024-07-27T10:30:00+08:00"
                      }
                    ],
                    "hasNext": false,
                    "hasPrevious": false
                  }
                }
                """;

        // 余额信息相关
        String GET_BALANCE_SUCCESS = """
                {
                  "code": 200,
                  "msg": "获取余额信息成功",
                  "data": {
                    "id": 1,
                    "memberId": 100,
                    "balance": 156.78,
                    "frozenAmount": 0.00,
                    "totalBalance": 156.78,
                    "currencySymbol": "¥",
                    "currencyCode": "CNY",
                    "lowThreshold": 10.00,
                    "isLowBalance": false,
                    "balanceStatusDesc": "余额充足"
                  }
                }
                """;

        // 交易记录相关
        String GET_TRANSACTION_LIST_SUCCESS = """
                {
                  "code": 200,
                  "msg": "获取交易记录成功",
                  "data": {
                    "total": 15,
                    "pageNum": 1,
                    "pageSize": 20,
                    "pages": 1,
                    "list": [
                      {
                        "id": 456,
                        "type": 1,
                        "typeDesc": "消费",
                        "amount": -2.50,
                        "currencySymbol": "¥",
                        "description": "使用模型: gpt-4-turbo",
                        "transactionTime": "2024-07-27T14:20:00+08:00"
                      }
                    ],
                    "hasNext": false,
                    "hasPrevious": false
                  }
                }
                """;

        // 使用记录相关
        String GET_USAGE_LIST_SUCCESS = """
                {
                  "code": 200,
                  "msg": "获取使用记录成功",
                  "data": {
                    "total": 25,
                    "pageNum": 1,
                    "pageSize": 20,
                    "pages": 2,
                    "list": [
                      {
                        "id": 789,
                        "modelId": 10,
                        "modelName": "GPT-4 Turbo",
                        "modelCode": "gpt-4-turbo",
                        "planId": 1,
                        "planName": "按Token计费",
                        "unit": 1,
                        "unitDesc": "Token",
                        "amount": 1250.0000,
                        "durationMs": 1200,
                        "durationDesc": "1.2秒",
                        "resultSize": 2048,
                        "resultSizeDesc": "2.0KB",
                        "usedTime": "2024-07-27T14:20:00+08:00",
                        "cost": 2.50,
                        "currencySymbol": "¥"
                      }
                    ],
                    "hasNext": true,
                    "hasPrevious": false
                  }
                }
                """;

        // 计费方案相关
        String GET_PLAN_LIST_SUCCESS = """
                {
                  "code": 200,
                  "msg": "获取计费方案成功",
                  "data": [
                    {
                      "id": 1,
                      "name": "按Token计费",
                      "unit": 1,
                      "unitDesc": "Token",
                      "sortOrder": 1,
                      "description": "按照模型处理的Token数量进行计费"
                    },
                    {
                      "id": 2,
                      "name": "按次计费",
                      "unit": 2,
                      "unitDesc": "次",
                      "sortOrder": 2,
                      "description": "按照调用次数进行计费"
                    }
                  ]
                }
                """;

        // 错误响应
        String GET_PACKAGE_LIST_FAILED = "{\"code\": 500, \"msg\": \"获取套餐列表失败，请稍后重试\"}";
        String CREATE_ORDER_FAILED = "{\"code\": 500, \"msg\": \"订单创建失败，请稍后重试\"}";
        String GET_ORDER_LIST_FAILED = "{\"code\": 500, \"msg\": \"获取订单列表失败，请稍后重试\"}";
        String GET_BALANCE_FAILED = "{\"code\": 500, \"msg\": \"获取余额信息失败，请稍后重试\"}";
        String GET_TRANSACTION_LIST_FAILED = "{\"code\": 500, \"msg\": \"获取交易记录失败，请稍后重试\"}";
        String GET_USAGE_LIST_FAILED = "{\"code\": 500, \"msg\": \"获取使用记录失败，请稍后重试\"}";
        String GET_PLAN_LIST_FAILED = "{\"code\": 500, \"msg\": \"获取计费方案失败，请稍后重试\"}";
    }

    @Operation(summary = "获取计费套餐列表", description = "获取所有可用的计费套餐，支持分页和筛选。此接口无需认证，任何用户都可以查看可用套餐。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取套餐列表成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_PACKAGE_LIST_SUCCESS))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，获取套餐列表失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_PACKAGE_LIST_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface GetPackageListApiDoc {}

    @Operation(summary = "创建订单", description = "为当前登录会员创建套餐购买订单。需要有效的JWT Token和有效的套餐ID。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "订单创建成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.CREATE_ORDER_SUCCESS))),
            @ApiResponse(responseCode = "400", description = "请求参数校验失败：套餐ID不能为空",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.BAD_REQUEST_PACKAGE_ID_EMPTY))),
            @ApiResponse(responseCode = "401", description = "未授权：JWT Token 无效、缺失或已过期",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.UNAUTHORIZED))),
            @ApiResponse(responseCode = "404", description = "套餐不存在",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.PACKAGE_NOT_FOUND))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，订单创建失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.CREATE_ORDER_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface CreateOrderApiDoc {}

    @Operation(summary = "获取订单列表", description = "获取当前登录会员的订单列表，支持分页和筛选。需要有效的JWT Token。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取订单列表成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_ORDER_LIST_SUCCESS))),
            @ApiResponse(responseCode = "401", description = "未授权：JWT Token 无效、缺失或已过期",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.UNAUTHORIZED))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，获取订单列表失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_ORDER_LIST_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface GetOrderListApiDoc {}

    @Operation(summary = "获取余额信息", description = "获取当前登录会员的账户余额信息。需要有效的JWT Token。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取余额信息成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_BALANCE_SUCCESS))),
            @ApiResponse(responseCode = "401", description = "未授权：JWT Token 无效、缺失或已过期",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.UNAUTHORIZED))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，获取余额信息失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_BALANCE_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface GetBalanceApiDoc {}

    @Operation(summary = "获取交易记录列表", description = "获取当前登录会员的交易记录列表，支持分页和筛选。需要有效的JWT Token。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取交易记录成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_TRANSACTION_LIST_SUCCESS))),
            @ApiResponse(responseCode = "401", description = "未授权：JWT Token 无效、缺失或已过期",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.UNAUTHORIZED))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，获取交易记录失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_TRANSACTION_LIST_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface GetTransactionListApiDoc {}

    @Operation(summary = "获取使用记录列表", description = "获取当前登录会员的模型使用记录列表，支持分页和筛选。需要有效的JWT Token。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取使用记录成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_USAGE_LIST_SUCCESS))),
            @ApiResponse(responseCode = "401", description = "未授权：JWT Token 无效、缺失或已过期",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.UNAUTHORIZED))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，获取使用记录失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_USAGE_LIST_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface GetUsageListApiDoc {}

    @Operation(summary = "获取计费方案列表", description = "获取所有可用的计费方案列表。此接口无需认证，任何用户都可以查看。")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取计费方案成功",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_PLAN_LIST_SUCCESS))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误，获取计费方案失败，请稍后重试",
                    content = @Content(mediaType = "application/json",
                            examples = @ExampleObject(value = ExampleResponses.GET_PLAN_LIST_FAILED)))
    })
    @Target(ElementType.METHOD)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface GetPlanListApiDoc {}
}
