import request from '@/utils/request'

// 查询会员权限列表
export function listMemberPermission(query) {
  return request({
    url: '/system/member/permissions/list',
    method: 'get',
    params: query
  })
}

// 查询会员权限详细
export function getMemberPermission(id) {
  return request({
    url: '/system/member/permissions/' + id,
    method: 'get'
  })
}

// 新增会员权限
export function addMemberPermission(data) {
  return request({
    url: '/system/member/permissions',
    method: 'post',
    data: data
  })
}

// 修改会员权限
export function updateMemberPermission(data) {
  return request({
    url: '/system/member/permissions',
    method: 'put',
    data: data
  })
}

// 删除会员权限
export function delMemberPermission(id) {
  return request({
    url: '/system/member/permissions/' + id,
    method: 'delete'
  })
}
