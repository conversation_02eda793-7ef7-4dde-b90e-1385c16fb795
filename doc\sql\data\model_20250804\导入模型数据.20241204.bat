@echo off
REM ====================================================================================
REM AIShowLab - 模型管理数据导入脚本 (Windows版本)
REM 文件名: 导入模型数据.20241204.bat
REM 作者: AI Assistant
REM 描述: 用于导入模型管理模拟数据的Windows批处理脚本
REM 创建时间: 2024-12-04
REM ====================================================================================

setlocal enabledelayedexpansion

REM 默认配置
set DEFAULT_DB_HOST=localhost
set DEFAULT_DB_PORT=5432
set DEFAULT_DB_NAME=aishowlab
set DEFAULT_DB_USER=postgres

REM 设置默认值
set DB_HOST=%DEFAULT_DB_HOST%
set DB_PORT=%DEFAULT_DB_PORT%
set DB_NAME=%DEFAULT_DB_NAME%
set DB_USER=%DEFAULT_DB_USER%
set DB_PASSWORD=
set DRY_RUN=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-h" (
    set DB_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--host" (
    set DB_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-p" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set DB_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--database" (
    set DB_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-u" (
    set DB_USER=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--user" (
    set DB_USER=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-w" (
    set DB_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--password" (
    set DB_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--dry-run" (
    set DRY_RUN=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
echo 未知参数: %~1
goto :show_help

:args_done

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set SQL_FILE=%SCRIPT_DIR%模型管理模拟数据.20241204.sql
set VALIDATION_FILE=%SCRIPT_DIR%验证模型数据.20241204.sql

REM 检查SQL文件是否存在
if not exist "%SQL_FILE%" (
    echo [错误] 找不到SQL文件: %SQL_FILE%
    exit /b 1
)

REM 构建psql连接字符串
set PSQL_CONN=postgresql://%DB_USER%
if not "%DB_PASSWORD%"=="" (
    set PSQL_CONN=!PSQL_CONN!:%DB_PASSWORD%
)
set PSQL_CONN=!PSQL_CONN!@%DB_HOST%:%DB_PORT%/%DB_NAME%

echo ====================================================
echo AIShowLab 模型管理数据导入脚本
echo ====================================================
echo 数据库连接信息:
echo   主机: %DB_HOST%
echo   端口: %DB_PORT%
echo   数据库: %DB_NAME%
echo   用户: %DB_USER%
echo   SQL文件: %SQL_FILE%

if "%DRY_RUN%"=="true" (
    echo   模式: 仅验证SQL语法（不执行导入）
) else (
    echo   模式: 执行数据导入
)

echo ====================================================

REM 验证数据库连接
echo 正在验证数据库连接...
psql "%PSQL_CONN%" -c "SELECT version();" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法连接到数据库
    echo 请检查连接参数或确保数据库服务正在运行
    exit /b 1
)
echo [成功] 数据库连接成功

REM 检查必要的表是否存在
echo 正在检查数据库表结构...
set TABLES=a_model_category a_model_provider a_model a_model_feature a_model_output_format a_model_api_key a_model_visibility

for %%t in (%TABLES%) do (
    psql "%PSQL_CONN%" -c "SELECT 1 FROM %%t LIMIT 1;" >nul 2>&1
    if errorlevel 1 (
        echo [错误] 表 %%t 不存在
        echo 请先执行数据库架构脚本创建表结构
        exit /b 1
    )
)
echo [成功] 所有必要的表都存在

if "%DRY_RUN%"=="true" (
    REM 仅验证SQL语法
    echo 正在验证SQL语法...
    psql "%PSQL_CONN%" -f "%SQL_FILE%" --single-transaction --set ON_ERROR_STOP=on --echo-errors >nul 2>&1
    if errorlevel 1 (
        echo [失败] SQL语法验证失败
        exit /b 1
    ) else (
        echo [成功] SQL语法验证通过
    )
) else (
    REM 执行数据导入
    echo 正在导入模型管理数据...
    
    psql "%PSQL_CONN%" -f "%SQL_FILE%" --single-transaction --set ON_ERROR_STOP=on
    if errorlevel 1 (
        echo [失败] 数据导入失败
        exit /b 1
    ) else (
        echo [成功] 数据导入成功
        
        REM 执行验证脚本（如果存在）
        if exist "%VALIDATION_FILE%" (
            echo 正在执行数据验证...
            psql "%PSQL_CONN%" -f "%VALIDATION_FILE%" > "%TEMP%\validation_result.txt" 2>&1
            echo [成功] 数据验证完成，结果已保存到 %TEMP%\validation_result.txt
        )
    )
)

echo ====================================================
echo 操作完成！

if not "%DRY_RUN%"=="true" (
    echo.
    echo 导入的数据包括:
    echo   - 6个模型分类
    echo   - 12个模型供应商
    echo   - 28个AI模型（LLM、图像生成、视频生成、多模态）
    echo   - 模型特性配置
    echo   - 输出格式定义
    echo   - API密钥示例
    echo   - 可见性权限控制
    echo.
    echo 建议执行以下查询验证数据:
    echo   SELECT c.name, COUNT(m.id) FROM a_model_category c LEFT JOIN a_model m ON c.id = m.category_id GROUP BY c.name;
)

echo ====================================================
goto :eof

:show_help
echo AIShowLab 模型管理数据导入脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -h, --host HOST        数据库主机地址 (默认: %DEFAULT_DB_HOST%)
echo   -p, --port PORT        数据库端口 (默认: %DEFAULT_DB_PORT%)
echo   -d, --database DB      数据库名称 (默认: %DEFAULT_DB_NAME%)
echo   -u, --user USER        数据库用户名 (默认: %DEFAULT_DB_USER%)
echo   -w, --password PASS    数据库密码 (可选，建议使用 .pgpass 文件)
echo   --dry-run              仅验证SQL语法，不执行导入
echo   --help                 显示此帮助信息
echo.
echo 示例:
echo   %~nx0 -h localhost -d aishowlab -u postgres
echo   %~nx0 --dry-run  # 仅验证SQL语法
echo.
exit /b 0
