package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MemberTypeEnum {
    
    /** 普通会员 */
    NORMAL(1, "普通会员"),
    
    /** 管理员 */
    ADMIN(2, "管理员"),
    
    /** 访客 */
    VISITOR(3, "访客");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static MemberTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MemberTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 