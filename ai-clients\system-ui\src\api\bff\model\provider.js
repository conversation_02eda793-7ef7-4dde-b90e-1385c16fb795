import request from '@/utils/request'

// 查询模型供应商列表
export function listModelProvider(query) {
  return request({
    url: '/system/model/provider/list',
    method: 'get',
    params: query
  })
}

// 查询模型供应商详细
export function getModelProvider(id) {
  return request({
    url: '/system/model/provider/' + id,
    method: 'get'
  })
}

// 新增模型供应商
export function addModelProvider(data) {
  return request({
    url: '/system/model/provider',
    method: 'post',
    data: data
  })
}

// 修改模型供应商
export function updateModelProvider(data) {
  return request({
    url: '/system/model/provider',
    method: 'put',
    data: data
  })
}

// 删除模型供应商
export function delModelProvider(id) {
  return request({
    url: '/system/model/provider/' + id,
    method: 'delete'
  })
}
