#!/bin/bash

# ====================================================================================
# AIShowLab - 会员功能和前端菜单权限模块数据导入脚本
# 文件名: 导入数据脚本.sh
# 作者: AI Assistant
# 描述: 用于导入会员功能和前端菜单权限模块模拟数据的便捷脚本
# 创建时间: 2025-08-04
# ====================================================================================

# 设置脚本参数
set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DB_NAME="aishowlab"
DEFAULT_DB_USER="postgres"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：打印帮助信息
print_help() {
    echo "AIShowLab 会员功能和前端菜单权限模块数据导入脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST        数据库主机地址 (默认: $DEFAULT_DB_HOST)"
    echo "  -p, --port PORT        数据库端口 (默认: $DEFAULT_DB_PORT)"
    echo "  -d, --database DB      数据库名称 (默认: $DEFAULT_DB_NAME)"
    echo "  -u, --user USER        数据库用户名 (默认: $DEFAULT_DB_USER)"
    echo "  -w, --password PASS    数据库密码 (可选，建议使用 .pgpass 文件)"
    echo "  --member-only          仅导入会员功能模块数据"
    echo "  --permission-only      仅导入前端菜单权限模块数据"
    echo "  --dry-run              仅验证SQL语法，不执行导入"
    echo "  --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -h localhost -d aishowlab -u postgres"
    echo "  $0 --member-only       # 仅导入会员功能数据"
    echo "  $0 --permission-only   # 仅导入权限数据"
    echo "  $0 --dry-run           # 仅验证SQL语法"
    echo ""
}

# 解析命令行参数
DB_HOST=$DEFAULT_DB_HOST
DB_PORT=$DEFAULT_DB_PORT
DB_NAME=$DEFAULT_DB_NAME
DB_USER=$DEFAULT_DB_USER
DB_PASSWORD=""
DRY_RUN=false
MEMBER_ONLY=false
PERMISSION_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -p|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -w|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --member-only)
            MEMBER_ONLY=true
            shift
            ;;
        --permission-only)
            PERMISSION_ONLY=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            print_help
            exit 0
            ;;
        *)
            print_message $RED "未知参数: $1"
            print_help
            exit 1
            ;;
    esac
done

# 检查互斥参数
if [[ "$MEMBER_ONLY" == true && "$PERMISSION_ONLY" == true ]]; then
    print_message $RED "错误: --member-only 和 --permission-only 不能同时使用"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MEMBER_SQL="$SCRIPT_DIR/会员功能模拟数据.sql"
PERMISSION_SQL="$SCRIPT_DIR/前端菜单权限模拟数据.sql"
VALIDATION_SQL="$SCRIPT_DIR/数据验证脚本.sql"

# 检查SQL文件是否存在
if [[ "$PERMISSION_ONLY" != true && ! -f "$MEMBER_SQL" ]]; then
    print_message $RED "错误: 找不到会员功能数据文件: $MEMBER_SQL"
    exit 1
fi

if [[ "$MEMBER_ONLY" != true && ! -f "$PERMISSION_SQL" ]]; then
    print_message $RED "错误: 找不到权限数据文件: $PERMISSION_SQL"
    exit 1
fi

# 构建psql连接字符串
PSQL_CONN="postgresql://$DB_USER"
if [[ -n "$DB_PASSWORD" ]]; then
    PSQL_CONN="$PSQL_CONN:$DB_PASSWORD"
fi
PSQL_CONN="$PSQL_CONN@$DB_HOST:$DB_PORT/$DB_NAME"

print_message $BLUE "===================================================="
print_message $BLUE "AIShowLab 会员功能和前端菜单权限模块数据导入脚本"
print_message $BLUE "===================================================="
print_message $YELLOW "数据库连接信息:"
print_message $YELLOW "  主机: $DB_HOST"
print_message $YELLOW "  端口: $DB_PORT"
print_message $YELLOW "  数据库: $DB_NAME"
print_message $YELLOW "  用户: $DB_USER"

if [[ "$DRY_RUN" == true ]]; then
    print_message $YELLOW "  模式: 仅验证SQL语法（不执行导入）"
elif [[ "$MEMBER_ONLY" == true ]]; then
    print_message $YELLOW "  模式: 仅导入会员功能模块数据"
    print_message $YELLOW "  SQL文件: $MEMBER_SQL"
elif [[ "$PERMISSION_ONLY" == true ]]; then
    print_message $YELLOW "  模式: 仅导入前端菜单权限模块数据"
    print_message $YELLOW "  SQL文件: $PERMISSION_SQL"
else
    print_message $YELLOW "  模式: 导入所有模块数据"
    print_message $YELLOW "  会员功能文件: $MEMBER_SQL"
    print_message $YELLOW "  权限文件: $PERMISSION_SQL"
fi

print_message $BLUE "===================================================="

# 验证数据库连接
print_message $YELLOW "正在验证数据库连接..."
if ! psql "$PSQL_CONN" -c "SELECT version();" > /dev/null 2>&1; then
    print_message $RED "错误: 无法连接到数据库"
    print_message $RED "请检查连接参数或确保数据库服务正在运行"
    exit 1
fi
print_message $GREEN "✓ 数据库连接成功"

# 检查必要的表是否存在
print_message $YELLOW "正在检查数据库表结构..."

# 会员功能相关表
if [[ "$PERMISSION_ONLY" != true ]]; then
    MEMBER_TABLES=("a_member" "a_member_auth" "a_member_session")
    for table in "${MEMBER_TABLES[@]}"; do
        if ! psql "$PSQL_CONN" -c "SELECT 1 FROM $table LIMIT 1;" > /dev/null 2>&1; then
            print_message $RED "错误: 会员功能表 $table 不存在"
            print_message $RED "请先执行数据库架构脚本创建表结构"
            exit 1
        fi
    done
    print_message $GREEN "✓ 会员功能相关表都存在"
fi

# 权限相关表
if [[ "$MEMBER_ONLY" != true ]]; then
    PERMISSION_TABLES=("a_func_menu" "a_func_permission" "a_func_permission_group" "a_func_role" "a_func_role_permission" "a_member_role")
    for table in "${PERMISSION_TABLES[@]}"; do
        if ! psql "$PSQL_CONN" -c "SELECT 1 FROM $table LIMIT 1;" > /dev/null 2>&1; then
            print_message $RED "错误: 权限表 $table 不存在"
            print_message $RED "请先执行数据库架构脚本创建表结构"
            exit 1
        fi
    done
    print_message $GREEN "✓ 权限相关表都存在"
fi

if [[ "$DRY_RUN" == true ]]; then
    # 仅验证SQL语法
    print_message $YELLOW "正在验证SQL语法..."
    
    if [[ "$PERMISSION_ONLY" != true ]]; then
        if ! psql "$PSQL_CONN" --single-transaction --set ON_ERROR_STOP=on -f "$MEMBER_SQL" --echo-errors > /dev/null 2>&1; then
            print_message $RED "✗ 会员功能数据SQL语法验证失败"
            exit 1
        fi
        print_message $GREEN "✓ 会员功能数据SQL语法验证通过"
    fi
    
    if [[ "$MEMBER_ONLY" != true ]]; then
        if ! psql "$PSQL_CONN" --single-transaction --set ON_ERROR_STOP=on -f "$PERMISSION_SQL" --echo-errors > /dev/null 2>&1; then
            print_message $RED "✗ 权限数据SQL语法验证失败"
            exit 1
        fi
        print_message $GREEN "✓ 权限数据SQL语法验证通过"
    fi
else
    # 执行数据导入
    if [[ "$PERMISSION_ONLY" != true ]]; then
        print_message $YELLOW "正在导入会员功能模块数据..."
        if psql "$PSQL_CONN" -f "$MEMBER_SQL" --single-transaction --set ON_ERROR_STOP=on; then
            print_message $GREEN "✓ 会员功能数据导入成功"
        else
            print_message $RED "✗ 会员功能数据导入失败"
            exit 1
        fi
    fi
    
    if [[ "$MEMBER_ONLY" != true ]]; then
        print_message $YELLOW "正在导入前端菜单权限模块数据..."
        if psql "$PSQL_CONN" -f "$PERMISSION_SQL" --single-transaction --set ON_ERROR_STOP=on; then
            print_message $GREEN "✓ 权限数据导入成功"
        else
            print_message $RED "✗ 权限数据导入失败"
            exit 1
        fi
    fi
    
    # 执行验证脚本（如果存在）
    if [[ -f "$VALIDATION_SQL" ]]; then
        print_message $YELLOW "正在执行数据验证..."
        psql "$PSQL_CONN" -f "$VALIDATION_SQL" > /tmp/member_validation_result_$(date +%Y%m%d_%H%M%S).txt 2>&1
        print_message $GREEN "✓ 数据验证完成，结果已保存到 /tmp/member_validation_result_*.txt"
    fi
fi

print_message $BLUE "===================================================="
print_message $GREEN "操作完成！"

if [[ "$DRY_RUN" == false ]]; then
    print_message $YELLOW ""
    if [[ "$PERMISSION_ONLY" != true ]]; then
        print_message $YELLOW "会员功能模块导入的数据包括:"
        print_message $YELLOW "  - 15个会员账户（含管理员、普通用户、访客）"
        print_message $YELLOW "  - 多种认证方式（用户名密码、手机、邮箱、第三方登录）"
        print_message $YELLOW "  - 会话管理数据（PC端、移动端）"
        print_message $YELLOW "  - 邀请关系链"
    fi
    
    if [[ "$MEMBER_ONLY" != true ]]; then
        print_message $YELLOW "前端菜单权限模块导入的数据包括:"
        print_message $YELLOW "  - 完整的菜单结构（6个一级菜单，20+个二级菜单）"
        print_message $YELLOW "  - 40+个功能权限定义"
        print_message $YELLOW "  - 7个角色（超级管理员、管理员、审核员、企业用户、VIP用户、普通用户、访客）"
        print_message $YELLOW "  - 7个权限组"
        print_message $YELLOW "  - 完整的角色权限关联"
        print_message $YELLOW "  - 会员角色分配"
    fi
    
    print_message $YELLOW ""
    print_message $YELLOW "建议执行验证脚本检查数据完整性:"
    print_message $YELLOW "  psql -d $DB_NAME -f $VALIDATION_SQL"
fi

print_message $BLUE "===================================================="
