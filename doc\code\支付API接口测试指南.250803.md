# 支付API接口测试指南

## 概述

本文档提供了新创建的支付相关API接口的测试指南，包括接口说明、测试用例和预期结果。

## 已创建的文件

### 枚举类
- `BillingOrderStatusEnum.java` - 订单状态枚举，定义了订单的各种状态和相关业务方法

### 参数类 (Param)
- `PaymentInitiateParam.java` - 支付发起参数
- `PaymentCallbackParam.java` - 支付回调参数
- `PaymentStatusQueryParam.java` - 支付状态查询参数

### 视图对象 (VO)
- `PaymentInitiateVo.java` - 支付发起结果视图对象
- `PaymentStatusVo.java` - 支付状态视图对象（包含便利方法）

### 服务层
- `IPaymentService.java` - 支付服务接口
- `PaymentServiceImpl.java` - 支付服务实现（使用枚举进行状态管理）

### 控制器
- `PaymentController.java` - 支付控制器

### 数据访问层
- 更新了 `BillingOrderMapper.java` 和对应的XML映射文件

## API接口说明

### 1. 发起支付
- **接口**: `POST /api/v1/payment/initiate`
- **认证**: 需要登录认证
- **参数验证**: 启用
- **功能**: 基于已创建的订单发起支付

#### 请求示例
```json
{
  "bizParam": {
    "orderNo": "ORDER_20240101_001",
    "paymentGatewayId": 1,
    "returnUrl": "https://example.com/return",
    "notifyUrl": "https://example.com/notify"
  }
}
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "支付发起成功",
  "data": {
    "orderNo": "ORDER_20240101_001",
    "gatewayName": "支付宝",
    "gatewayCode": "alipay",
    "amount": 99.00,
    "currencySymbol": "¥",
    "currencyCode": "CNY",
    "paymentUrl": "https://example.com/alipay/pay?orderNo=ORDER_20240101_001",
    "paymentType": "redirect",
    "expireTime": "2024-01-01T12:30:00+08:00",
    "paymentDescription": "支付宝支付"
  }
}
```

### 2. 查询支付状态
- **接口**: `POST /api/v1/payment/status`
- **认证**: 需要登录认证
- **参数验证**: 启用
- **功能**: 查询订单的支付状态

#### 请求示例
```json
{
  "bizParam": {
    "orderNo": "ORDER_20240101_001",
    "forceRefresh": false
  }
}
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "orderNo": "ORDER_20240101_001",
    "orderStatus": 2,
    "orderStatusDesc": "已完成",
    "paymentStatus": "success",
    "paymentStatusDesc": "支付成功",
    "gatewayName": "支付宝",
    "gatewayTransactionId": "2024010122001234567890",
    "amount": 99.00,
    "paidAmount": 99.00,
    "currencySymbol": "¥",
    "currencyCode": "CNY",
    "createTime": "2024-01-01T12:00:00+08:00",
    "paidTime": "2024-01-01T12:05:00+08:00",
    "packageName": "基础套餐",
    "packageDescription": "包含100次AI对话"
  }
}
```

### 3. 取消支付
- **接口**: `POST /api/v1/payment/cancel/{orderNo}`
- **认证**: 需要登录认证
- **功能**: 取消未支付的订单

#### 请求示例
```
POST /api/v1/payment/cancel/ORDER_20240101_001
```

#### 响应示例
```json
{
  "code": 200,
  "msg": "支付取消成功"
}
```

### 4. 支付回调接口
- **支付宝回调**: `POST /api/v1/payment/callback/alipay`
- **微信支付回调**: `POST /api/v1/payment/callback/wechat`
- **PayPal回调**: `POST /api/v1/payment/callback/paypal`
- **认证**: 无需认证（由支付网关调用）

## 测试建议

### 1. 单元测试
建议为以下类创建单元测试：
- `PaymentServiceImpl` - 测试核心业务逻辑
- `PaymentController` - 测试API接口

### 2. 集成测试
- 测试与billing服务的集成
- 测试与数据库的交互
- 测试缓存功能

### 3. 端到端测试
- 完整的支付流程测试
- 支付回调处理测试
- 异常情况处理测试

## 注意事项

### 1. 待完善功能
以下功能需要根据具体的支付网关进行实现：
- 支付网关API调用逻辑
- 签名验证算法
- 回调参数解析
- 支付状态同步

### 2. 安全考虑
- 支付回调签名验证
- 敏感信息加密存储
- 防重放攻击
- 接口访问频率限制

### 3. 性能优化
- 缓存策略优化
- 数据库查询优化
- 异步处理支付回调

## 订单状态枚举说明

### BillingOrderStatusEnum
- **PENDING_PAYMENT(1, "待支付")** - 订单已创建，等待支付
- **COMPLETED(2, "已完成")** - 订单已完成，支付成功
- **CANCELLED(3, "已取消")** - 订单已取消
- **PAYMENT_FAILED(4, "支付失败")** - 支付失败

### 枚举提供的便利方法
- `canPay(code)` - 检查是否可以支付
- `canCancel(code)` - 检查是否可以取消
- `isCompleted(code)` - 检查是否已完成
- `isCancelled(code)` - 检查是否已取消
- `isPaymentFailed(code)` - 检查支付是否失败
- `isFinalStatus(code)` - 检查是否为终态

## 下一步工作

1. **实现具体支付网关集成**
   - 集成支付宝SDK
   - 集成微信支付SDK
   - 集成PayPal SDK

2. **完善错误处理**
   - 添加更详细的错误码
   - 完善异常处理逻辑

3. **添加监控和日志**
   - 支付流程监控
   - 关键操作日志记录

4. **编写测试用例**
   - 单元测试
   - 集成测试
   - 性能测试

5. **文档完善**
   - API文档
   - 部署文档
   - 运维文档
