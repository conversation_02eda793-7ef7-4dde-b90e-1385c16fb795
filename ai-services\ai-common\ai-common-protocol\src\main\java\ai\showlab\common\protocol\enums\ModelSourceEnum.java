package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型来源枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModelSourceEnum {
    
    /** 第三方 */
    THIRD_PARTY(1, "第三方"),
    
    /** 内部模型 */
    INTERNAL(2, "内部模型");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ModelSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 