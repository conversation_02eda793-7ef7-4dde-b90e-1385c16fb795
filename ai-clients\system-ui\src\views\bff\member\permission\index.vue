<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员" prop="memberId">
        <el-input
          v-model="queryParams.memberId"
          placeholder="请输入会员"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限" prop="permissionId">
        <el-input
          v-model="queryParams.permissionId"
          placeholder="请输入权限"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权限生效时间" prop="validFrom">
        <el-date-picker clearable
          v-model="queryParams.validFrom"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择权限生效时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="权限失效时间" prop="validTo">
        <el-date-picker clearable
          v-model="queryParams.validTo"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择权限失效时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:memberPermission:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:memberPermission:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:memberPermission:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:memberPermission:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberPermissionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="会员" align="center" prop="memberId" />
      <el-table-column label="权限" align="center" prop="permissionId" />
      <el-table-column label="是否授权" align="center" prop="isGranted" />
      <el-table-column label="权限生效时间" align="center" prop="validFrom" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.validFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="权限失效时间" align="center" prop="validTo" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.validTo, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:memberPermission:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:memberPermission:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员权限对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="memberPermissionRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="会员" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员" />
        </el-form-item>
        <el-form-item label="权限" prop="permissionId">
          <el-input v-model="form.permissionId" placeholder="请输入权限" />
        </el-form-item>
        <el-form-item label="权限生效时间" prop="validFrom">
          <el-date-picker clearable
            v-model="form.validFrom"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择权限生效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="权限失效时间" prop="validTo">
          <el-date-picker clearable
            v-model="form.validTo"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择权限失效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MemberPermission">
import { listMemberPermission, getMemberPermission, delMemberPermission, addMemberPermission, updateMemberPermission } from "@/api/bff/member/permission"

const { proxy } = getCurrentInstance()

const memberPermissionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    memberId: null,
    permissionId: null,
    isGranted: null,
    scope: null,
    validFrom: null,
    validTo: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询会员权限列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listMemberPermission(queryParams.value).then(response => {
    memberPermissionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    memberId: null,
    permissionId: null,
    isGranted: null,
    scope: null,
    validFrom: null,
    validTo: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("memberPermissionRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加会员权限"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getMemberPermission(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改会员权限"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["memberPermissionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateMemberPermission(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addMemberPermission(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除会员权限编号为"' + _ids + '"的数据项？').then(function() {
    return delMemberPermission(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/member/permissions/export', {
    ...queryParams.value
  }, `memberPermission_${new Date().getTime()}.xlsx`)
}

getList()
</script>
