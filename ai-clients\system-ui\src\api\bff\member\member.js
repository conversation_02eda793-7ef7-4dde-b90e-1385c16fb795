import request from '@/utils/request'

// 查询会员信息列表
export function listMember(query) {
  return request({
    url: '/system/member/member/list',
    method: 'get',
    params: query
  })
}

// 查询会员信息详细
export function getMember(id) {
  return request({
    url: '/system/member/member/' + id,
    method: 'get'
  })
}

// 新增会员信息
export function addMember(data) {
  return request({
    url: '/system/member/member',
    method: 'post',
    data: data
  })
}

// 修改会员信息
export function updateMember(data) {
  return request({
    url: '/system/member/member',
    method: 'put',
    data: data
  })
}

// 删除会员信息
export function delMember(id) {
  return request({
    url: '/system/member/member/' + id,
    method: 'delete'
  })
}
