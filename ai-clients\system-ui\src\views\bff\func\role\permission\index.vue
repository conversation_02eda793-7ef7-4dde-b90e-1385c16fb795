<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="角色名称" prop="roleId">
        <el-input
                @click="openRoleSelectDialog"
                placeholder="请选择角色"
                readonly
                v-model="roleName"
        >
          <template #append>
            <el-button @click.stop="openRoleSelectDialog">
              <el-icon>
                <Search/>
              </el-icon>
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="权限" prop="permissionId">
        <el-input
                @click="openPermissionSelectDialog"
                placeholder="请选择权限"
                readonly
                v-model="permissionName"
        >
          <template #append>
            <el-button @click.stop="openPermissionSelectDialog">
              <el-icon>
                <Search/>
              </el-icon>
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="生效时间" prop="validFrom">
        <el-date-picker clearable
          v-model="queryParams.validFrom"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择生效时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="失效时间" prop="validTo">
        <el-date-picker clearable
          v-model="queryParams.validTo"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择失效时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:funcRolePermission:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:funcRolePermission:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:funcRolePermission:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:funcRolePermission:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="funcRolePermissionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column align="center" label="权限" prop="permissionId">
        <template #default="scope">
          <span>{{ getPermissionName(scope.row.permissionId) }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column label="ID" align="center" prop="id" />-->
      <el-table-column align="center" label="角色" prop="roleId">
        <template #default="scope">
          <span>{{ getRoleName(scope.row.roleId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生效时间" align="center" prop="validFrom" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.validFrom, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="失效时间" align="center" prop="validTo" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.validTo, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:funcRolePermission:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:funcRolePermission:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色-权限映射，用于将权限分配给角色对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="funcRolePermissionRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="角色" prop="roleId">
          <el-input
                  @click="openFormRoleSelectDialog"
                  placeholder="请选择角色"
                  readonly
                  v-model="formRoleName"
          >
            <template #append>
              <el-button @click.stop="openFormRoleSelectDialog">
                <el-icon>
                  <Search/>
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="权限" prop="permissionId">
          <el-input
                  @click="openFormPermissionSelectDialog"
                  placeholder="请选择权限"
                  readonly
                  v-model="formPermissionName"
          >
            <template #append>
              <el-button @click.stop="openFormPermissionSelectDialog">
                <el-icon>
                  <Search/>
                </el-icon>
              </el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="生效时间" prop="validFrom">
          <el-date-picker clearable
            v-model="form.validFrom"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择生效时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="失效时间" prop="validTo">
          <el-date-picker clearable
            v-model="form.validTo"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择失效时间">
          </el-date-picker>
        </el-form-item>
        <!--<el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 角色选择组件 -->
    <role-select
            :selected-id="currentRoleId"
            @select="handleRoleSelect"
            v-model:visible="roleSelectOpen"
    />

    <!-- 表单角色选择组件 -->
    <role-select
            :selected-id="form.roleId"
            @select="handleFormRoleSelect"
            v-model:visible="formRoleSelectOpen"
    />

    <!-- 权限选择组件 -->
    <permission-select
            :selected-id="currentPermissionId"
            @select="handlePermissionSelect"
            v-model:visible="permissionSelectOpen"
    />

    <!-- 表单权限选择组件 -->
    <permission-select
            :selected-id="form.permissionId"
            @select="handleFormPermissionSelect"
            v-model:visible="formPermissionSelectOpen"
    />
  </div>
</template>

<script setup name="FuncRolePermission">
import { listFuncRolePermission, getFuncRolePermission, delFuncRolePermission, addFuncRolePermission, updateFuncRolePermission } from "@/api/bff/func/role/permission"
import { getFuncRole } from "@/api/bff/func/role"
import { getFuncPermission } from "@/api/bff/func/permission"
import { useRoute } from 'vue-router'
import { Search } from '@element-plus/icons-vue'
import RoleSelect from './components/RoleSelect.vue'
import PermissionSelect from './components/PermissionSelect.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()

const funcRolePermissionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

// 角色相关
const roleName = ref('')
const formRoleName = ref('')
const roleSelectOpen = ref(false)
const formRoleSelectOpen = ref(false)
const currentRoleId = ref(null)
const roleMap = ref({})

// 权限相关
const permissionName = ref('')
const formPermissionName = ref('')
const permissionSelectOpen = ref(false)
const formPermissionSelectOpen = ref(false)
const currentPermissionId = ref(null)
const permissionMap = ref({})

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    roleId: null,
    permissionId: null,
    validFrom: null,
    validTo: null,
    scope: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  },
  rules: {
    roleId: [
      { required: true, message: "角色不能为空", trigger: "blur" }
    ],
    permissionId: [
      { required: true, message: "权限不能为空", trigger: "blur" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 打开角色选择对话框 - 查询条件
const openRoleSelectDialog = () => {
  currentRoleId.value = queryParams.value.roleId
  roleSelectOpen.value = true
}

// 打开角色选择对话框 - 表单
const openFormRoleSelectDialog = () => {
  formRoleSelectOpen.value = true
}

// 打开权限选择对话框 - 查询条件
const openPermissionSelectDialog = () => {
  currentPermissionId.value = queryParams.value.permissionId
  permissionSelectOpen.value = true
}

// 打开权限选择对话框 - 表单
const openFormPermissionSelectDialog = () => {
  formPermissionSelectOpen.value = true
}

// 处理角色选择结果 - 查询条件
const handleRoleSelect = (data) => {
  queryParams.value.roleId = data.id
  roleName.value = data.name
  roleMap.value[data.id] = data.name
}

// 处理角色选择结果 - 表单
const handleFormRoleSelect = (data) => {
  form.value.roleId = data.id
  formRoleName.value = data.name
  roleMap.value[data.id] = data.name
}

// 处理权限选择结果 - 查询条件
const handlePermissionSelect = (data) => {
  queryParams.value.permissionId = data.id
  permissionName.value = data.name
  permissionMap.value[data.id] = data.name
}

// 处理权限选择结果 - 表单
const handleFormPermissionSelect = (data) => {
  form.value.permissionId = data.id
  formPermissionName.value = data.name
  permissionMap.value[data.id] = data.name
}

// 获取表格中角色名称
const getRoleName = (id) => {
  return roleMap.value[id] || id
}

// 获取表格中权限名称
const getPermissionName = (id) => {
  return permissionMap.value[id] || id
}

// 根据ID获取角色名称
const getRoleNameById = (id) => {
  if (!id) return ''
  
  if (roleMap.value[id]) {
    roleName.value = roleMap.value[id]
    return roleMap.value[id]
  }
  
  // 如果map中没有，尝试从后端获取
  getFuncRole(id).then(response => {
    if (response.data) {
      roleMap.value[id] = response.data.name
      roleName.value = response.data.name
    }
  })
  
  return id
}

// 根据ID获取权限名称
const getPermissionNameById = (id) => {
  if (!id) return ''
  
  if (permissionMap.value[id]) {
    permissionName.value = permissionMap.value[id]
    return permissionMap.value[id]
  }
  
  // 如果map中没有，尝试从后端获取
  getFuncPermission(id).then(response => {
    if (response.data) {
      permissionMap.value[id] = response.data.name
      permissionName.value = response.data.name
    }
  })
  
  return id
}

// 检查路由参数中是否有roleId
const initRouteParams = () => {
  if (route.params.roleId) {
    queryParams.value.roleId = route.params.roleId
    getRoleNameById(route.params.roleId)
  }
  if (route.query.roleId) {
    queryParams.value.roleId = route.query.roleId
    getRoleNameById(route.query.roleId)
  }
  
  // 如果有权限ID参数
  if (route.params.permissionId) {
    queryParams.value.permissionId = route.params.permissionId
    getPermissionNameById(route.params.permissionId)
  }
  if (route.query.permissionId) {
    queryParams.value.permissionId = route.query.permissionId
    getPermissionNameById(route.query.permissionId)
  }
}

/** 查询角色-权限映射，用于将权限分配给角色列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listFuncRolePermission(queryParams.value).then(response => {
    funcRolePermissionList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    roleId: null,
    permissionId: null,
    validFrom: null,
    validTo: null,
    scope: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  formRoleName.value = ''
  formPermissionName.value = ''
  proxy.resetForm("funcRolePermissionRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  // 清空角色
  queryParams.value.roleId = null
  roleName.value = ''
  // 清空权限
  queryParams.value.permissionId = null
  permissionName.value = ''
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  // 如果查询条件中有角色ID，则默认使用该角色
  if (queryParams.value.roleId) {
    form.value.roleId = queryParams.value.roleId
    formRoleName.value = roleName.value
  }
  open.value = true
  title.value = "添加角色-权限映射，用于将权限分配给角色"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getFuncRolePermission(_id).then(response => {
    form.value = response.data
    
    // 设置表单中的角色名称
    if (form.value.roleId) {
      if (roleMap.value[form.value.roleId]) {
        formRoleName.value = roleMap.value[form.value.roleId]
      } else {
        getFuncRole(form.value.roleId).then(res => {
          if (res.data) {
            roleMap.value[form.value.roleId] = res.data.name
            formRoleName.value = res.data.name
          } else {
            formRoleName.value = form.value.roleId
          }
        }).catch(() => {
          formRoleName.value = form.value.roleId
        })
      }
    }
    
    // 设置表单中的权限名称
    if (form.value.permissionId) {
      if (permissionMap.value[form.value.permissionId]) {
        formPermissionName.value = permissionMap.value[form.value.permissionId]
      } else {
        getFuncPermission(form.value.permissionId).then(res => {
          if (res.data) {
            permissionMap.value[form.value.permissionId] = res.data.name
            formPermissionName.value = res.data.name
          } else {
            formPermissionName.value = form.value.permissionId
          }
        }).catch(() => {
          formPermissionName.value = form.value.permissionId
        })
      }
    }
    
    open.value = true
    title.value = "修改角色-权限映射，用于将权限分配给角色"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["funcRolePermissionRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateFuncRolePermission(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addFuncRolePermission(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除角色-权限映射，用于将权限分配给角色编号为"' + _ids + '"的数据项？').then(function() {
    return delFuncRolePermission(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/func/role/permission/export', {
    ...queryParams.value
  }, `funcRolePermission_${new Date().getTime()}.xlsx`)
}

// 初始化
initRouteParams()

// 获取角色和权限的名称
const initNames = () => {
  // 获取表格数据中所有角色和权限的名称
  listFuncRolePermission(queryParams.value).then(response => {
    if (response.rows && response.rows.length > 0) {
      // 收集所有角色和权限ID
      const roleIds = new Set()
      const permissionIds = new Set()
      
      response.rows.forEach(item => {
        if (item.roleId) roleIds.add(item.roleId)
        if (item.permissionId) permissionIds.add(item.permissionId)
      })
      
      // 获取角色名称
      roleIds.forEach(id => {
        if (!roleMap.value[id]) {
          getFuncRole(id).then(response => {
            if (response.data) {
              roleMap.value[id] = response.data.name
            }
          })
        }
      })
      
      // 获取权限名称
      permissionIds.forEach(id => {
        if (!permissionMap.value[id]) {
          getFuncPermission(id).then(response => {
            if (response.data) {
              permissionMap.value[id] = response.data.name
            }
          })
        }
      })
    }
  })
}

initNames()
getList()
</script>
