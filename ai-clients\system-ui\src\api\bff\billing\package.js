import request from '@/utils/request'

// 查询计费套餐列表
export function listBillingPackage(query) {
  return request({
    url: '/system/billing/package/list',
    method: 'get',
    params: query
  })
}

// 查询计费套餐详细
export function getBillingPackage(id) {
  return request({
    url: '/system/billing/package/' + id,
    method: 'get'
  })
}

// 新增计费套餐
export function addBillingPackage(data) {
  return request({
    url: '/system/billing/package',
    method: 'post',
    data: data
  })
}

// 修改计费套餐
export function updateBillingPackage(data) {
  return request({
    url: '/system/billing/package',
    method: 'put',
    data: data
  })
}

// 删除计费套餐
export function delBillingPackage(id) {
  return request({
    url: '/system/billing/package/' + id,
    method: 'delete'
  })
}
