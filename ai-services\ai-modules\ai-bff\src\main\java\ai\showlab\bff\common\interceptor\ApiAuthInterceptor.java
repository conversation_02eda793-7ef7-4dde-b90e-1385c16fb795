package ai.showlab.bff.common.interceptor;

import ai.showlab.bff.common.annotation.ApiAuth;
import ai.showlab.bff.common.util.BffKit;
import ai.showlab.bff.service.v1.member.IMemberPermissionService;
import ai.showlab.common.core.constant.CodeConstants;
import ai.showlab.common.core.web.domain.RestResult;
import ai.showlab.common.protocol.enums.ApiAuthTypeEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.lang.reflect.Method;

/**
 * API权限认证拦截器
 * <p>
 * 根据接口上的@ApiAuth注解进行权限校验：
 * - ANONYMOUS: 允许匿名访问
 * - LOGIN_REQUIRED: 需要登录
 * - PERMISSION_REQUIRED: 需要登录并校验权限
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ApiAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private IMemberPermissionService memberPermissionService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        // 只处理Controller方法
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }
        Method method = handlerMethod.getMethod();
        // 获取方法上的@ApiAuth注解
        ApiAuth apiAuth = method.getAnnotation(ApiAuth.class);
        // 如果方法上没有注解，检查类上的注解
        if (apiAuth == null) {
            apiAuth = handlerMethod.getBeanType().getAnnotation(ApiAuth.class);
        }
        // 如果都没有注解，使用默认的LOGIN_REQUIRED
        ApiAuthTypeEnum authType = ApiAuthTypeEnum.LOGIN_REQUIRED;
        String permissionCode = "";
        boolean enabled = true;
        if (apiAuth != null) {
            authType = apiAuth.type();
            permissionCode = apiAuth.code();
            enabled = apiAuth.enabled();
        }
        // 如果权限校验被禁用，直接通过
        if (!enabled) {
            log.debug("权限校验已禁用，接口: {}", getRequestInfo(request));
            return true;
        }
        // 记录请求信息
        log.debug("权限校验开始，接口: {}, 认证类型: {}, 权限编码: {}", getRequestInfo(request), authType, permissionCode);

        try {
            return checkPermission(request, response, authType, permissionCode);
        } catch (Exception e) {
            log.error("权限校验异常，接口: {}", getRequestInfo(request), e);
            writeErrorResponse(response, CodeConstants.SERVER_ERROR, "权限校验失败");
            return false;
        }
    }

    /**
     * 执行权限校验
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param authType 认证类型
     * @param permissionCode 权限编码
     * @return true表示校验通过，false表示校验失败
     */
    private boolean checkPermission(HttpServletRequest request, HttpServletResponse response, ApiAuthTypeEnum authType, String permissionCode) throws IOException {
        switch (authType) {
            case ANONYMOUS:
                // 匿名访问，直接通过
                log.debug("匿名访问，校验通过");
                return true;

            case LOGIN_REQUIRED:
                // 需要登录
                return checkLogin(request, response);

            case PERMISSION_REQUIRED:
                // 需要登录并校验权限
                if (!checkLogin(request, response)) {
                    return false;
                }
                return checkPermissionCode(request, response, permissionCode);

            default:
                log.warn("未知的认证类型: {}", authType);
                writeErrorResponse(response, CodeConstants.SERVER_ERROR, "未知的认证类型");
                return false;
        }
    }

    /**
     * 检查登录状态
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @return true表示已登录，false表示未登录
     */
    private boolean checkLogin(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Long currentMemberId = BffKit.getCurrentMemberId();
        if (currentMemberId == null) {
            log.debug("用户未登录，接口: {}", getRequestInfo(request));
            writeErrorResponse(response, CodeConstants.UNAUTHORIZED, "请先登录");
            return false;
        }
        log.debug("用户已登录，会员ID: {}", currentMemberId);
        return true;
    }

    /**
     * 检查权限编码
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param permissionCode 权限编码
     * @return true表示有权限，false表示无权限
     */
    private boolean checkPermissionCode(HttpServletRequest request, HttpServletResponse response, String permissionCode) throws IOException {
        if (!StringUtils.hasText(permissionCode)) {
            log.warn("权限编码为空，接口: {}", getRequestInfo(request));
            writeErrorResponse(response, CodeConstants.SERVER_ERROR, "权限配置错误");
            return false;
        }
        Long currentMemberId = BffKit.getCurrentMemberId();
        // 检查权限编码是否存在
        if (!memberPermissionService.isPermissionCodeExists(permissionCode)) {
            log.warn("权限编码不存在: {}, 接口: {}", permissionCode, getRequestInfo(request));
            writeErrorResponse(response, CodeConstants.SERVER_ERROR, "权限配置错误");
            return false;
        }
        // 检查会员是否拥有该权限
        boolean hasPermission = memberPermissionService.hasPermissionByCode(currentMemberId, permissionCode);
        if (!hasPermission) {
            log.debug("用户无权限，会员ID: {}, 权限编码: {}, 接口: {}", currentMemberId, permissionCode, getRequestInfo(request));
            writeErrorResponse(response, CodeConstants.FORBIDDEN, "权限不足");
            return false;
        }
        log.debug("权限校验通过，会员ID: {}, 权限编码: {}", currentMemberId, permissionCode);
        return true;
    }

    /**
     * 写入错误响应
     *
     * @param response HTTP响应
     * @param code 错误码
     * @param message 错误消息
     */
    private void writeErrorResponse(HttpServletResponse response, Integer code, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");
        RestResult result = new RestResult(code, message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        response.getWriter().write(jsonResponse);
    }

    /**
     * 获取请求信息
     *
     * @param request HTTP请求
     * @return 请求信息字符串
     */
    private String getRequestInfo(HttpServletRequest request) {
        return String.format("%s %s", request.getMethod(), request.getRequestURI());
    }
}
