-- ====================================================================================
-- AIShowLab - 会员功能模块模拟数据 (PostgreSQL)
-- 文件名: 会员功能模拟数据.sql
-- 作者: AI Assistant
-- 描述: 为会员功能相关表生成模拟数据，包括会员信息、认证方式、会话管理等
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. 会员主表数据 (a_member)
-- ==================================================
INSERT INTO a_member (member_type, member_level, username, nickname, password, avatar, gender, email, phone, invitation_code, inviter_id, status, country_id, lang_id, time_zone, last_login_ip, last_login_time) VALUES
-- 管理员账户
(2, 3, 'admin', '系统管理员', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/admin.png', 1, '<EMAIL>', '13800000001', 'ADMIN001', NULL, 1, 1, 1, 1, '127.0.0.1', NOW() - INTERVAL '1 hour'),

-- 普通会员
(1, 1, 'user001', '张小明', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user001.png', 1, '<EMAIL>', '13800000002', 'USER001', NULL, 1, 1, 1, 1, '*************', NOW() - INTERVAL '30 minutes'),
(1, 2, 'user002', '李小红', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user002.png', 2, '<EMAIL>', '13800000003', 'USER002', 1, 1, 1, 1, 1, '*************', NOW() - INTERVAL '2 hours'),
(1, 2, 'user003', '王大力', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user003.png', 1, '<EMAIL>', '13800000004', 'USER003', 1, 1, 1, 1, 1, '*************', NOW() - INTERVAL '1 day'),
(1, 3, 'user004', '赵美丽', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user004.png', 2, '<EMAIL>', '13800000005', 'USER004', 2, 1, 1, 1, 1, '*************', NOW() - INTERVAL '3 hours'),
(1, 1, 'user005', '刘小强', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user005.png', 1, '<EMAIL>', '13800000006', 'USER005', 2, 1, 1, 1, 1, '*************', NOW() - INTERVAL '5 hours'),
(1, 2, 'user006', '陈小花', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user006.png', 2, '<EMAIL>', '13800000007', 'USER006', 3, 1, 1, 1, 1, '*************', NOW() - INTERVAL '6 hours'),
(1, 3, 'user007', '杨大山', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user007.png', 1, '<EMAIL>', '13800000008', 'USER007', 3, 1, 1, 1, 1, '*************', NOW() - INTERVAL '12 hours'),
(1, 1, 'user008', '周小雨', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user008.png', 2, '<EMAIL>', '13800000009', 'USER008', 4, 1, 1, 1, 1, '*************', NOW() - INTERVAL '1 day'),
(1, 2, 'user009', '吴小龙', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user009.png', 1, '<EMAIL>', '13800000010', 'USER009', 4, 1, 1, 1, 1, '*************', NOW() - INTERVAL '2 days'),
(1, 3, 'user010', '郑小凤', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user010.png', 2, '<EMAIL>', '13800000011', 'USER010', 5, 1, 1, 1, 1, '*************', NOW() - INTERVAL '3 days'),

-- 待激活用户
(1, 1, 'user011', '孙小明', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/default.png', 1, '<EMAIL>', '13800000012', 'USER011', 6, 3, 1, 1, 1, NULL, NULL),

-- 禁用用户
(1, 1, 'user012', '黄小黑', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', '/avatars/user012.png', 1, '<EMAIL>', '13800000013', 'USER012', 7, 2, 1, 1, 1, '192.168.1.110', NOW() - INTERVAL '7 days'),

-- 访客用户
(3, 1, 'guest001', '访客001', NULL, '/avatars/guest.png', 0, NULL, NULL, 'GUEST001', NULL, 1, 1, 1, 1, '*************', NOW() - INTERVAL '10 minutes'),
(3, 1, 'guest002', '访客002', NULL, '/avatars/guest.png', 0, NULL, NULL, 'GUEST002', NULL, 1, 1, 1, 1, '*************', NOW() - INTERVAL '20 minutes');

-- ==================================================
-- 2. 会员认证信息数据 (a_member_auth)
-- ==================================================

-- 管理员认证信息
INSERT INTO a_member_auth (member_id, auth_type, identifier, credential, is_verified) VALUES
(1, 1, 'admin', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(1, 3, '<EMAIL>', NULL, true),
(1, 2, '13800000001', NULL, true);

-- 普通会员认证信息
INSERT INTO a_member_auth (member_id, auth_type, identifier, credential, is_verified) VALUES
-- 用户001
(2, 1, 'user001', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(2, 3, '<EMAIL>', NULL, true),
(2, 2, '13800000002', NULL, true),

-- 用户002
(3, 1, 'user002', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(3, 3, '<EMAIL>', NULL, true),
(3, 2, '13800000003', NULL, true),
(3, 4, 'wx_openid_user002', 'wx_access_token_user002', true),

-- 用户003
(4, 1, 'user003', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(4, 3, '<EMAIL>', NULL, true),
(4, 2, '13800000004', NULL, true),

-- 用户004
(5, 1, 'user004', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(5, 3, '<EMAIL>', NULL, true),
(5, 2, '13800000005', NULL, true),
(5, 5, 'github_user_004', 'github_access_token_004', true),

-- 用户005
(6, 1, 'user005', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(6, 3, '<EMAIL>', NULL, true),
(6, 2, '13800000006', NULL, true),

-- 用户006
(7, 1, 'user006', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(7, 3, '<EMAIL>', NULL, true),
(7, 2, '13800000007', NULL, true),

-- 用户007
(8, 1, 'user007', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(8, 3, '<EMAIL>', NULL, true),
(8, 2, '13800000008', NULL, true),

-- 用户008
(9, 1, 'user008', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(9, 3, '<EMAIL>', NULL, true),
(9, 2, '13800000009', NULL, true),

-- 用户009
(10, 1, 'user009', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(10, 3, '<EMAIL>', NULL, true),
(10, 2, '13800000010', NULL, true),

-- 用户010
(11, 1, 'user010', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(11, 3, '<EMAIL>', NULL, true),
(11, 2, '13800000011', NULL, true),

-- 待激活用户011
(12, 1, 'user011', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(12, 3, '<EMAIL>', NULL, false),
(12, 2, '13800000012', NULL, false),

-- 禁用用户012
(13, 1, 'user012', '$2a$10$N.zmdr9k7uOCQb96VdodAOBXDUKyF9gk6vVqsQhKQNHSgr.WMlG8m', true),
(13, 3, '<EMAIL>', NULL, true),
(13, 2, '13800000013', NULL, true);

-- ==================================================
-- 3. 会员会话信息数据 (a_member_session)
-- ==================================================

-- 管理员活跃会话
INSERT INTO a_member_session (member_id, token, device_type, device_info, ip_address, login_time, expire_time, is_active) VALUES
(1, 'jwt_admin_session_001', 1, 'Chrome 120.0.0.0 on Windows 11', '127.0.0.1', NOW() - INTERVAL '1 hour', NOW() + INTERVAL '23 hours', true),
(1, 'jwt_admin_session_002', 2, 'Safari on iPhone 15 Pro', '************', NOW() - INTERVAL '3 hours', NOW() + INTERVAL '21 hours', true),

-- 普通会员活跃会话
INSERT INTO a_member_session (member_id, token, device_type, device_info, ip_address, login_time, expire_time, is_active) VALUES
-- 用户001
(2, 'jwt_user001_session_001', 1, 'Firefox ********* on macOS Sonoma', '*************', NOW() - INTERVAL '30 minutes', NOW() + INTERVAL '23.5 hours', true),
(2, 'jwt_user001_session_002', 2, 'Chrome on Android 14', '*************', NOW() - INTERVAL '2 hours', NOW() + INTERVAL '22 hours', true),

-- 用户002
(3, 'jwt_user002_session_001', 1, 'Edge 120.0.0.0 on Windows 10', '*************', NOW() - INTERVAL '2 hours', NOW() + INTERVAL '22 hours', true),

-- 用户003
(4, 'jwt_user003_session_001', 1, 'Chrome 120.0.0.0 on Ubuntu 22.04', '*************', NOW() - INTERVAL '1 day', NOW() + INTERVAL '23 hours', false),
(4, 'jwt_user003_session_002', 2, 'WeChat Browser on iOS 17', '*************', NOW() - INTERVAL '4 hours', NOW() + INTERVAL '20 hours', true),

-- 用户004
(5, 'jwt_user004_session_001', 1, 'Safari 17.0 on macOS Ventura', '*************', NOW() - INTERVAL '3 hours', NOW() + INTERVAL '21 hours', true),

-- 用户005
(6, 'jwt_user005_session_001', 1, 'Chrome 120.0.0.0 on Windows 11', '*************', NOW() - INTERVAL '5 hours', NOW() + INTERVAL '19 hours', true),

-- 用户006
(7, 'jwt_user006_session_001', 2, 'Chrome on Android 13', '*************', NOW() - INTERVAL '6 hours', NOW() + INTERVAL '18 hours', true),

-- 用户007
(8, 'jwt_user007_session_001', 1, 'Firefox ********* on Windows 10', '*************', NOW() - INTERVAL '12 hours', NOW() + INTERVAL '12 hours', true),

-- 用户008
(9, 'jwt_user008_session_001', 2, 'Safari on iPhone 14', '*************', NOW() - INTERVAL '1 day', NOW() + INTERVAL '23 hours', false),

-- 用户009
(10, 'jwt_user009_session_001', 1, 'Chrome 120.0.0.0 on macOS Monterey', '*************', NOW() - INTERVAL '2 days', NOW() + INTERVAL '22 hours', false),

-- 用户010
(11, 'jwt_user010_session_001', 1, 'Edge 120.0.0.0 on Windows 11', '*************', NOW() - INTERVAL '3 days', NOW() + INTERVAL '21 hours', false),

-- 访客会话
(14, 'jwt_guest001_session_001', 1, 'Chrome 120.0.0.0 on Windows 10', '*************', NOW() - INTERVAL '10 minutes', NOW() + INTERVAL '23 hours 50 minutes', true),
(15, 'jwt_guest002_session_001', 2, 'Safari on iPad Air', '*************', NOW() - INTERVAL '20 minutes', NOW() + INTERVAL '23 hours 40 minutes', true),

-- 历史会话（已过期）
INSERT INTO a_member_session (member_id, token, device_type, device_info, ip_address, login_time, expire_time, is_active) VALUES
(2, 'jwt_user001_session_expired_001', 1, 'Chrome ********* on Windows 10', '*************', NOW() - INTERVAL '2 days', NOW() - INTERVAL '1 day', false),
(3, 'jwt_user002_session_expired_001', 2, 'Safari on iPhone 13', '*************', NOW() - INTERVAL '3 days', NOW() - INTERVAL '2 days', false),
(4, 'jwt_user003_session_expired_001', 1, 'Firefox 120.0.0.0 on Ubuntu 20.04', '*************', NOW() - INTERVAL '5 days', NOW() - INTERVAL '4 days', false),
(5, 'jwt_user004_session_expired_001', 1, 'Edge ********* on Windows 11', '*************', NOW() - INTERVAL '7 days', NOW() - INTERVAL '6 days', false);

-- ==================================================
-- 数据插入完成
-- ==================================================
