<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分组ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入分组ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!--<el-form-item label="父分组" prop="pid">
        <el-input
          v-model="queryParams.pid"
          placeholder="请输入父分组"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="唯一编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入唯一编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="分组名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入分组名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:funcPermissionGroup:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Sort"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="funcPermissionGroupList"
      row-key="id"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column :show-overflow-tooltip="true" label="名称" prop="name" width="160"/>
      <!--<el-table-column label="唯一编码" align="center" prop="code" />-->
      <el-table-column :show-overflow-tooltip="true" label="描述" prop="description"/>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="300">
        <template #default="scope">
          <el-button @click="handlePermissionList(scope.row)" icon="List" link type="primary" v-hasPermi="['system:funcPermission:list']">权限列表</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:funcPermissionGroup:edit']">修改</el-button>
          <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:funcPermissionGroup:add']">新增</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:funcPermissionGroup:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改权限分组对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="funcPermissionGroupRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="父分组" prop="pid">
          <el-tree-select
            v-model="form.pid"
            :data="funcPermissionGroupOptions"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            placeholder="请选择父分组"
            check-strictly
          />
        </el-form-item>
        <!--<el-form-item label="唯一编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入唯一编码" />
        </el-form-item>-->
        <el-form-item label="分组名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FuncPermissionGroup">
import { listFuncPermissionGroup, getFuncPermissionGroup, delFuncPermissionGroup, addFuncPermissionGroup, updateFuncPermissionGroup } from "@/api/bff/func/permission/group"

const { proxy } = getCurrentInstance()

const funcPermissionGroupList = ref([])
const funcPermissionGroupOptions = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref("")
const isExpandAll = ref(true)
const refreshTable = ref(true)
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    id: null,
    pid: null,
    code: null,
    name: null,
    description: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询权限分组列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listFuncPermissionGroup(queryParams.value).then(response => {
    funcPermissionGroupList.value = proxy.handleTree(response.data, "id", "pid")
    loading.value = false
  })
}

/** 查询权限分组下拉树结构 */
function getTreeselect() {
  listFuncPermissionGroup().then(response => {
    funcPermissionGroupOptions.value = []
    const data = { id: null, name: '顶级节点', children: [] }
    data.children = proxy.handleTree(response.data, "id", "pid")
    funcPermissionGroupOptions.value.push(data)
  })
}
	
// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    pid: null,
    code: null,
    name: null,
    description: null,
    sortOrder: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("funcPermissionGroupRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset()
  getTreeselect()
  if (row != null && row.id) {
    form.value.pid = row.id
  } else {
    form.value.pid = null
  }
  open.value = true
  title.value = "添加权限分组"
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset()
  await getTreeselect()
  if (row != null) {
    form.value.pid = row.pid
  }
  getFuncPermissionGroup(row.id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改权限分组"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["funcPermissionGroupRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateFuncPermissionGroup(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addFuncPermissionGroup(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除权限分组编号为"' + row.id + '"的数据项？').then(function() {
    return delFuncPermissionGroup(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 权限列表按钮操作 */
function handlePermissionList(row) {
  const params = { groupId: row.id }
  proxy.$tab.openPage('权限列表 - ' + row.name, '/bff/func/permission', params)
}

getList()
</script>
