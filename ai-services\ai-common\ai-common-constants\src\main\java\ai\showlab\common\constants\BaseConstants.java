package ai.showlab.common.constants;

/**
 * 项目基本常量
 * <AUTHOR>
 */
public class BaseConstants {
    // 日志里的请求跟踪ID
    public static final String TRACE_ID_HEADER = "X-Req-Id";
    public static final String REQ_ID_MDC_KEY = "reqId";
    public static final String REQUEST_START_TIME = "requestStartTime";

    /**
     * 重复登录处理策略
     */
    public static final class DuplicateLoginStrategy {
        /** 允许多设备同时登录 */
        public static final String ALLOW_MULTIPLE = "ALLOW_MULTIPLE";
        /** 新登录踢掉旧登录 */
        public static final String KICK_OLD = "KICK_OLD";
        /** 拒绝新登录 */
        public static final String REJECT_NEW = "REJECT_NEW";
    }

    /**
     * 默认最大同时登录设备数
     */
    public static final int DEFAULT_MAX_CONCURRENT_SESSIONS = 3;
}
