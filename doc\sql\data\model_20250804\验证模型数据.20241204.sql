-- ====================================================================================
-- AIShowLab - 模型管理数据验证脚本
-- 文件名: 验证模型数据.20241204.sql
-- 作者: AI Assistant
-- 描述: 用于验证模型管理模拟数据的完整性和正确性
-- 创建时间: 2024-12-04
-- ====================================================================================

-- ==================================================
-- 1. 基础数据统计
-- ==================================================

-- 1.1 模型分类统计
SELECT 
    '模型分类统计' as 检查项目,
    c.name as 分类名称,
    COUNT(m.id) as 模型数量
FROM a_model_category c 
LEFT JOIN a_model m ON c.id = m.category_id AND m.delete_time IS NULL
WHERE c.delete_time IS NULL 
GROUP BY c.id, c.name 
ORDER BY c.sort_order;

-- 1.2 供应商模型分布
SELECT 
    '供应商模型分布' as 检查项目,
    p.provider_name as 供应商名称,
    p.channel_type as 渠道类型,
    COUNT(m.id) as 模型数量
FROM a_model_provider p
LEFT JOIN a_model m ON p.id = m.provider_id AND m.delete_time IS NULL
GROUP BY p.id, p.provider_name, p.channel_type
ORDER BY COUNT(m.id) DESC;

-- 1.3 模型来源统计
SELECT 
    '模型来源统计' as 检查项目,
    CASE m.source 
        WHEN 1 THEN '第三方'
        WHEN 2 THEN '内部模型'
        ELSE '未知'
    END as 模型来源,
    COUNT(*) as 数量
FROM a_model m
WHERE m.delete_time IS NULL
GROUP BY m.source
ORDER BY m.source;

-- ==================================================
-- 2. 数据完整性检查
-- ==================================================

-- 2.1 检查模型是否都有对应的分类和供应商
SELECT 
    '数据完整性检查' as 检查项目,
    '模型关联检查' as 检查内容,
    COUNT(*) as 问题数量
FROM a_model m
LEFT JOIN a_model_category c ON m.category_id = c.id AND c.delete_time IS NULL
LEFT JOIN a_model_provider p ON m.provider_id = p.id
WHERE m.delete_time IS NULL 
AND (c.id IS NULL OR p.id IS NULL);

-- 2.2 检查模型特性配置
SELECT 
    '模型特性配置' as 检查项目,
    m.name as 模型名称,
    COUNT(mf.id) as 特性数量
FROM a_model m
LEFT JOIN a_model_feature mf ON m.id = mf.model_id AND mf.delete_time IS NULL
WHERE m.delete_time IS NULL
GROUP BY m.id, m.name
HAVING COUNT(mf.id) = 0
ORDER BY m.name;

-- 2.3 检查模型输出格式配置
SELECT 
    '模型输出格式配置' as 检查项目,
    m.name as 模型名称,
    COUNT(mof.id) as 输出格式数量
FROM a_model m
LEFT JOIN a_model_output_format mof ON m.id = mof.model_id AND mof.delete_time IS NULL
WHERE m.delete_time IS NULL
GROUP BY m.id, m.name
HAVING COUNT(mof.id) = 0
ORDER BY m.name;

-- ==================================================
-- 3. 功能特性统计
-- ==================================================

-- 3.1 流式支持统计
SELECT 
    '流式支持统计' as 检查项目,
    supports_stream as 支持流式,
    COUNT(*) as 模型数量
FROM a_model m
WHERE m.delete_time IS NULL
GROUP BY supports_stream
ORDER BY supports_stream;

-- 3.2 函数调用支持统计
SELECT 
    '函数调用支持统计' as 检查项目,
    supports_function as 支持函数调用,
    COUNT(*) as 模型数量
FROM a_model m
WHERE m.delete_time IS NULL
GROUP BY supports_function
ORDER BY supports_function;

-- ==================================================
-- 4. API密钥状态检查
-- ==================================================

-- 4.1 API密钥状态统计
SELECT 
    'API密钥状态' as 检查项目,
    p.provider_name as 供应商,
    ak.status as 状态,
    COUNT(*) as 密钥数量,
    SUM(ak.quota) as 总额度,
    SUM(ak.used_quota) as 已用额度
FROM a_model_api_key ak
JOIN a_model_provider p ON ak.provider_id = p.id
WHERE ak.delete_time IS NULL
GROUP BY p.provider_name, ak.status
ORDER BY p.provider_name, ak.status;

-- ==================================================
-- 5. 可见性权限检查
-- ==================================================

-- 5.1 模型可见性统计
SELECT 
    '模型可见性统计' as 检查项目,
    m.name as 模型名称,
    CASE mv.visibility_type
        WHEN 1 THEN '按角色'
        WHEN 2 THEN '按会员'
        WHEN 3 THEN '按地区'
        ELSE '未知'
    END as 可见性类型,
    mv.reference_id as 引用ID,
    mv.is_enabled as 是否启用
FROM a_model m
LEFT JOIN a_model_visibility mv ON m.id = mv.model_id AND mv.delete_time IS NULL
WHERE m.delete_time IS NULL
ORDER BY m.name, mv.visibility_type, mv.reference_id;

-- ==================================================
-- 6. 数据质量检查
-- ==================================================

-- 6.1 检查重复的模型编码
SELECT 
    '重复模型编码检查' as 检查项目,
    code as 模型编码,
    COUNT(*) as 重复数量
FROM a_model
WHERE delete_time IS NULL
GROUP BY code
HAVING COUNT(*) > 1;

-- 6.2 检查空值字段
SELECT 
    '空值字段检查' as 检查项目,
    '模型名称为空' as 问题类型,
    COUNT(*) as 问题数量
FROM a_model
WHERE delete_time IS NULL AND (name IS NULL OR trim(name) = '')
UNION ALL
SELECT 
    '空值字段检查' as 检查项目,
    '模型编码为空' as 问题类型,
    COUNT(*) as 问题数量
FROM a_model
WHERE delete_time IS NULL AND (code IS NULL OR trim(code) = '');

-- ==================================================
-- 7. 总体数据概览
-- ==================================================
SELECT 
    '总体数据概览' as 检查项目,
    '模型分类' as 数据类型,
    COUNT(*) as 记录数
FROM a_model_category
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '模型供应商' as 数据类型,
    COUNT(*) as 记录数
FROM a_model_provider
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    'AI模型' as 数据类型,
    COUNT(*) as 记录数
FROM a_model
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '模型特性' as 数据类型,
    COUNT(*) as 记录数
FROM a_model_feature
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '输出格式' as 数据类型,
    COUNT(*) as 记录数
FROM a_model_output_format
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    'API密钥' as 数据类型,
    COUNT(*) as 记录数
FROM a_model_api_key
WHERE delete_time IS NULL
UNION ALL
SELECT 
    '总体数据概览' as 检查项目,
    '可见性规则' as 数据类型,
    COUNT(*) as 记录数
FROM a_model_visibility
WHERE delete_time IS NULL
ORDER BY 数据类型;
