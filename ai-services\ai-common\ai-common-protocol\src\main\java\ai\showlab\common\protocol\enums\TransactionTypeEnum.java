package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TransactionTypeEnum {
    
    /** 消费 */
    CONSUME(1, "消费"),
    
    /** 充值 */
    RECHARGE(2, "充值"),
    
    /** 退款 */
    REFUND(3, "退款"),
    
    /** 赠送 */
    GIFT(4, "赠送"),
    
    /** 分润 */
    PROFIT_SHARING(5, "分润");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TransactionTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransactionTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 