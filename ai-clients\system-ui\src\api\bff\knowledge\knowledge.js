import request from '@/utils/request'

// 查询知识库列表
export function listKnowledge(query) {
  return request({
    url: '/system/knowledges/list',
    method: 'get',
    params: query
  })
}

// 查询知识库详细
export function getKnowledge(id) {
  return request({
    url: '/system/knowledges/' + id,
    method: 'get'
  })
}

// 新增知识库
export function addKnowledge(data) {
  return request({
    url: '/system/knowledges',
    method: 'post',
    data: data
  })
}

// 修改知识库
export function updateKnowledge(data) {
  return request({
    url: '/system/knowledges',
    method: 'put',
    data: data
  })
}

// 删除知识库
export function delKnowledge(id) {
  return request({
    url: '/system/knowledges/' + id,
    method: 'delete'
  })
}
