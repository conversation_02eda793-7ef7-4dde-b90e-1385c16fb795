#!/bin/bash

# ====================================================================================
# AIShowLab - 模型管理数据导入脚本
# 文件名: 导入模型数据.20241204.sh
# 作者: AI Assistant
# 描述: 用于导入模型管理模拟数据的便捷脚本
# 创建时间: 2024-12-04
# ====================================================================================

# 设置脚本参数
set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DB_NAME="aishowlab"
DEFAULT_DB_USER="postgres"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：打印帮助信息
print_help() {
    echo "AIShowLab 模型管理数据导入脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST        数据库主机地址 (默认: $DEFAULT_DB_HOST)"
    echo "  -p, --port PORT        数据库端口 (默认: $DEFAULT_DB_PORT)"
    echo "  -d, --database DB      数据库名称 (默认: $DEFAULT_DB_NAME)"
    echo "  -u, --user USER        数据库用户名 (默认: $DEFAULT_DB_USER)"
    echo "  -w, --password PASS    数据库密码 (可选，建议使用 .pgpass 文件)"
    echo "  --dry-run              仅验证SQL语法，不执行导入"
    echo "  --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -h localhost -d aishowlab -u postgres"
    echo "  $0 --dry-run  # 仅验证SQL语法"
    echo ""
}

# 解析命令行参数
DB_HOST=$DEFAULT_DB_HOST
DB_PORT=$DEFAULT_DB_PORT
DB_NAME=$DEFAULT_DB_NAME
DB_USER=$DEFAULT_DB_USER
DB_PASSWORD=""
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -p|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -w|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            print_help
            exit 0
            ;;
        *)
            print_message $RED "未知参数: $1"
            print_help
            exit 1
            ;;
    esac
done

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_FILE="$SCRIPT_DIR/模型管理模拟数据.20241204.sql"
VALIDATION_FILE="$SCRIPT_DIR/验证模型数据.20241204.sql"

# 检查SQL文件是否存在
if [[ ! -f "$SQL_FILE" ]]; then
    print_message $RED "错误: 找不到SQL文件: $SQL_FILE"
    exit 1
fi

# 构建psql连接字符串
PSQL_CONN="postgresql://$DB_USER"
if [[ -n "$DB_PASSWORD" ]]; then
    PSQL_CONN="$PSQL_CONN:$DB_PASSWORD"
fi
PSQL_CONN="$PSQL_CONN@$DB_HOST:$DB_PORT/$DB_NAME"

print_message $BLUE "===================================================="
print_message $BLUE "AIShowLab 模型管理数据导入脚本"
print_message $BLUE "===================================================="
print_message $YELLOW "数据库连接信息:"
print_message $YELLOW "  主机: $DB_HOST"
print_message $YELLOW "  端口: $DB_PORT"
print_message $YELLOW "  数据库: $DB_NAME"
print_message $YELLOW "  用户: $DB_USER"
print_message $YELLOW "  SQL文件: $SQL_FILE"

if [[ "$DRY_RUN" == true ]]; then
    print_message $YELLOW "  模式: 仅验证SQL语法（不执行导入）"
else
    print_message $YELLOW "  模式: 执行数据导入"
fi

print_message $BLUE "===================================================="

# 验证数据库连接
print_message $YELLOW "正在验证数据库连接..."
if ! psql "$PSQL_CONN" -c "SELECT version();" > /dev/null 2>&1; then
    print_message $RED "错误: 无法连接到数据库"
    print_message $RED "请检查连接参数或确保数据库服务正在运行"
    exit 1
fi
print_message $GREEN "✓ 数据库连接成功"

# 检查必要的表是否存在
print_message $YELLOW "正在检查数据库表结构..."
REQUIRED_TABLES=("a_model_category" "a_model_provider" "a_model" "a_model_feature" "a_model_output_format" "a_model_api_key" "a_model_visibility")

for table in "${REQUIRED_TABLES[@]}"; do
    if ! psql "$PSQL_CONN" -c "SELECT 1 FROM $table LIMIT 1;" > /dev/null 2>&1; then
        print_message $RED "错误: 表 $table 不存在"
        print_message $RED "请先执行数据库架构脚本创建表结构"
        exit 1
    fi
done
print_message $GREEN "✓ 所有必要的表都存在"

if [[ "$DRY_RUN" == true ]]; then
    # 仅验证SQL语法
    print_message $YELLOW "正在验证SQL语法..."
    if psql "$PSQL_CONN" -f "$SQL_FILE" --single-transaction --set ON_ERROR_STOP=on --dry-run > /dev/null 2>&1; then
        print_message $GREEN "✓ SQL语法验证通过"
    else
        print_message $RED "✗ SQL语法验证失败"
        exit 1
    fi
else
    # 执行数据导入
    print_message $YELLOW "正在导入模型管理数据..."
    
    # 使用事务确保数据一致性
    if psql "$PSQL_CONN" -f "$SQL_FILE" --single-transaction --set ON_ERROR_STOP=on; then
        print_message $GREEN "✓ 数据导入成功"
        
        # 执行验证脚本（如果存在）
        if [[ -f "$VALIDATION_FILE" ]]; then
            print_message $YELLOW "正在执行数据验证..."
            psql "$PSQL_CONN" -f "$VALIDATION_FILE" > /tmp/validation_result.txt 2>&1
            print_message $GREEN "✓ 数据验证完成，结果已保存到 /tmp/validation_result.txt"
        fi
    else
        print_message $RED "✗ 数据导入失败"
        exit 1
    fi
fi

print_message $BLUE "===================================================="
print_message $GREEN "操作完成！"

if [[ "$DRY_RUN" == false ]]; then
    print_message $YELLOW "导入的数据包括:"
    print_message $YELLOW "  - 6个模型分类"
    print_message $YELLOW "  - 12个模型供应商"
    print_message $YELLOW "  - 28个AI模型（LLM、图像生成、视频生成、多模态）"
    print_message $YELLOW "  - 模型特性配置"
    print_message $YELLOW "  - 输出格式定义"
    print_message $YELLOW "  - API密钥示例"
    print_message $YELLOW "  - 可见性权限控制"
    print_message $YELLOW ""
    print_message $YELLOW "建议执行以下查询验证数据:"
    print_message $YELLOW "  SELECT c.name, COUNT(m.id) FROM a_model_category c LEFT JOIN a_model m ON c.id = m.category_id GROUP BY c.name;"
fi

print_message $BLUE "===================================================="
