package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新会员助手实例模型参数
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantUpdateModelParam {
    
    /**
     * 会员助手实例ID
     */
    @NotNull(message = "会员助手实例ID不能为空")
    private Long memberAssistantId;
    
    /**
     * 新的模型ID
     */
    @NotNull(message = "模型ID不能为空")
    private Long modelId;
}
