<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="知识库" prop="knowledgeId">
        <el-input
          v-model="queryParams.knowledgeId"
          placeholder="请输入知识库"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="原文件名" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入原文件名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="MIME类型" prop="fileType">
        <el-select v-model="queryParams.fileType" placeholder="请选择MIME类型" clearable>
          <el-option
            v-for="dict in file_mime_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="processingStatus">
        <el-select v-model="queryParams.processingStatus" placeholder="请选择处理状态" clearable>
          <el-option
            v-for="dict in doc_processing_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文本块数量" prop="chunkCount">
        <el-input
          v-model="queryParams.chunkCount"
          placeholder="请输入文本块数量"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:knowledgeDoc:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:knowledgeDoc:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:knowledgeDoc:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:knowledgeDoc:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="knowledgeDocList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="知识库" align="center" prop="knowledgeId" />
      <el-table-column label="原文件名" align="center" prop="fileName" />
      <el-table-column label="MIME类型" align="center" prop="fileType">
        <template #default="scope">
          <dict-tag :options="file_mime_type" :value="scope.row.fileType"/>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" align="center" prop="fileSize" />
      <el-table-column label="存储路径" align="center" prop="storagePath" />
      <el-table-column label="处理状态" align="center" prop="processingStatus">
        <template #default="scope">
          <dict-tag :options="doc_processing_status" :value="scope.row.processingStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="错误信息" align="center" prop="errorMessage" />
      <el-table-column label="文本块数量" align="center" prop="chunkCount" />
      <el-table-column label="总字符数" align="center" prop="charCount" />
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:knowledgeDoc:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:knowledgeDoc:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库文档对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="knowledgeDocRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="知识库" prop="knowledgeId">
          <el-input v-model="form.knowledgeId" placeholder="请输入知识库" />
        </el-form-item>
        <el-form-item label="原文件名" prop="fileName">
          <el-input v-model="form.fileName" placeholder="请输入原文件名" />
        </el-form-item>
        <el-form-item label="MIME类型" prop="fileType">
          <el-select v-model="form.fileType" placeholder="请选择MIME类型">
            <el-option
              v-for="dict in file_mime_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件大小" prop="fileSize">
          <el-input v-model="form.fileSize" placeholder="请输入文件大小" />
        </el-form-item>
        <el-form-item label="存储路径" prop="storagePath">
          <el-input v-model="form.storagePath" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="处理状态" prop="processingStatus">
          <el-radio-group v-model="form.processingStatus">
            <el-radio
              v-for="dict in doc_processing_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="错误信息" prop="errorMessage">
          <el-input v-model="form.errorMessage" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文本块数量" prop="chunkCount">
          <el-input v-model="form.chunkCount" placeholder="请输入文本块数量" />
        </el-form-item>
        <el-form-item label="总字符数" prop="charCount">
          <el-input v-model="form.charCount" placeholder="请输入总字符数" />
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
        <el-divider content-position="center">文档分块信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" @click="handleAddKnowledgeDocChunk">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="Delete" @click="handleDeleteKnowledgeDocChunk">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="knowledgeDocChunkList" :row-class-name="rowKnowledgeDocChunkIndex" @selection-change="handleKnowledgeDocChunkSelectionChange" ref="knowledgeDocChunk">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50"/>
          <el-table-column label="ID" prop="id" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.id" placeholder="请输入ID" />
            </template>
          </el-table-column>
          <el-table-column label="字数" prop="charCount" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.charCount" placeholder="请输入字数" />
            </template>
          </el-table-column>
          <el-table-column label="软删除时间" prop="deleteTime" width="240">
            <template #default="scope">
              <el-date-picker clearable
                v-model="scope.row.deleteTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择软删除时间">
              </el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="更新时间" prop="updateTime" width="240">
            <template #default="scope">
              <el-date-picker clearable
                v-model="scope.row.updateTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择更新时间">
              </el-date-picker>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeDoc">
import { listKnowledgeDoc, getKnowledgeDoc, delKnowledgeDoc, addKnowledgeDoc, updateKnowledgeDoc } from "@/api/bff/knowledge/doc"

const { proxy } = getCurrentInstance()
const { file_mime_type, doc_processing_status } = proxy.useDict('file_mime_type', 'doc_processing_status')

const knowledgeDocList = ref([])
const knowledgeDocChunkList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const checkedKnowledgeDocChunk = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    knowledgeId: null,
    fileName: null,
    fileType: null,
    storagePath: null,
    processingStatus: null,
    errorMessage: null,
    chunkCount: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询知识库文档列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listKnowledgeDoc(queryParams.value).then(response => {
    knowledgeDocList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    knowledgeId: null,
    fileName: null,
    fileType: null,
    fileSize: null,
    storagePath: null,
    processingStatus: null,
    errorMessage: null,
    chunkCount: null,
    charCount: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  }
  knowledgeDocChunkList.value = []
  proxy.resetForm("knowledgeDocRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加知识库文档"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getKnowledgeDoc(_id).then(response => {
    form.value = response.data
    knowledgeDocChunkList.value = response.data.knowledgeDocChunkList
    open.value = true
    title.value = "修改知识库文档"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["knowledgeDocRef"].validate(valid => {
    if (valid) {
      form.value.knowledgeDocChunkList = knowledgeDocChunkList.value
      if (form.value.id != null) {
        updateKnowledgeDoc(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addKnowledgeDoc(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除知识库文档编号为"' + _ids + '"的数据项？').then(function() {
    return delKnowledgeDoc(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 文档分块序号 */
function rowKnowledgeDocChunkIndex({ row, rowIndex }) {
  row.index = rowIndex + 1
}

/** 文档分块添加按钮操作 */
function handleAddKnowledgeDocChunk() {
  let obj = {}
  obj.id = ""
  obj.charCount = ""
  obj.deleteTime = ""
  obj.updateTime = ""
  knowledgeDocChunkList.value.push(obj)
}

/** 文档分块删除按钮操作 */
function handleDeleteKnowledgeDocChunk() {
  if (checkedKnowledgeDocChunk.value.length == 0) {
    proxy.$modal.msgError("请先选择要删除的文档分块数据")
  } else {
    const knowledgeDocChunks = knowledgeDocChunkList.value
    const checkedKnowledgeDocChunks = checkedKnowledgeDocChunk.value
    knowledgeDocChunkList.value = knowledgeDocChunks.filter(function(item) {
      return checkedKnowledgeDocChunks.indexOf(item.index) == -1
    })
  }
}

/** 复选框选中数据 */
function handleKnowledgeDocChunkSelectionChange(selection) {
  checkedKnowledgeDocChunk.value = selection.map(item => item.index)
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/knowledgeDoc/export', {
    ...queryParams.value
  }, `knowledgeDoc_${new Date().getTime()}.xlsx`)
}

getList()
</script>
