package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.time.OffsetDateTime;

/**
 * 交易记录列表查询参数
 * 
 * <AUTHOR>
 */
@Data
public class BillingTransactionListParam {
    
    /**
     * 交易类型，可选 (字典: transaction_type), 1-消费, 2-充值, 3-退款, 4-赠送, 5-分润
     */
    private Integer type;
    
    /**
     * 开始时间，可选
     */
    private OffsetDateTime startTime;
    
    /**
     * 结束时间，可选
     */
    private OffsetDateTime endTime;
    
    /**
     * 最小金额，可选（绝对值）
     */
    private java.math.BigDecimal minAmount;
    
    /**
     * 最大金额，可选（绝对值）
     */
    private java.math.BigDecimal maxAmount;
    
    /**
     * 关键词搜索，可选（搜索交易描述）
     */
    private String keyword;
    
    /**
     * 排序字段，可选 (transaction_time-交易时间, amount-金额)
     */
    private String sortBy = "transaction_time";
    
    /**
     * 排序方向，可选 (asc-升序, desc-降序)
     */
    private String sortOrder = "desc";
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
