<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.permission.PermissionMapper">

    <!-- 统计会员通过角色拥有的指定权限数量 -->
    <select id="countMemberRolePermission" resultType="Integer">
        SELECT COUNT(DISTINCT fp.id)
        FROM a_member_role mr
        INNER JOIN a_func_role fr ON mr.role_code = fr.role_code
        INNER JOIN a_func_permission fp ON fr.permission_code = fp.code
        WHERE mr.member_id = #{memberId}
        AND fp.code = #{permissionCode}
        AND mr.delete_time IS NULL
        AND fr.delete_time IS NULL
        AND fp.delete_time IS NULL
        AND mr.is_active = true
        AND fr.is_active = true
        AND fp.is_active = true
    </select>

    <!-- 获取会员的直接权限状态 -->
    <select id="getMemberDirectPermission" resultType="Boolean">
        SELECT mp.is_granted
        FROM a_member_permission mp
        INNER JOIN a_func_permission fp ON mp.permission_code = fp.code
        WHERE mp.member_id = #{memberId}
        AND fp.code = #{permissionCode}
        AND mp.delete_time IS NULL
        AND fp.delete_time IS NULL
        AND fp.is_active = true
        LIMIT 1
    </select>

    <!-- 获取会员通过角色拥有的所有权限编码列表 -->
    <select id="getMemberRolePermissions" resultType="String">
        SELECT DISTINCT fp.code
        FROM a_member_role mr
        INNER JOIN a_func_role fr ON mr.role_code = fr.role_code
        INNER JOIN a_func_permission fp ON fr.permission_code = fp.code
        WHERE mr.member_id = #{memberId}
        AND mr.delete_time IS NULL
        AND fr.delete_time IS NULL
        AND fp.delete_time IS NULL
        AND mr.is_active = true
        AND fr.is_active = true
        AND fp.is_active = true
        ORDER BY fp.code
    </select>

    <!-- 获取会员的直接权限编码列表 -->
    <select id="getMemberDirectPermissions" resultType="String">
        SELECT DISTINCT fp.code
        FROM a_member_permission mp
        INNER JOIN a_func_permission fp ON mp.permission_code = fp.code
        WHERE mp.member_id = #{memberId}
        AND mp.is_granted = true
        AND mp.delete_time IS NULL
        AND fp.delete_time IS NULL
        AND fp.is_active = true
        ORDER BY fp.code
    </select>

    <!-- 获取会员的所有角色编码列表 -->
    <select id="getMemberRoles" resultType="String">
        SELECT mr.role_code
        FROM a_member_role mr
        WHERE mr.member_id = #{memberId}
        AND mr.delete_time IS NULL
        AND mr.is_active = true
        ORDER BY mr.role_code
    </select>

    <!-- 统计指定权限编码的数量 -->
    <select id="countPermissionByCode" resultType="Integer">
        SELECT COUNT(*)
        FROM a_func_permission fp
        WHERE fp.code = #{permissionCode}
        AND fp.delete_time IS NULL
        AND fp.is_active = true
    </select>

    <!-- 检查会员是否拥有指定角色 -->
    <select id="countMemberRole" resultType="Integer">
        SELECT COUNT(*)
        FROM a_member_role mr
        WHERE mr.member_id = #{memberId}
        AND mr.role_code = #{roleCode}
        AND mr.delete_time IS NULL
        AND mr.is_active = true
    </select>

    <!-- 检查角色是否拥有指定权限 -->
    <select id="countRolePermission" resultType="Integer">
        SELECT COUNT(*)
        FROM a_func_role fr
        INNER JOIN a_func_permission fp ON fr.permission_code = fp.code
        WHERE fr.role_code = #{roleCode}
        AND fp.code = #{permissionCode}
        AND fr.delete_time IS NULL
        AND fp.delete_time IS NULL
        AND fr.is_active = true
        AND fp.is_active = true
    </select>

</mapper>
