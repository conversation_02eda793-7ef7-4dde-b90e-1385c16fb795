<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员" prop="memberId">
        <el-input
          v-model="queryParams.memberId"
          placeholder="请输入会员"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模型" prop="modelId">
        <el-input
          v-model="queryParams.modelId"
          placeholder="请输入模型"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="模型来源" prop="modelSource">
        <el-input
          v-model="queryParams.modelSource"
          placeholder="请输入模型来源"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="调用状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择调用状态" clearable>
          <el-option
            v-for="dict in request_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起请求时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeRequestTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:logModelRequest:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:logModelRequest:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:logModelRequest:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:logModelRequest:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logModelRequestList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="请求ID" align="center" prop="requestId" />
      <el-table-column label="会员" align="center" prop="memberId" />
      <el-table-column label="模型" align="center" prop="modelId" />
      <el-table-column label="模型来源" align="center" prop="modelSource">
        <template #default="scope">
          <dict-tag :options="model_source" :value="scope.row.modelSource"/>
        </template>
      </el-table-column>
      <el-table-column label="输入摘要" align="center" prop="inputSummary" />
      <el-table-column label="调用参数" align="center" prop="inputParams" />
      <el-table-column label="调用状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="request_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="IP" align="center" prop="sourceIp" />
      <el-table-column label="user-agent" align="center" prop="userAgent" />
      <el-table-column label="发起请求时间" align="center" prop="requestTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:logModelRequest:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:logModelRequest:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改模型调用请求对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="logModelRequestRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="会员" prop="memberId">
          <el-input v-model="form.memberId" placeholder="请输入会员" />
        </el-form-item>
        <el-form-item label="模型" prop="modelId">
          <el-input v-model="form.modelId" placeholder="请输入模型" />
        </el-form-item>
        <el-form-item label="模型来源" prop="modelSource">
          <el-input v-model="form.modelSource" placeholder="请输入模型来源" />
        </el-form-item>
        <el-form-item label="输入摘要" prop="inputSummary">
          <el-input v-model="form.inputSummary" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="请求体" prop="requestBody">
          <el-input v-model="form.requestBody" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="调用状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in request_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="user-agent" prop="userAgent">
          <el-input v-model="form.userAgent" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="发起请求时间" prop="requestTime">
          <el-date-picker clearable
            v-model="form.requestTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择发起请求时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LogModelRequest">
import { listLogModelRequest, getLogModelRequest, delLogModelRequest, addLogModelRequest, updateLogModelRequest } from "@/api/bff/log/model/request"

const { proxy } = getCurrentInstance()
const { request_status } = proxy.useDict('request_status')

const logModelRequestList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeRequestTime = ref([])
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    requestId: null,
    memberId: null,
    modelId: null,
    modelSource: null,
    inputSummary: null,
    inputParams: null,
    status: null,
    sourceIp: null,
    userAgent: null,
    requestTime: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询模型调用请求列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeRequestTime && '' != daterangeRequestTime) {
    queryParams.value.params["beginRequestTime"] = daterangeRequestTime.value[0]
    queryParams.value.params["endRequestTime"] = daterangeRequestTime.value[1]
  }
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listLogModelRequest(queryParams.value).then(response => {
    logModelRequestList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    requestId: null,
    memberId: null,
    modelId: null,
    modelSource: null,
    inputSummary: null,
    inputParams: null,
    requestBody: null,
    status: null,
    sourceIp: null,
    userAgent: null,
    requestTime: null,
    deleteTime: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("logModelRequestRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeRequestTime.value = []
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加模型调用请求"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getLogModelRequest(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改模型调用请求"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["logModelRequestRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateLogModelRequest(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addLogModelRequest(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除模型调用请求编号为"' + _ids + '"的数据项？').then(function() {
    return delLogModelRequest(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/log/model/request/export', {
    ...queryParams.value
  }, `logModelRequest_${new Date().getTime()}.xlsx`)
}

getList()
</script>
