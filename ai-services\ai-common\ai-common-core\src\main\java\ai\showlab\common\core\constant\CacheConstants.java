package ai.showlab.common.core.constant;

/**
 * 缓存key定义
 *
 * <AUTHOR>
 */
public class CacheConstants {

    /**
     * BFF 缓存 key
     */
    public static final String BFF_LANG_KEY = "bff:lang";
    public static final String BFF_DOCUMENT_KEY = "bff:document";
    public static final String BFF_CURRENCY_KEY = "bff:currency";
    public static final String BFF_COUNTRY_KEY = "bff:country";

    /**
     * BFF 会员信息缓存 key
     */
    public static final String BFF_MEMBER_PROFILE = "bff:member:profile";

    public static final String BFF_MEMBER_INFO_VO = "bff:member:info";

    /**
     * BFF 会员认证方式缓存 key
     */
    public static final String BFF_MEMBER_AUTH_KEY = "bff:member:auth";

    /**
     * BFF 会员会话缓存 key (按 Token)
     */
    public static final String BFF_MEMBER_SESSION_BY_TOKEN_KEY = "bff:member:session:token";

    /**
     * BFF 会员会话列表缓存 key (按 Member ID)
     */
    public static final String BFF_MEMBER_SESSIONS_BY_ID_KEY = "bff:member:sessions:id";

    /**
     * BFF 会员菜单缓存 key (按 Member ID)
     */
    public static final String BFF_MEMBER_MENUS_KEY = "bff:member:menus";

    /**
     * BFF 会员角色缓存 key (按 Member ID)
     */
    public static final String BFF_MEMBER_ROLES_KEY = "bff:member:roles";

    /**
     * BFF 会员特定权限缓存 key (按 Member ID)
     */
    public static final String BFF_MEMBER_PERMISSIONS_KEY = "bff:member:permissions";

    public static final String PASSWORD_RESET_CODE_PREFIX = "bff:member:pwd:reset:code:";

    /**
     * BFF 权限组缓存 key
     */
    public static final String BFF_PERMISSION_GROUPS_KEY = "bff:permission:groups";

    /**
     * BFF AI助手缓存 key
     */
    public static final String BFF_ASSISTANTS_KEY = "bff:assistants";

    /**
     * BFF AI助手分类缓存 key
     */
    public static final String BFF_ASSISTANT_CATEGORIES_KEY = "bff:assistant:categories";

    /**
     * BFF 计费套餐缓存 key
     */
    public static final String BFF_BILLING_PACKAGES_KEY = "bff:billing:packages";

    /**
     * BFF 会员余额缓存 key
     */
    public static final String BFF_MEMBER_BALANCE_KEY = "bff:member:balance";

    /**
     * BFF 会员订阅缓存 key
     */
    public static final String BFF_MEMBER_SUBSCRIPTIONS_KEY = "bff:member:subscriptions";

    /**
     * BFF 知识库列表缓存 key
     */
    public static final String BFF_KNOWLEDGE_BASES_KEY = "bff:knowledge:bases";

    /**
     * BFF 知识库详情缓存 key
     */
    public static final String BFF_KNOWLEDGE_BASE_DETAIL_KEY = "bff:knowledge:base:detail";

    /**
     * BFF 会员助手列表缓存 key (按 Member ID)
     */
    public static final String BFF_MEMBER_ASSISTANTS_KEY = "bff:member:assistants";

    /**
     * 系统数据字典缓存 key
     */
    public static final String SYS_DICT_KEY = "sys_dict";

    /**
     * 模型市场相关缓存 key
     */
    public static final String BFF_MODEL_CATEGORIES_KEY = "bff:model:categories";
    public static final String BFF_MODEL_PROVIDERS_KEY = "bff:model:providers";
    public static final String BFF_MODELS_KEY = "bff:models";
    public static final String BFF_MODEL_API_KEYS_KEY = "bff:model:api_keys";
    public static final String BFF_MODEL_FEATURES_KEY = "bff:model:features";
    public static final String BFF_MODEL_OUTPUT_FORMATS_KEY = "bff:model:output_formats";
    public static final String BFF_MODEL_VISIBILITY_KEY = "bff:model:visibility";

    /**
     * 支付网关
     */
    public static final String BFF_PAYMENT_GATEWAY_KEY = "bff:paymentGateway";

    /**
     * BFF Token 黑名单 key
     */
    public static final String BFF_TOKEN_BLACKLIST_KEY = "bff:token:blacklist";

    public static final String DUPLICATE_SUBMISSION_PREFIX = "bff:token:duplicate:";

    /**
     * BFF 重复登录检查缓存 key
     */
    public static final String BFF_DUPLICATE_LOGIN_CHECK_KEY = "bff:duplicate:login:check";

    /**
     * BFF 会员在线状态缓存 key
     */
    public static final String BFF_MEMBER_ONLINE_STATUS_KEY = "bff:member:online:status";

    /**
     * BFF 会员使用统计缓存 key
     */
    public static final String BFF_MEMBER_USAGE_STATS_KEY = "bff:member:usage:stats";

    /**
     * BFF 助手使用统计缓存 key
     */
    public static final String BFF_ASSISTANT_USAGE_STATS_KEY = "bff:assistant:usage:stats";

    /**
     * BFF 会员助手实例使用统计缓存 key
     */
    public static final String BFF_MEMBER_ASSISTANT_USAGE_STATS_KEY = "bff:member:assistant:usage:stats";

    /**
     * BFF 会员权限缓存 key
     */
    public static final String BFF_MEMBER_PERMISSION_KEY = "bff:member:permission";

    /**
     * BFF 会员角色权限缓存 key
     */
    public static final String BFF_MEMBER_ROLE_PERMISSION_KEY = "bff:member:role:permission";

    /**
     * BFF 会员直接权限缓存 key
     */
    public static final String BFF_MEMBER_DIRECT_PERMISSION_KEY = "bff:member:direct:permission";

    /**
     * BFF 会员所有权限缓存 key
     */
    public static final String BFF_MEMBER_ALL_PERMISSIONS_KEY = "bff:member:all:permissions";

    /**
     * BFF 权限存在性缓存 key
     */
    public static final String BFF_PERMISSION_EXISTS_KEY = "bff:permission:exists";

}
