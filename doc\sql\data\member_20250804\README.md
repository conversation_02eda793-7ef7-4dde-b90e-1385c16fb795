# AIShowLab 会员功能和前端菜单权限模块模拟数据

本目录包含了AIShowLab项目会员功能和前端菜单权限模块的完整模拟数据，为开发测试和功能演示提供丰富的数据支持。

## 📁 文件列表

### 核心数据文件
- **`会员功能模拟数据.sql`** - 会员功能模块模拟数据SQL脚本
- **`前端菜单权限模拟数据.sql`** - 前端菜单权限模块模拟数据SQL脚本
- **`数据验证脚本.sql`** - 数据验证和统计查询脚本

### 导入工具
- **`导入数据脚本.sh`** - Linux/macOS 导入脚本
- **`导入数据脚本.bat`** - Windows 导入脚本

### 文档文件
- **`README.md`** - 本文件，项目概览

## 🚀 快速开始

### 前置条件
1. PostgreSQL 数据库已安装并运行
2. 已创建数据库和相关表结构（执行 `doc/sql/design/会员功能.sql` 和 `doc/sql/design/前端菜单权限.sql`）
3. 已导入基础数据（货币、数据字典等）

### 导入数据

#### Linux/macOS
```bash
# 导入所有模块数据
./导入数据脚本.sh -d aishowlab -u postgres

# 仅导入会员功能模块数据
./导入数据脚本.sh --member-only -d aishowlab -u postgres

# 仅导入权限模块数据
./导入数据脚本.sh --permission-only -d aishowlab -u postgres

# 验证SQL语法
./导入数据脚本.sh --dry-run
```

#### Windows
```cmd
REM 导入所有模块数据
导入数据脚本.bat -d aishowlab -u postgres

REM 仅导入会员功能模块数据
导入数据脚本.bat --member-only -d aishowlab -u postgres

REM 仅导入权限模块数据
导入数据脚本.bat --permission-only -d aishowlab -u postgres

REM 验证SQL语法
导入数据脚本.bat --dry-run
```

#### 手动导入
```bash
# 导入会员功能数据
psql -d aishowlab -f 会员功能模拟数据.sql

# 导入权限数据
psql -d aishowlab -f 前端菜单权限模拟数据.sql

# 验证数据
psql -d aishowlab -f 数据验证脚本.sql
```

## 📊 数据概览

### 会员功能模块

#### 会员账户 (15个)
- **管理员账户** (1个): admin - 系统超级管理员
- **普通会员** (10个): user001-user010 - 不同等级的普通用户
- **待激活用户** (1个): user011 - 注册但未激活
- **禁用用户** (1个): user012 - 被禁用的用户
- **访客用户** (2个): guest001-guest002 - 临时访客

#### 会员等级分布
- **免费会员**: 基础功能权限
- **VIP会员**: 增强功能权限
- **企业会员**: 高级功能权限

#### 认证方式
- **用户名密码**: 所有用户的基础认证方式
- **手机号**: 绑定手机号认证
- **邮箱**: 邮箱验证认证
- **第三方登录**: 微信、GitHub等社交登录

#### 会话管理
- **PC端会话**: 桌面浏览器会话
- **移动端会话**: 手机、平板会话
- **会话状态**: 活跃、过期会话管理
- **设备信息**: 详细的设备和浏览器信息

#### 邀请关系
- 完整的邀请关系链
- 邀请码管理
- 多层级邀请统计

### 前端菜单权限模块

#### 菜单结构 (6个一级菜单 + 20+个二级菜单)
1. **工作台** - 系统首页和概览
2. **AI助手** - 智能对话、助手广场、我的助手、创建助手
3. **模型管理** - 模型列表、分类、供应商、API密钥
4. **计费管理** - 使用统计、套餐、订单、余额
5. **会员中心** - 个人资料、安全设置、邀请好友
6. **系统管理** - 用户、角色、权限、菜单、日志

#### 功能权限 (40+个)
- **页面权限**: 控制页面访问
- **功能权限**: 控制具体功能操作
- **数据权限**: 控制数据访问范围

#### 角色体系 (7个角色)
1. **超级管理员** - 拥有所有权限
2. **系统管理员** - 拥有大部分管理权限
3. **内容审核员** - 负责内容审核
4. **企业用户** - 高级功能权限
5. **VIP用户** - 部分高级功能权限
6. **普通用户** - 基础功能权限
7. **访客用户** - 仅查看权限

#### 权限组 (7个)
- **基础功能** - 最基本的查看和使用权限
- **AI助手功能** - AI助手相关权限
- **模型管理** - 模型管理相关权限
- **计费管理** - 计费管理相关权限
- **会员功能** - 会员中心相关权限
- **系统管理** - 系统管理相关权限
- **高级功能** - 高级功能权限

## 🔧 技术特点

### 1. 完整的权限体系
- **RBAC模型**: 基于角色的访问控制
- **细粒度权限**: 页面、功能、数据三级权限控制
- **权限继承**: 角色权限继承和组合
- **动态权限**: 支持运行时权限检查

### 2. 灵活的会员管理
- **多种认证方式**: 支持多种登录方式
- **会话管理**: 完整的会话生命周期管理
- **等级体系**: 支持会员等级升级
- **邀请机制**: 完整的邀请关系管理

### 3. 真实的业务场景
- **合理的数据分布**: 符合实际使用模式
- **完整的状态管理**: 涵盖各种业务状态
- **时间序列数据**: 模拟真实的时间分布

## ⚠️ 注意事项

### 安全考虑
- **密码加密**: 所有密码都使用BCrypt加密
- **会话安全**: JWT Token管理和过期控制
- **权限验证**: 严格的权限检查机制

### 数据依赖
- **基础数据**: 需要货币、数据字典等基础数据支持
- **表结构**: 依赖完整的数据库表结构
- **外键约束**: 确保数据关联完整性

### 业务规则
- **角色权限**: 不同角色拥有不同的权限范围
- **会员等级**: 等级决定功能访问权限
- **会话管理**: 支持多设备登录和会话控制

## 📈 使用场景

1. **开发测试** - 为开发环境提供完整的测试数据
2. **功能演示** - 展示会员和权限功能的完整性
3. **权限测试** - 验证不同角色的权限控制
4. **性能测试** - 提供足够的数据量进行性能测试
5. **培训教学** - 作为权限管理的学习案例

## 🔍 数据验证

执行验证脚本检查数据完整性：
```bash
psql -d aishowlab -f 数据验证脚本.sql
```

验证内容包括：
- 会员类型和等级分布
- 认证方式统计
- 会话设备类型分析
- 菜单权限结构检查
- 角色权限关联验证
- 数据完整性检查
- 业务逻辑一致性验证

## 🎯 权限控制示例

### 角色权限矩阵
| 功能模块 | 访客 | 普通用户 | VIP用户 | 企业用户 | 管理员 |
|---------|------|----------|---------|----------|--------|
| 工作台查看 | ✓ | ✓ | ✓ | ✓ | ✓ |
| AI助手对话 | ✗ | ✓ | ✓ | ✓ | ✓ |
| 创建助手 | ✗ | ✗ | ✓ | ✓ | ✓ |
| 模型管理 | ✗ | ✗ | ✗ | ✗ | ✓ |
| 系统管理 | ✗ | ✗ | ✗ | ✗ | ✓ |

### 菜单显示控制
- 根据用户角色动态显示菜单
- 通过 `status` 字段控制菜单启用/禁用状态
- 支持菜单项的层级结构和排序

## 📞 支持

如有问题或建议，请参考：
- 数据验证脚本: `数据验证脚本.sql`
- 项目设计文档: `doc/sql/design/会员功能.sql` 和 `doc/sql/design/前端菜单权限.sql`
- 导入工具帮助: `./导入数据脚本.sh --help`

---

**AIShowLab Team**  
*让AI触手可及*
