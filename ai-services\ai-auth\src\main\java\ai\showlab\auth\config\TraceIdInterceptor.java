package ai.showlab.auth.config;

import ai.showlab.common.constants.BaseConstants;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * Interceptor to extract traceId from request headers and set it in MDC.
 *
 * <AUTHOR>
 */
@Component
public class TraceIdInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String traceId = request.getHeader(BaseConstants.TRACE_ID_HEADER);
        if (traceId != null && !traceId.isEmpty()) {
            MDC.put(BaseConstants.REQ_ID_MDC_KEY, traceId);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // Not used for this purpose
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        MDC.remove(BaseConstants.REQ_ID_MDC_KEY);
    }
} 