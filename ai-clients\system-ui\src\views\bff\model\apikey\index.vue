<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="providerId">
        <el-input
          v-model="queryParams.providerId"
          placeholder="请输入供应商"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="queryParams.priority" placeholder="请选择优先级" clearable>
          <el-option
            v-for="dict in api_key_priority"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="权重" prop="weight">
        <el-select v-model="queryParams.weight" placeholder="请选择权重" clearable>
          <el-option
            v-for="dict in api_key_weight"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="总额度" prop="quota">
        <el-input
          v-model="queryParams.quota"
          placeholder="请输入总额度"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="已用额度" prop="usedQuota">
        <el-input
          v-model="queryParams.usedQuota"
          placeholder="请输入已用额度"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货币单位" prop="currencyId">
        <el-input
          v-model="queryParams.currencyId"
          placeholder="请输入货币单位"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="密钥状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择密钥状态" clearable>
          <el-option
            v-for="dict in api_key_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="最近使用时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeLastUsedTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:modelApiKey:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:modelApiKey:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:modelApiKey:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:modelApiKey:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="modelApiKeyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="供应商" align="center" prop="providerId" />
      <el-table-column label="代理URL" align="center" prop="apiEndpointOverride" />
      <el-table-column label="优先级" align="center" prop="priority">
        <template #default="scope">
          <dict-tag :options="api_key_priority" :value="scope.row.priority"/>
        </template>
      </el-table-column>
      <el-table-column label="权重" align="center" prop="weight">
        <template #default="scope">
          <dict-tag :options="api_key_weight" :value="scope.row.weight"/>
        </template>
      </el-table-column>
      <el-table-column label="总额度" align="center" prop="quota" />
      <el-table-column label="已用额度" align="center" prop="usedQuota" />
      <el-table-column label="货币单位" align="center" prop="currencyId" />
      <el-table-column label="密钥状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="api_key_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="最近使用时间" align="center" prop="lastUsedTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastUsedTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:modelApiKey:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:modelApiKey:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改API密钥对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="modelApiKeyRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="供应商" prop="providerId">
          <el-input v-model="form.providerId" placeholder="请输入供应商" />
        </el-form-item>
        <el-form-item label="API密钥" prop="apiKey">
          <el-input v-model="form.apiKey" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="代理URL" prop="apiEndpointOverride">
          <el-input v-model="form.apiEndpointOverride" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="form.priority" placeholder="请选择优先级">
            <el-option
              v-for="dict in api_key_priority"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="权重" prop="weight">
          <el-select v-model="form.weight" placeholder="请选择权重">
            <el-option
              v-for="dict in api_key_weight"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="总额度" prop="quota">
          <el-input v-model="form.quota" placeholder="请输入总额度" />
        </el-form-item>
        <el-form-item label="已用额度" prop="usedQuota">
          <el-input v-model="form.usedQuota" placeholder="请输入已用额度" />
        </el-form-item>
        <el-form-item label="货币单位" prop="currencyId">
          <el-input v-model="form.currencyId" placeholder="请输入货币单位" />
        </el-form-item>
        <el-form-item label="密钥状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择密钥状态">
            <el-option
              v-for="dict in api_key_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="最近使用时间" prop="lastUsedTime">
          <el-date-picker clearable
            v-model="form.lastUsedTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择最近使用时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ModelApiKey">
import { listModelApiKey, getModelApiKey, delModelApiKey, addModelApiKey, updateModelApiKey } from "@/api/bff/model/apikey"

const { proxy } = getCurrentInstance()
const { api_key_priority, api_key_status, api_key_weight } = proxy.useDict('api_key_priority', 'api_key_status', 'api_key_weight')

const modelApiKeyList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeLastUsedTime = ref([])
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    providerId: null,
    priority: null,
    weight: null,
    quota: null,
    usedQuota: null,
    currencyId: null,
    status: null,
    lastUsedTime: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询API密钥列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeLastUsedTime && '' != daterangeLastUsedTime) {
    queryParams.value.params["beginLastUsedTime"] = daterangeLastUsedTime.value[0]
    queryParams.value.params["endLastUsedTime"] = daterangeLastUsedTime.value[1]
  }
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listModelApiKey(queryParams.value).then(response => {
    modelApiKeyList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    providerId: null,
    apiKey: null,
    apiEndpointOverride: null,
    priority: null,
    weight: null,
    quota: null,
    usedQuota: null,
    currencyId: null,
    status: null,
    description: null,
    lastUsedTime: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("modelApiKeyRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeLastUsedTime.value = []
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加API密钥"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getModelApiKey(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改API密钥"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["modelApiKeyRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateModelApiKey(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addModelApiKey(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除API密钥编号为"' + _ids + '"的数据项？').then(function() {
    return delModelApiKey(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/model/apikey/export', {
    ...queryParams.value
  }, `modelApiKey_${new Date().getTime()}.xlsx`)
}

getList()
</script>
