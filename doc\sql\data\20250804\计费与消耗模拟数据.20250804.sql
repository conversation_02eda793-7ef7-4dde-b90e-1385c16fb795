-- ====================================================================================
-- AIShowLab - 计费与消耗模块模拟数据 (PostgreSQL)
-- 文件名: 计费与消耗模拟数据.20250804.sql
-- 作者: AI Assistant
-- 描述: 为计费与消耗相关表生成模拟数据，包括计费方案、套餐、价格、使用记录、余额、流水、订单等
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. 计费方案数据 (a_billing_plan)
-- ==================================================
INSERT INTO a_billing_plan (name, unit, sort_order, description, create_by, update_by) VALUES
('按Token计费', 1, 1, '根据模型处理的Token数量进行计费，适用于大语言模型', 'system', 'system'),
('按次数计费', 2, 2, '根据调用次数进行计费，适用于简单的API调用', 'system', 'system'),
('按图片计费', 3, 3, '根据生成或处理的图片数量进行计费，适用于图像生成模型', 'system', 'system'),
('按秒计费', 4, 4, '根据音频或视频的时长进行计费，适用于语音和视频处理', 'system', 'system'),
('包月计费', 2, 5, '固定月费，不限使用次数，适用于高频用户', 'system', 'system'),
('按分钟计费', 4, 6, '根据处理时长按分钟计费，适用于长时间处理任务', 'system', 'system');

-- ==================================================
-- 2. 计费套餐数据 (a_billing_package)
-- ==================================================
INSERT INTO a_billing_package (owner_member_id, code, name, description, type, price, currency_id, credits_granted, validity_days, renewal_interval_unit, member_level_grant, status, sort_order, create_by, update_by) VALUES
-- 系统预设套餐
(NULL, 'free-trial', '免费体验包', '新用户免费体验套餐，包含基础功能使用额度', 1, 0.00, 1, 100.0000, 30, 1, 1, 2, 1, 'system', 'system'),
(NULL, 'basic-monthly', '基础月卡', '基础版月度套餐，适合轻度使用用户', 2, 29.90, 1, 1000.0000, 30, 2, 1, 2, 2, 'system', 'system'),
(NULL, 'pro-monthly', 'Pro月卡', '专业版月度套餐，适合中度使用用户', 2, 99.90, 1, 5000.0000, 30, 2, 2, 2, 3, 'system', 'system'),
(NULL, 'enterprise-monthly', '企业月卡', '企业版月度套餐，适合重度使用用户', 2, 299.90, 1, 20000.0000, 30, 2, 3, 2, 4, 'system', 'system'),
(NULL, 'basic-yearly', '基础年卡', '基础版年度套餐，享受2个月优惠', 2, 299.00, 1, 12000.0000, 365, 4, 1, 2, 5, 'system', 'system'),
(NULL, 'pro-yearly', 'Pro年卡', '专业版年度套餐，享受2个月优惠', 2, 999.00, 1, 60000.0000, 365, 4, 2, 2, 6, 'system', 'system'),
(NULL, 'enterprise-yearly', '企业年卡', '企业版年度套餐，享受2个月优惠', 2, 2999.00, 1, 240000.0000, 365, 4, 3, 2, 7, 'system', 'system'),

-- 一次性资源包
(NULL, 'token-pack-small', '小型Token包', '10万Token一次性资源包', 1, 19.90, 1, 100000.0000, NULL, 1, 1, 2, 8, 'system', 'system'),
(NULL, 'token-pack-medium', '中型Token包', '50万Token一次性资源包', 1, 89.90, 1, 500000.0000, NULL, 1, 1, 2, 9, 'system', 'system'),
(NULL, 'token-pack-large', '大型Token包', '100万Token一次性资源包', 1, 159.90, 1, 1000000.0000, NULL, 1, 1, 2, 10, 'system', 'system'),

-- 特殊套餐
(NULL, 'student-monthly', '学生月卡', '学生专享优惠套餐', 2, 19.90, 1, 800.0000, 30, 2, 1, 2, 11, 'system', 'system'),
(NULL, 'creator-monthly', '创作者月卡', '内容创作者专享套餐，包含图像和视频生成额度', 2, 199.90, 1, 8000.0000, 30, 2, 2, 2, 12, 'system', 'system');

-- ==================================================
-- 3. 模型价格数据 (a_billing_price)
-- ==================================================
-- 注意：这里假设模型ID 1-28已存在，对应之前创建的模型数据

-- GPT-4 Turbo价格 (model_id: 1)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(1, 1, 1, 0.0300, 1, 'system', 'system'), -- 免费版：0.03元/1K Token
(1, 1, 2, 0.0250, 1, 'system', 'system'), -- VIP版：0.025元/1K Token
(1, 1, 3, 0.0200, 1, 'system', 'system'); -- 企业版：0.02元/1K Token

-- GPT-4价格 (model_id: 2)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(2, 1, 1, 0.0250, 1, 'system', 'system'),
(2, 1, 2, 0.0200, 1, 'system', 'system'),
(2, 1, 3, 0.0150, 1, 'system', 'system');

-- GPT-3.5 Turbo价格 (model_id: 3)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(3, 1, 1, 0.0020, 1, 'system', 'system'),
(3, 1, 2, 0.0015, 1, 'system', 'system'),
(3, 1, 3, 0.0010, 1, 'system', 'system');

-- Claude 3.5 Sonnet价格 (model_id: 4)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(4, 1, 1, 0.0280, 1, 'system', 'system'),
(4, 1, 2, 0.0230, 1, 'system', 'system'),
(4, 1, 3, 0.0180, 1, 'system', 'system');

-- Gemini 1.5 Pro价格 (model_id: 7)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(7, 1, 1, 0.0260, 1, 'system', 'system'),
(7, 1, 2, 0.0210, 1, 'system', 'system'),
(7, 1, 3, 0.0160, 1, 'system', 'system');

-- DALL-E 3价格 (model_id: 15) - 按图片计费
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(15, 3, 1, 2.0000, 1, 'system', 'system'), -- 免费版：2元/张
(15, 3, 2, 1.5000, 1, 'system', 'system'), -- VIP版：1.5元/张
(15, 3, 3, 1.0000, 1, 'system', 'system'); -- 企业版：1元/张

-- Stable Diffusion XL价格 (model_id: 17)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(17, 3, 1, 0.5000, 1, 'system', 'system'),
(17, 3, 2, 0.4000, 1, 'system', 'system'),
(17, 3, 3, 0.3000, 1, 'system', 'system');

-- Runway Gen-3价格 (model_id: 21) - 按秒计费
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(21, 4, 1, 0.5000, 1, 'system', 'system'), -- 免费版：0.5元/秒
(21, 4, 2, 0.4000, 1, 'system', 'system'), -- VIP版：0.4元/秒
(21, 4, 3, 0.3000, 1, 'system', 'system'); -- 企业版：0.3元/秒

-- GPT-4 Vision价格 (model_id: 25)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(25, 1, 1, 0.0350, 1, 'system', 'system'),
(25, 1, 2, 0.0300, 1, 'system', 'system'),
(25, 1, 3, 0.0250, 1, 'system', 'system');

-- 国产模型价格（更优惠）
-- 文心一言 4.0价格 (model_id: 9)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(9, 1, 1, 0.0080, 1, 'system', 'system'),
(9, 1, 2, 0.0060, 1, 'system', 'system'),
(9, 1, 3, 0.0040, 1, 'system', 'system');

-- 通义千问 Max价格 (model_id: 10)
INSERT INTO a_billing_price (model_id, plan_id, member_level, price, currency_id, create_by, update_by) VALUES
(10, 1, 1, 0.0070, 1, 'system', 'system'),
(10, 1, 2, 0.0050, 1, 'system', 'system'),
(10, 1, 3, 0.0030, 1, 'system', 'system');

-- ==================================================
-- 4. 会员余额数据 (a_billing_balance)
-- ==================================================
-- 注意：这里假设已有会员ID 1-10，实际使用时需要根据真实会员数据调整

INSERT INTO a_billing_balance (member_id, balance, frozen_amount, currency_id, low_threshold) VALUES
(1, 156.7500, 0.0000, 1, 10.0000),  -- 会员1：余额156.75元
(2, 89.2300, 5.0000, 1, 20.0000),   -- 会员2：余额89.23元，冻结5元
(3, 234.5600, 0.0000, 1, 15.0000),  -- 会员3：余额234.56元
(4, 45.8900, 0.0000, 1, 10.0000),   -- 会员4：余额45.89元
(5, 678.9000, 12.5000, 1, 50.0000), -- 会员5：余额678.90元，冻结12.5元
(6, 23.4500, 0.0000, 1, 10.0000),   -- 会员6：余额23.45元
(7, 345.6700, 0.0000, 1, 30.0000),  -- 会员7：余额345.67元
(8, 12.3400, 0.0000, 1, 10.0000),   -- 会员8：余额12.34元
(9, 567.8900, 8.0000, 1, 40.0000),  -- 会员9：余额567.89元，冻结8元
(10, 123.4500, 0.0000, 1, 20.0000); -- 会员10：余额123.45元

-- ==================================================
-- 5. 会员使用记录数据 (a_billing_usage)
-- ==================================================

-- 会员1的使用记录
INSERT INTO a_billing_usage (member_id, model_id, plan_id, unit, amount, duration_ms, result_size, used_time) VALUES
-- 最近的使用记录
(1, 1, 1, 1, 1250.0000, 2340, 1024, NOW() - INTERVAL '2 hours'),      -- GPT-4 Turbo，1250 tokens
(1, 25, 1, 1, 890.0000, 1890, 2048, NOW() - INTERVAL '3 hours'),      -- GPT-4 Vision，890 tokens
(1, 3, 1, 1, 2340.0000, 1560, 512, NOW() - INTERVAL '1 day'),         -- GPT-3.5 Turbo，2340 tokens
(1, 15, 3, 3, 1.0000, 8900, 1048576, NOW() - INTERVAL '2 days'),      -- DALL-E 3，1张图片
(1, 1, 1, 1, 3450.0000, 3200, 1536, NOW() - INTERVAL '3 days'),       -- GPT-4 Turbo，3450 tokens

-- 会员2的使用记录
(2, 17, 3, 3, 3.0000, 12000, 3145728, NOW() - INTERVAL '30 minutes'), -- Stable Diffusion XL，3张图片
(2, 1, 1, 1, 2100.0000, 2800, 1024, NOW() - INTERVAL '1 hour'),       -- GPT-4 Turbo，2100 tokens
(2, 21, 4, 4, 15.0000, 45000, 10485760, NOW() - INTERVAL '2 days'),   -- Runway Gen-3，15秒视频
(2, 4, 1, 1, 1800.0000, 2100, 768, NOW() - INTERVAL '4 days'),        -- Claude 3.5 Sonnet，1800 tokens

-- 会员3的使用记录
(3, 3, 1, 1, 5600.0000, 4200, 2048, NOW() - INTERVAL '15 minutes'),   -- GPT-3.5 Turbo，5600 tokens
(3, 9, 1, 1, 3200.0000, 2800, 1024, NOW() - INTERVAL '4 hours'),      -- 文心一言 4.0，3200 tokens
(3, 10, 1, 1, 2800.0000, 2400, 896, NOW() - INTERVAL '6 hours'),      -- 通义千问 Max，2800 tokens
(3, 1, 1, 1, 1900.0000, 2600, 1152, NOW() - INTERVAL '1 day'),        -- GPT-4 Turbo，1900 tokens

-- 会员4的使用记录
(4, 3, 1, 1, 8900.0000, 5600, 3072, NOW() - INTERVAL '10 minutes'),   -- GPT-3.5 Turbo，8900 tokens
(4, 25, 1, 1, 1200.0000, 2100, 1536, NOW() - INTERVAL '45 minutes'),  -- GPT-4 Vision，1200 tokens
(4, 17, 3, 3, 2.0000, 8000, 2097152, NOW() - INTERVAL '2 hours'),     -- Stable Diffusion XL，2张图片

-- 会员5的使用记录
(5, 1, 1, 1, 4500.0000, 3800, 2048, NOW() - INTERVAL '2 hours'),      -- GPT-4 Turbo，4500 tokens
(5, 7, 1, 1, 3200.0000, 2900, 1280, NOW() - INTERVAL '1 day'),        -- Gemini 1.5 Pro，3200 tokens
(5, 15, 3, 3, 1.0000, 9200, 1048576, NOW() - INTERVAL '3 days');      -- DALL-E 3，1张图片

-- ==================================================
-- 6. 消费/充值流水数据 (a_billing_transaction)
-- ==================================================

-- 会员1的交易流水
INSERT INTO a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time) VALUES
-- 充值记录
(1, 2, 200.0000, 1, 1001, '购买Pro月卡充值', NOW() - INTERVAL '7 days'),
(1, 2, 100.0000, 1, 1002, '购买Token包充值', NOW() - INTERVAL '15 days'),
-- 消费记录
(1, 1, -37.5000, 1, 2001, 'GPT-4 Turbo使用消费', NOW() - INTERVAL '2 hours'),
(1, 1, -31.1500, 1, 2002, 'GPT-4 Vision使用消费', NOW() - INTERVAL '3 hours'),
(1, 1, -4.6800, 1, 2003, 'GPT-3.5 Turbo使用消费', NOW() - INTERVAL '1 day'),
(1, 1, -2.0000, 1, 2004, 'DALL-E 3图像生成消费', NOW() - INTERVAL '2 days'),

-- 会员2的交易流水
INSERT INTO a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time) VALUES
-- 充值记录
(2, 2, 299.9000, 1, 1003, '购买企业月卡充值', NOW() - INTERVAL '5 days'),
-- 消费记录
(2, 1, -1.2000, 1, 2005, 'Stable Diffusion XL图像生成消费', NOW() - INTERVAL '30 minutes'),
(2, 1, -52.5000, 1, 2006, 'GPT-4 Turbo使用消费', NOW() - INTERVAL '1 hour'),
(2, 1, -4.5000, 1, 2007, 'Runway Gen-3视频生成消费', NOW() - INTERVAL '2 days'),
(2, 1, -50.4000, 1, 2008, 'Claude 3.5 Sonnet使用消费', NOW() - INTERVAL '4 days'),

-- 会员3的交易流水
INSERT INTO a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time) VALUES
-- 充值记录
(3, 2, 299.0000, 1, 1004, '购买基础年卡充值', NOW() - INTERVAL '10 days'),
-- 消费记录
(3, 1, -11.2000, 1, 2009, 'GPT-3.5 Turbo使用消费', NOW() - INTERVAL '15 minutes'),
(3, 1, -25.6000, 1, 2010, '文心一言 4.0使用消费', NOW() - INTERVAL '4 hours'),
(3, 1, -19.6000, 1, 2011, '通义千问 Max使用消费', NOW() - INTERVAL '6 hours'),
(3, 1, -47.5000, 1, 2012, 'GPT-4 Turbo使用消费', NOW() - INTERVAL '1 day'),

-- 会员4的交易流水
INSERT INTO a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time) VALUES
-- 充值记录
(4, 2, 89.9000, 1, 1005, '购买中型Token包充值', NOW() - INTERVAL '3 days'),
-- 消费记录
(4, 1, -17.8000, 1, 2013, 'GPT-3.5 Turbo使用消费', NOW() - INTERVAL '10 minutes'),
(4, 1, -42.0000, 1, 2014, 'GPT-4 Vision使用消费', NOW() - INTERVAL '45 minutes'),
(4, 1, -0.8000, 1, 2015, 'Stable Diffusion XL图像生成消费', NOW() - INTERVAL '2 hours'),

-- 会员5的交易流水
INSERT INTO a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time) VALUES
-- 充值记录
(5, 2, 999.0000, 1, 1006, '购买Pro年卡充值', NOW() - INTERVAL '20 days'),
-- 消费记录
(5, 1, -135.0000, 1, 2016, 'GPT-4 Turbo使用消费', NOW() - INTERVAL '2 hours'),
(5, 1, -83.2000, 1, 2017, 'Gemini 1.5 Pro使用消费', NOW() - INTERVAL '1 day'),
(5, 1, -1.5000, 1, 2018, 'DALL-E 3图像生成消费', NOW() - INTERVAL '3 days'),

-- 系统赠送记录
INSERT INTO a_billing_transaction (member_id, type, amount, currency_id, reference_id, description, transaction_time) VALUES
(1, 4, 50.0000, 1, 3001, '新用户注册赠送', NOW() - INTERVAL '30 days'),
(2, 4, 50.0000, 1, 3002, '新用户注册赠送', NOW() - INTERVAL '25 days'),
(3, 4, 50.0000, 1, 3003, '新用户注册赠送', NOW() - INTERVAL '35 days'),
(4, 4, 50.0000, 1, 3004, '新用户注册赠送', NOW() - INTERVAL '20 days'),
(5, 4, 50.0000, 1, 3005, '新用户注册赠送', NOW() - INTERVAL '40 days');

-- ==================================================
-- 7. 订单数据 (a_billing_order)
-- ==================================================
-- 注意：这里假设支付网关ID 1-3已存在，实际使用时需要根据真实支付网关数据调整

-- 已完成的订单
INSERT INTO a_billing_order (order_no, member_id, package_id, package_info_snapshot, amount, currency_id, status, payment_gateway_id, gateway_transaction_id, paid_time) VALUES
('ORD202508040001', 1, 3, '{"name": "Pro月卡", "price": 99.90, "credits_granted": 5000.0000, "validity_days": 30}', 99.90, 1, 2, 1, 'WX_20250804_001', NOW() - INTERVAL '7 days'),
('ORD202508040002', 1, 9, '{"name": "中型Token包", "price": 89.90, "credits_granted": 500000.0000}', 89.90, 1, 2, 2, 'ALI_20250804_001', NOW() - INTERVAL '15 days'),
('ORD202508040003', 2, 4, '{"name": "企业月卡", "price": 299.90, "credits_granted": 20000.0000, "validity_days": 30}', 299.90, 1, 2, 1, 'WX_20250804_002', NOW() - INTERVAL '5 days'),
('ORD202508040004', 3, 5, '{"name": "基础年卡", "price": 299.00, "credits_granted": 12000.0000, "validity_days": 365}', 299.00, 1, 2, 3, 'BANK_20250804_001', NOW() - INTERVAL '10 days'),
('ORD202508040005', 4, 9, '{"name": "中型Token包", "price": 89.90, "credits_granted": 500000.0000}', 89.90, 1, 2, 2, 'ALI_20250804_002', NOW() - INTERVAL '3 days'),
('ORD202508040006', 5, 6, '{"name": "Pro年卡", "price": 999.00, "credits_granted": 60000.0000, "validity_days": 365}', 999.00, 1, 2, 1, 'WX_20250804_003', NOW() - INTERVAL '20 days'),

-- 待支付的订单
('ORD202508040007', 6, 2, '{"name": "基础月卡", "price": 29.90, "credits_granted": 1000.0000, "validity_days": 30}', 29.90, 1, 1, 1, NULL, NULL),
('ORD202508040008', 7, 8, '{"name": "小型Token包", "price": 19.90, "credits_granted": 100000.0000}', 19.90, 1, 1, 2, NULL, NULL),

-- 已取消的订单
('ORD202508040009', 8, 3, '{"name": "Pro月卡", "price": 99.90, "credits_granted": 5000.0000, "validity_days": 30}', 99.90, 1, 3, 1, NULL, NULL),

-- 支付失败的订单
('ORD202508040010', 9, 4, '{"name": "企业月卡", "price": 299.90, "credits_granted": 20000.0000, "validity_days": 30}', 299.90, 1, 4, 2, 'ALI_20250804_FAIL', NULL),

-- 最近的订单
('ORD202508040011', 10, 11, '{"name": "学生月卡", "price": 19.90, "credits_granted": 800.0000, "validity_days": 30}', 19.90, 1, 2, 1, 'WX_20250804_004', NOW() - INTERVAL '1 day'),
('ORD202508040012', 1, 12, '{"name": "创作者月卡", "price": 199.90, "credits_granted": 8000.0000, "validity_days": 30}', 199.90, 1, 1, 1, NULL, NULL),
('ORD202508040013', 2, 10, '{"name": "大型Token包", "price": 159.90, "credits_granted": 1000000.0000}', 159.90, 1, 2, 3, 'BANK_20250804_002', NOW() - INTERVAL '2 hours'),

-- 历史订单（更早期的）
('ORD202507280001', 1, 1, '{"name": "免费体验包", "price": 0.00, "credits_granted": 100.0000, "validity_days": 30}', 0.00, 1, 2, 1, 'FREE_TRIAL_001', NOW() - INTERVAL '30 days'),
('ORD202507280002', 2, 1, '{"name": "免费体验包", "price": 0.00, "credits_granted": 100.0000, "validity_days": 30}', 0.00, 1, 2, 1, 'FREE_TRIAL_002', NOW() - INTERVAL '25 days'),
('ORD202507280003', 3, 1, '{"name": "免费体验包", "price": 0.00, "credits_granted": 100.0000, "validity_days": 30}', 0.00, 1, 2, 1, 'FREE_TRIAL_003', NOW() - INTERVAL '35 days'),
('ORD202507280004', 4, 1, '{"name": "免费体验包", "price": 0.00, "credits_granted": 100.0000, "validity_days": 30}', 0.00, 1, 2, 1, 'FREE_TRIAL_004', NOW() - INTERVAL '20 days'),
('ORD202507280005', 5, 1, '{"name": "免费体验包", "price": 0.00, "credits_granted": 100.0000, "validity_days": 30}', 0.00, 1, 2, 1, 'FREE_TRIAL_005', NOW() - INTERVAL '40 days');

-- ==================================================
-- 数据插入完成
-- ==================================================
