package ai.showlab.bff.entity.vo.v1;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

/**
 * 使用记录视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = "id")
public class BillingUsageVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模型ID
     */
    private Long modelId;
    
    /**
     * 模型名称
     */
    private String modelName;
    
    /**
     * 模型编码
     */
    private String modelCode;

    /**
     * 计费方案ID
     */
    private Long planId;
    
    /**
     * 计费方案名称
     */
    private String planName;

    /**
     * 计费单位
     */
    private Integer unit;
    
    /**
     * 计费单位描述
     */
    private String unitDesc;

    /**
     * 本次使用量
     */
    private BigDecimal amount;

    /**
     * 请求耗时（毫秒）
     */
    private Integer durationMs;
    
    /**
     * 耗时描述（如：1.2秒）
     */
    private String durationDesc;

    /**
     * 返回结果大小（字节）
     */
    private Integer resultSize;
    
    /**
     * 结果大小描述（如：1.2KB）
     */
    private String resultSizeDesc;

    /**
     * 使用时间
     */
    private OffsetDateTime usedTime;
    
    /**
     * 本次使用的费用（从交易记录中计算得出）
     */
    private BigDecimal cost;
    
    /**
     * 货币符号
     */
    private String currencySymbol;
}
