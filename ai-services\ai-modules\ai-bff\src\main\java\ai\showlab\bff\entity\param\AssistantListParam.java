package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * 助手列表查询参数
 * 
 * <AUTHOR>
 */
@Data
public class AssistantListParam {
    
    /**
     * 分类ID，可选
     */
    private Long categoryId;
    
    /**
     * 交互模式，可选 (字典: assistant_interaction_mode), 1-标准对话, 2-图像分析, 3-文生视频, 4-持续监听
     */
    private Integer interactionMode;
    
    /**
     * 助手状态，可选 (字典: assistant_status), 1-草稿, 2-待审核, 3-已发布, 4-已归档
     */
    private Integer status;
    
    /**
     * 是否公开，可选
     */
    private Boolean isPublic;
    
    /**
     * 是否为预设助手，可选
     */
    private Boolean isPreset;
    
    /**
     * 关键词搜索，可选（搜索助手名称和描述）
     */
    private String keyword;
    
    /**
     * 排序字段，可选 (usage_count-使用次数, create_time-创建时间, update_time-更新时间)
     */
    private String sortBy = "usage_count";
    
    /**
     * 排序方向，可选 (asc-升序, desc-降序)
     */
    private String sortOrder = "desc";
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;
}
