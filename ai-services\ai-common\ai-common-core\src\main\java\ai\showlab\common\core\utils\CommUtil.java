package ai.showlab.common.core.utils;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 通用工具类
 *
 * <AUTHOR>
 */
public class CommUtil {

    /**
     * 将 Date 转换为 OffsetDateTime
     */
    public static OffsetDateTime toOffsetDateTime(Date date) {
        date = date != null ? date : new Date();
        return date.toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    /**
     * 将 OffsetDateTime 转换为 Date
     */
    public static Date toDate(OffsetDateTime offsetDateTime) {
        offsetDateTime = offsetDateTime != null ? offsetDateTime : OffsetDateTime.now();
        return Date.from(offsetDateTime.toInstant());
    }

    /**
     * 在 OffsetDateTime 上加指定天数
     */
    public static OffsetDateTime addOffsetDateTime(OffsetDateTime offsetDateTime, long days) {
        offsetDateTime = offsetDateTime != null ? offsetDateTime : OffsetDateTime.now();
        return offsetDateTime.plusDays(days);
    }


}
