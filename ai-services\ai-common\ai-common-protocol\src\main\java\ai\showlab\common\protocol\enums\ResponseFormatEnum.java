package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应格式枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResponseFormatEnum {
    
    /** 纯文本 */
    PLAIN_TEXT(1, "纯文本"),
    
    /** JSON */
    JSON(2, "JSON"),
    
    /** 图片 */
    IMAGE(3, "图片"),
    
    /** 音频 */
    AUDIO(4, "音频");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ResponseFormatEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ResponseFormatEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 