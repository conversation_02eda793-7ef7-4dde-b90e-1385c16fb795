import request from '@/utils/request'

// 查询权限依赖关系列表
export function listFuncPermissionDependency(query) {
  return request({
    url: '/system/func/permission/dependency/list',
    method: 'get',
    params: query
  })
}

// 查询权限依赖关系详细
export function getFuncPermissionDependency(id) {
  return request({
    url: '/system/func/permission/dependency/' + id,
    method: 'get'
  })
}

// 新增权限依赖关系
export function addFuncPermissionDependency(data) {
  return request({
    url: '/system/func/permission/dependency',
    method: 'post',
    data: data
  })
}

// 修改权限依赖关系
export function updateFuncPermissionDependency(data) {
  return request({
    url: '/system/func/permission/dependency',
    method: 'put',
    data: data
  })
}

// 删除权限依赖关系
export function delFuncPermissionDependency(id) {
  return request({
    url: '/system/func/permission/dependency/' + id,
    method: 'delete'
  })
}
