package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠券类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CouponTypeEnum {
    
    /** 固定金额 */
    FIXED_AMOUNT(1, "固定金额"),
    
    /** 百分比折扣 */
    PERCENTAGE_DISCOUNT(2, "百分比折扣"),
    
    /** 满减 */
    THRESHOLD_DISCOUNT(3, "满减");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static CouponTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CouponTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 