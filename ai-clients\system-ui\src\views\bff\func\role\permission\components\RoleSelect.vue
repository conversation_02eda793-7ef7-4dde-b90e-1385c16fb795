<template>
  <el-dialog append-to-body :title="title" v-model="dialogVisible" width="500px" @close="handleClose">
    <el-input
      clearable
      placeholder="请输入关键字进行过滤"
      style="margin-bottom: 10px"
      v-model="roleFilterText"
    />
    <el-tree
      :data="roleTreeData"
      :filter-node-method="filterNode"
      :props="{ label: 'name', children: 'children' }"
      @node-click="handleNodeClick"
      highlight-current
      node-key="id"
      ref="roleTreeRef"
      :default-expanded-keys="expandedKeys"
      :current-node-key="currentRoleId"
    />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="confirmSelectRole" type="primary">确 定</el-button>
        <el-button @click="cancelSelectRole">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue'
import { listFuncRole, getFuncRole } from "@/api/bff/func/role"
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择角色'
  },
  selectedId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'select', 'cancel'])

// 角色相关
const dialogVisible = ref(props.visible)
const roleTreeData = ref([])
const roleFilterText = ref('')
const roleTreeRef = ref(null)
const currentRoleId = ref(props.selectedId)
const roleMap = ref({})
const expandedKeys = ref([])

// 监听props变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    getRoleTreeData()
  }
})

watch(() => props.selectedId, (val) => {
  currentRoleId.value = val
  // 当选中ID变化时，确保树节点展开到该节点
  if (val) {
    findParentKeys(val)
  }
})

// 监听过滤文本变化
watch(roleFilterText, (val) => {
  roleTreeRef.value?.filter(val)
})

// 过滤节点方法
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.indexOf(value) !== -1
}

// 查找节点的所有父节点ID，用于展开树
const findParentKeys = (id) => {
  // 在树加载完成后执行
  nextTick(() => {
    if (roleTreeRef.value) {
      // 设置当前选中节点
      roleTreeRef.value.setCurrentKey(id)
      
      // 获取节点的路径并展开
      const node = roleTreeRef.value.getNode(id)
      if (node && node.parent && node.parent.key !== undefined) {
        expandedKeys.value = [node.parent.key]
      }
    }
  })
}

// 获取角色树形数据
const getRoleTreeData = () => {
  listFuncRole().then(response => {
    roleTreeData.value = proxy.handleTree(response.rows, "id", "pid")
    
    // 构建roleMap用于显示名称
    buildRoleMap(response.rows)
    
    // 如果有选中ID，确保树渲染完成后设置当前节点
    if (currentRoleId.value) {
      findParentKeys(currentRoleId.value)
    }
  })
}

// 构建角色映射
const buildRoleMap = (roles) => {
  roles.forEach(role => {
    roleMap.value[role.id] = role.name
  })
}

// 树节点点击事件
const handleNodeClick = (data) => {
  currentRoleId.value = data.id
}

// 确认选择角色
const confirmSelectRole = () => {
  if (currentRoleId.value !== undefined) {
    const selectedName = roleMap.value[currentRoleId.value] || currentRoleId.value
    emit('select', {
      id: currentRoleId.value,
      name: selectedName
    })
  }
  handleClose()
}

// 取消选择角色
const cancelSelectRole = () => {
  emit('cancel')
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 初始化
if (props.visible) {
  getRoleTreeData()
}
</script> 