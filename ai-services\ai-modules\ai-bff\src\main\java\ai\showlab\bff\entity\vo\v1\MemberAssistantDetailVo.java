package ai.showlab.bff.entity.vo.v1;

import lombok.Data;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会员助手实例详情视图对象
 * 用于向前端展示会员助手实例详细信息
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantDetailVo {
    
    /**
     * 会员助手实例ID
     */
    private Long id;
    
    /**
     * 会员ID
     */
    private Long memberId;
    
    /**
     * 助手模板ID
     */
    private Long assistantId;
    
    /**
     * 助手编码
     */
    private String assistantCode;
    
    /**
     * 助手名称
     */
    private String assistantName;
    
    /**
     * 助手描述
     */
    private String assistantDescription;
    
    /**
     * 助手图标URL
     */
    private String assistantIconUrl;
    
    /**
     * 助手分类信息
     */
    private AssistantCategoryVo category;
    
    /**
     * 交互模式 (字典: assistant_interaction_mode)
     */
    private Integer interactionMode;

    /**
     * 交互模式名称（通过数据字典获取）
     */
    private String interactionModeName;
    

    
    /**
     * 模板版本号
     */
    private String templateVersion;
    
    /**
     * 选择的模型信息
     */
    private ModelListVo model;
    
    /**
     * 可选的模型列表
     */
    private List<ModelListVo> availableModels;
    
    /**
     * 自定义名称
     */
    private String customName;
    
    /**
     * 参数配置覆盖 (JSON格式)
     */
    private String settingsOverride;
    
    /**
     * 参数配置覆盖 (解析后的Map)
     */
    private Map<String, Object> settingsMap;
    
    /**
     * 助手参数配置列表（包含当前值）
     */
    private List<AssistantParamVo> params;
    
    /**
     * 是否收藏
     */
    private Boolean isFavorite;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private OffsetDateTime createTime;
    
    /**
     * 更新时间
     */
    private OffsetDateTime updateTime;
    
    /**
     * 最后使用时间
     */
    private OffsetDateTime lastUsedTime;
    
    /**
     * 使用次数
     */
    private Long usageCount;
    
    /**
     * 使用统计信息
     */
    private MemberAssistantUsageStatsVo usageStats;
}
