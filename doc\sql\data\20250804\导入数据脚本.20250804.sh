#!/bin/bash

# ====================================================================================
# AIShowLab - AI助手和计费模块数据导入脚本
# 文件名: 导入数据脚本.20250804.sh
# 作者: AI Assistant
# 描述: 用于导入AI助手和计费与消耗模块模拟数据的便捷脚本
# 创建时间: 2025-08-04
# ====================================================================================

# 设置脚本参数
set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DB_NAME="aishowlab"
DEFAULT_DB_USER="postgres"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：打印帮助信息
print_help() {
    echo "AIShowLab AI助手和计费模块数据导入脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --host HOST        数据库主机地址 (默认: $DEFAULT_DB_HOST)"
    echo "  -p, --port PORT        数据库端口 (默认: $DEFAULT_DB_PORT)"
    echo "  -d, --database DB      数据库名称 (默认: $DEFAULT_DB_NAME)"
    echo "  -u, --user USER        数据库用户名 (默认: $DEFAULT_DB_USER)"
    echo "  -w, --password PASS    数据库密码 (可选，建议使用 .pgpass 文件)"
    echo "  --assistant-only       仅导入AI助手模块数据"
    echo "  --billing-only         仅导入计费与消耗模块数据"
    echo "  --dry-run              仅验证SQL语法，不执行导入"
    echo "  --help                 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -h localhost -d aishowlab -u postgres"
    echo "  $0 --assistant-only    # 仅导入AI助手数据"
    echo "  $0 --billing-only      # 仅导入计费数据"
    echo "  $0 --dry-run           # 仅验证SQL语法"
    echo ""
}

# 解析命令行参数
DB_HOST=$DEFAULT_DB_HOST
DB_PORT=$DEFAULT_DB_PORT
DB_NAME=$DEFAULT_DB_NAME
DB_USER=$DEFAULT_DB_USER
DB_PASSWORD=""
DRY_RUN=false
ASSISTANT_ONLY=false
BILLING_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            DB_HOST="$2"
            shift 2
            ;;
        -p|--port)
            DB_PORT="$2"
            shift 2
            ;;
        -d|--database)
            DB_NAME="$2"
            shift 2
            ;;
        -u|--user)
            DB_USER="$2"
            shift 2
            ;;
        -w|--password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --assistant-only)
            ASSISTANT_ONLY=true
            shift
            ;;
        --billing-only)
            BILLING_ONLY=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            print_help
            exit 0
            ;;
        *)
            print_message $RED "未知参数: $1"
            print_help
            exit 1
            ;;
    esac
done

# 检查互斥参数
if [[ "$ASSISTANT_ONLY" == true && "$BILLING_ONLY" == true ]]; then
    print_message $RED "错误: --assistant-only 和 --billing-only 不能同时使用"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ASSISTANT_SQL="$SCRIPT_DIR/AI助手模拟数据.20250804.sql"
BILLING_SQL="$SCRIPT_DIR/计费与消耗模拟数据.20250804.sql"
VALIDATION_SQL="$SCRIPT_DIR/数据验证脚本.20250804.sql"

# 检查SQL文件是否存在
if [[ "$BILLING_ONLY" != true && ! -f "$ASSISTANT_SQL" ]]; then
    print_message $RED "错误: 找不到AI助手数据文件: $ASSISTANT_SQL"
    exit 1
fi

if [[ "$ASSISTANT_ONLY" != true && ! -f "$BILLING_SQL" ]]; then
    print_message $RED "错误: 找不到计费数据文件: $BILLING_SQL"
    exit 1
fi

# 构建psql连接字符串
PSQL_CONN="postgresql://$DB_USER"
if [[ -n "$DB_PASSWORD" ]]; then
    PSQL_CONN="$PSQL_CONN:$DB_PASSWORD"
fi
PSQL_CONN="$PSQL_CONN@$DB_HOST:$DB_PORT/$DB_NAME"

print_message $BLUE "===================================================="
print_message $BLUE "AIShowLab AI助手和计费模块数据导入脚本"
print_message $BLUE "===================================================="
print_message $YELLOW "数据库连接信息:"
print_message $YELLOW "  主机: $DB_HOST"
print_message $YELLOW "  端口: $DB_PORT"
print_message $YELLOW "  数据库: $DB_NAME"
print_message $YELLOW "  用户: $DB_USER"

if [[ "$DRY_RUN" == true ]]; then
    print_message $YELLOW "  模式: 仅验证SQL语法（不执行导入）"
elif [[ "$ASSISTANT_ONLY" == true ]]; then
    print_message $YELLOW "  模式: 仅导入AI助手模块数据"
    print_message $YELLOW "  SQL文件: $ASSISTANT_SQL"
elif [[ "$BILLING_ONLY" == true ]]; then
    print_message $YELLOW "  模式: 仅导入计费与消耗模块数据"
    print_message $YELLOW "  SQL文件: $BILLING_SQL"
else
    print_message $YELLOW "  模式: 导入所有模块数据"
    print_message $YELLOW "  AI助手文件: $ASSISTANT_SQL"
    print_message $YELLOW "  计费文件: $BILLING_SQL"
fi

print_message $BLUE "===================================================="

# 验证数据库连接
print_message $YELLOW "正在验证数据库连接..."
if ! psql "$PSQL_CONN" -c "SELECT version();" > /dev/null 2>&1; then
    print_message $RED "错误: 无法连接到数据库"
    print_message $RED "请检查连接参数或确保数据库服务正在运行"
    exit 1
fi
print_message $GREEN "✓ 数据库连接成功"

# 检查必要的表是否存在
print_message $YELLOW "正在检查数据库表结构..."

# AI助手相关表
if [[ "$BILLING_ONLY" != true ]]; then
    ASSISTANT_TABLES=("a_assistant_category" "a_assistant" "a_assistant_param" "a_member_assistant" "a_assistant_favorite")
    for table in "${ASSISTANT_TABLES[@]}"; do
        if ! psql "$PSQL_CONN" -c "SELECT 1 FROM $table LIMIT 1;" > /dev/null 2>&1; then
            print_message $RED "错误: AI助手表 $table 不存在"
            print_message $RED "请先执行数据库架构脚本创建表结构"
            exit 1
        fi
    done
    print_message $GREEN "✓ AI助手相关表都存在"
fi

# 计费相关表
if [[ "$ASSISTANT_ONLY" != true ]]; then
    BILLING_TABLES=("a_billing_plan" "a_billing_package" "a_billing_price" "a_billing_balance" "a_billing_usage" "a_billing_transaction" "a_billing_order")
    for table in "${BILLING_TABLES[@]}"; do
        if ! psql "$PSQL_CONN" -c "SELECT 1 FROM $table LIMIT 1;" > /dev/null 2>&1; then
            print_message $RED "错误: 计费表 $table 不存在"
            print_message $RED "请先执行数据库架构脚本创建表结构"
            exit 1
        fi
    done
    print_message $GREEN "✓ 计费相关表都存在"
fi

if [[ "$DRY_RUN" == true ]]; then
    # 仅验证SQL语法
    print_message $YELLOW "正在验证SQL语法..."
    
    if [[ "$BILLING_ONLY" != true ]]; then
        if ! psql "$PSQL_CONN" --single-transaction --set ON_ERROR_STOP=on -f "$ASSISTANT_SQL" --echo-errors > /dev/null 2>&1; then
            print_message $RED "✗ AI助手数据SQL语法验证失败"
            exit 1
        fi
        print_message $GREEN "✓ AI助手数据SQL语法验证通过"
    fi
    
    if [[ "$ASSISTANT_ONLY" != true ]]; then
        if ! psql "$PSQL_CONN" --single-transaction --set ON_ERROR_STOP=on -f "$BILLING_SQL" --echo-errors > /dev/null 2>&1; then
            print_message $RED "✗ 计费数据SQL语法验证失败"
            exit 1
        fi
        print_message $GREEN "✓ 计费数据SQL语法验证通过"
    fi
else
    # 执行数据导入
    if [[ "$BILLING_ONLY" != true ]]; then
        print_message $YELLOW "正在导入AI助手模块数据..."
        if psql "$PSQL_CONN" -f "$ASSISTANT_SQL" --single-transaction --set ON_ERROR_STOP=on; then
            print_message $GREEN "✓ AI助手数据导入成功"
        else
            print_message $RED "✗ AI助手数据导入失败"
            exit 1
        fi
    fi
    
    if [[ "$ASSISTANT_ONLY" != true ]]; then
        print_message $YELLOW "正在导入计费与消耗模块数据..."
        if psql "$PSQL_CONN" -f "$BILLING_SQL" --single-transaction --set ON_ERROR_STOP=on; then
            print_message $GREEN "✓ 计费数据导入成功"
        else
            print_message $RED "✗ 计费数据导入失败"
            exit 1
        fi
    fi
    
    # 执行验证脚本（如果存在）
    if [[ -f "$VALIDATION_SQL" ]]; then
        print_message $YELLOW "正在执行数据验证..."
        psql "$PSQL_CONN" -f "$VALIDATION_SQL" > /tmp/validation_result_$(date +%Y%m%d_%H%M%S).txt 2>&1
        print_message $GREEN "✓ 数据验证完成，结果已保存到 /tmp/validation_result_*.txt"
    fi
fi

print_message $BLUE "===================================================="
print_message $GREEN "操作完成！"

if [[ "$DRY_RUN" == false ]]; then
    print_message $YELLOW ""
    if [[ "$BILLING_ONLY" != true ]]; then
        print_message $YELLOW "AI助手模块导入的数据包括:"
        print_message $YELLOW "  - 6个助手分类（含二级分类）"
        print_message $YELLOW "  - 10个AI助手定义"
        print_message $YELLOW "  - 助手参数配置"
        print_message $YELLOW "  - 会员助手实例"
        print_message $YELLOW "  - 助手收藏记录"
    fi
    
    if [[ "$ASSISTANT_ONLY" != true ]]; then
        print_message $YELLOW "计费模块导入的数据包括:"
        print_message $YELLOW "  - 6个计费方案"
        print_message $YELLOW "  - 12个计费套餐"
        print_message $YELLOW "  - 模型价格配置"
        print_message $YELLOW "  - 会员余额记录"
        print_message $YELLOW "  - 使用记录和交易流水"
        print_message $YELLOW "  - 订单数据"
    fi
    
    print_message $YELLOW ""
    print_message $YELLOW "建议执行验证脚本检查数据完整性:"
    print_message $YELLOW "  psql -d $DB_NAME -f $VALIDATION_SQL"
fi

print_message $BLUE "===================================================="
