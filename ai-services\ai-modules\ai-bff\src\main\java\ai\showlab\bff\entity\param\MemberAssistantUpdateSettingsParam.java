package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 更新会员助手实例参数配置参数
 * 
 * <AUTHOR>
 */
@Data
public class MemberAssistantUpdateSettingsParam {
    
    /**
     * 会员助手实例ID
     */
    @NotNull(message = "会员助手实例ID不能为空")
    private Long memberAssistantId;
    
    /**
     * 新的参数配置
     * 以 key-value 形式存储，如 {"wake_word": "你好AI", "response_style": "formal"}
     */
    @NotNull(message = "参数配置不能为空")
    private Map<String, Object> settingsOverride;
}
