-- ====================================================================================
-- AIShowLab - AI助手模块模拟数据 (PostgreSQL)
-- 文件名: AI助手模拟数据.20250804.sql
-- 作者: AI Assistant
-- 描述: 为AI助手相关表生成模拟数据，包括助手分类、助手定义、参数配置、会员实例等
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. AI助手分类数据 (a_assistant_category)
-- ==================================================
INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) VALUES
(NULL, 1, '生活娱乐', '日常生活和娱乐相关的AI助手', 1, 'system', 'system'),
(NULL, 2, '办公效率', '提升工作效率的AI助手', 2, 'system', 'system'),
(NULL, 3, '创意趣味', '创意设计和趣味互动的AI助手', 3, 'system', 'system'),
(NULL, 4, '学习教育', '学习辅导和教育相关的AI助手', 4, 'system', 'system'),
(NULL, 5, '健康医疗', '健康管理和医疗咨询的AI助手', 5, 'system', 'system'),
(NULL, 99, '其他', '其他类型的AI助手', 99, 'system', 'system');

-- 添加二级分类
INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) 
SELECT c.id, 11, '拍照识物', '通过拍照识别物品的助手', 1, 'system', 'system'
FROM a_assistant_category c WHERE c.type = 1 AND c.delete_time IS NULL;

INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) 
SELECT c.id, 12, '美食推荐', '美食推荐和菜谱分享的助手', 2, 'system', 'system'
FROM a_assistant_category c WHERE c.type = 1 AND c.delete_time IS NULL;

INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) 
SELECT c.id, 21, '文档处理', '文档编写和处理的助手', 1, 'system', 'system'
FROM a_assistant_category c WHERE c.type = 2 AND c.delete_time IS NULL;

INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) 
SELECT c.id, 22, '数据分析', '数据分析和报表生成的助手', 2, 'system', 'system'
FROM a_assistant_category c WHERE c.type = 2 AND c.delete_time IS NULL;

INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) 
SELECT c.id, 31, '图像创作', '图像生成和创意设计的助手', 1, 'system', 'system'
FROM a_assistant_category c WHERE c.type = 3 AND c.delete_time IS NULL;

INSERT INTO a_assistant_category (pid, type, name, description, sort_order, create_by, update_by) 
SELECT c.id, 32, '视频制作', '视频生成和编辑的助手', 2, 'system', 'system'
FROM a_assistant_category c WHERE c.type = 3 AND c.delete_time IS NULL;

-- ==================================================
-- 2. AI助手定义数据 (a_assistant)
-- ==================================================
INSERT INTO a_assistant (category_id, owner_member_id, revenue_share_rate, code, name, billing_package_ids, knowledge_ids, description, icon_url, prompt_template, interaction_mode, model_suggestions, version, status, usage_count, is_public, is_preset, create_by, update_by) VALUES
-- 拍照识物助手
(1, NULL, 0.0000, 'photo-identifier', '拍照识物', '1,2,3', '1', '通过拍照快速识别物品、植物、动物等，提供详细信息和相关知识', '/icons/photo-identifier.png', '你是一个专业的物品识别专家。请仔细观察用户上传的图片，识别其中的主要物体，并提供以下信息：\n1. 物品名称\n2. 基本特征描述\n3. 用途或功能\n4. 相关知识或趣味事实\n请用简洁明了的语言回答。', 2, '[{"model_id": 25, "priority": 1}, {"model_id": 26, "priority": 2}]', '1.0.0', 3, 15420, true, true, 'system', 'system'),

-- 美食推荐助手
(1, NULL, 0.0000, 'food-recommender', '美食推荐师', '1,2', '2', '根据用户喜好推荐美食，提供菜谱和制作方法', '/icons/food-recommender.png', '你是一位资深的美食专家和营养师。请根据用户的需求为他们推荐合适的美食，包括：\n1. 推荐理由\n2. 营养价值\n3. 制作难度\n4. 详细制作步骤\n请考虑用户的饮食偏好、健康状况和时间安排。', 1, '[{"model_id": 1, "priority": 1}, {"model_id": 4, "priority": 2}]', '1.0.0', 3, 8930, true, true, 'system', 'system'),

-- 文档写作助手
(2, NULL, 0.0000, 'doc-writer', '文档写作助手', '2,3', '3', '协助用户撰写各类文档，包括报告、方案、邮件等', '/icons/doc-writer.png', '你是一位专业的文档写作专家。请根据用户的需求协助撰写高质量的文档，包括：\n1. 明确文档结构\n2. 优化语言表达\n3. 确保逻辑清晰\n4. 提供改进建议\n请根据文档类型调整写作风格和格式。', 1, '[{"model_id": 1, "priority": 1}, {"model_id": 2, "priority": 2}]', '1.0.0', 3, 12650, true, true, 'system', 'system'),

-- 数据分析助手
(2, NULL, 0.0000, 'data-analyst', '数据分析师', '2,3', '4', '帮助用户分析数据，生成图表和报告', '/icons/data-analyst.png', '你是一位专业的数据分析师。请帮助用户分析数据并提供洞察，包括：\n1. 数据清洗和预处理建议\n2. 统计分析方法\n3. 可视化建议\n4. 结论和建议\n请用通俗易懂的语言解释复杂的数据概念。', 1, '[{"model_id": 1, "priority": 1}, {"model_id": 7, "priority": 2}]', '1.0.0', 3, 6780, true, true, 'system', 'system'),

-- 图像创作助手
(3, NULL, 0.0000, 'image-creator', '图像创作大师', '2,3', '5', '根据描述生成精美的图像作品', '/icons/image-creator.png', '你是一位专业的图像创作专家。请根据用户的描述生成高质量的图像，注意：\n1. 理解用户的创意需求\n2. 选择合适的艺术风格\n3. 注意构图和色彩搭配\n4. 确保图像质量和美感\n请提供详细的创作说明和技巧建议。', 2, '[{"model_id": 15, "priority": 1}, {"model_id": 17, "priority": 2}]', '1.0.0', 3, 9340, true, true, 'system', 'system'),

-- 视频制作助手
(3, NULL, 0.0000, 'video-creator', '视频制作专家', '3', '6', '协助用户制作各类视频内容', '/icons/video-creator.png', '你是一位专业的视频制作专家。请协助用户制作高质量的视频内容，包括：\n1. 脚本策划\n2. 镜头设计\n3. 剪辑建议\n4. 特效应用\n请根据视频类型和目标受众提供专业建议。', 3, '[{"model_id": 21, "priority": 1}, {"model_id": 22, "priority": 2}]', '1.0.0', 3, 4560, true, true, 'system', 'system'),

-- 学习辅导助手
(4, NULL, 0.0000, 'study-tutor', '学习辅导老师', '1,2', '7', '为学生提供个性化的学习辅导和答疑', '/icons/study-tutor.png', '你是一位经验丰富的教育专家。请为学生提供个性化的学习辅导，包括：\n1. 知识点讲解\n2. 学习方法指导\n3. 练习题解答\n4. 学习计划制定\n请根据学生的年龄和学习水平调整教学方式。', 1, '[{"model_id": 1, "priority": 1}, {"model_id": 4, "priority": 2}]', '1.0.0', 3, 11230, true, true, 'system', 'system'),

-- 健康顾问助手
(5, NULL, 0.0000, 'health-advisor', '健康生活顾问', '1,2', '8', '提供健康生活建议和基础医疗咨询', '/icons/health-advisor.png', '你是一位专业的健康顾问。请为用户提供科学的健康建议，包括：\n1. 生活方式建议\n2. 饮食营养指导\n3. 运动健身计划\n4. 基础健康知识\n注意：仅提供一般性建议，严重疾病请咨询专业医生。', 1, '[{"model_id": 1, "priority": 1}, {"model_id": 4, "priority": 2}]', '1.0.0', 3, 7890, true, true, 'system', 'system'),

-- 智能客服助手
(99, NULL, 0.0000, 'smart-service', '智能客服', '1', '9', '提供7x24小时智能客服服务', '/icons/smart-service.png', '你是一位专业的客服代表。请为用户提供优质的服务，包括：\n1. 问题解答\n2. 业务咨询\n3. 投诉处理\n4. 服务指导\n请保持耐心、专业和友好的态度。', 1, '[{"model_id": 3, "priority": 1}, {"model_id": 1, "priority": 2}]', '1.0.0', 3, 23450, true, true, 'system', 'system'),

-- 代码助手
(2, NULL, 0.0000, 'code-assistant', '编程助手', '2,3', '10', '协助开发者编写和优化代码', '/icons/code-assistant.png', '你是一位资深的软件工程师。请协助用户解决编程问题，包括：\n1. 代码编写和优化\n2. Bug调试和修复\n3. 架构设计建议\n4. 最佳实践指导\n请提供清晰的代码示例和详细的解释。', 1, '[{"model_id": 1, "priority": 1}, {"model_id": 2, "priority": 2}]', '1.0.0', 3, 18760, true, true, 'system', 'system');

-- ==================================================
-- 3. 助手参数定义数据 (a_assistant_param)
-- ==================================================

-- 拍照识物助手参数
INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'recognition_mode', '识别模式', 4, '"general"', '[{"value": "general", "label": "通用识别"}, {"value": "plant", "label": "植物识别"}, {"value": "animal", "label": "动物识别"}, {"value": "food", "label": "食物识别"}]', '选择识别的专业领域', 1, 'system', 'system'
FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'detail_level', '详细程度', 4, '"medium"', '[{"value": "simple", "label": "简单"}, {"value": "medium", "label": "中等"}, {"value": "detailed", "label": "详细"}]', '返回信息的详细程度', 2, 'system', 'system'
FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

-- 美食推荐助手参数
INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'cuisine_type', '菜系偏好', 4, '"chinese"', '[{"value": "chinese", "label": "中式"}, {"value": "western", "label": "西式"}, {"value": "japanese", "label": "日式"}, {"value": "korean", "label": "韩式"}, {"value": "mixed", "label": "不限"}]', '用户偏好的菜系类型', 1, 'system', 'system'
FROM a_assistant a WHERE a.code = 'food-recommender' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'difficulty_level', '制作难度', 4, '"medium"', '[{"value": "easy", "label": "简单"}, {"value": "medium", "label": "中等"}, {"value": "hard", "label": "困难"}]', '推荐菜品的制作难度', 2, 'system', 'system'
FROM a_assistant a WHERE a.code = 'food-recommender' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'dietary_restrictions', '饮食限制', 5, '""', NULL, '特殊饮食要求，如素食、无糖等', 3, 'system', 'system'
FROM a_assistant a WHERE a.code = 'food-recommender' AND a.delete_time IS NULL;

-- 文档写作助手参数
INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'document_type', '文档类型', 4, '"report"', '[{"value": "report", "label": "报告"}, {"value": "proposal", "label": "方案"}, {"value": "email", "label": "邮件"}, {"value": "article", "label": "文章"}, {"value": "summary", "label": "总结"}]', '要撰写的文档类型', 1, 'system', 'system'
FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'writing_style', '写作风格', 4, '"formal"', '[{"value": "formal", "label": "正式"}, {"value": "casual", "label": "随意"}, {"value": "academic", "label": "学术"}, {"value": "business", "label": "商务"}]', '文档的写作风格', 2, 'system', 'system'
FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'word_count', '字数要求', 2, '1000', NULL, '目标字数（大约）', 3, 'system', 'system'
FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

-- 图像创作助手参数
INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'art_style', '艺术风格', 4, '"realistic"', '[{"value": "realistic", "label": "写实"}, {"value": "cartoon", "label": "卡通"}, {"value": "anime", "label": "动漫"}, {"value": "abstract", "label": "抽象"}, {"value": "oil_painting", "label": "油画"}]', '图像的艺术风格', 1, 'system', 'system'
FROM a_assistant a WHERE a.code = 'image-creator' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'image_size', '图像尺寸', 4, '"1024x1024"', '[{"value": "512x512", "label": "512x512"}, {"value": "1024x1024", "label": "1024x1024"}, {"value": "1024x1792", "label": "1024x1792"}, {"value": "1792x1024", "label": "1792x1024"}]', '生成图像的尺寸', 2, 'system', 'system'
FROM a_assistant a WHERE a.code = 'image-creator' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'quality', '图像质量', 4, '"standard"', '[{"value": "standard", "label": "标准"}, {"value": "hd", "label": "高清"}]', '生成图像的质量', 3, 'system', 'system'
FROM a_assistant a WHERE a.code = 'image-creator' AND a.delete_time IS NULL;

-- 编程助手参数
INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'programming_language', '编程语言', 4, '"java"', '[{"value": "java", "label": "Java"}, {"value": "python", "label": "Python"}, {"value": "javascript", "label": "JavaScript"}, {"value": "csharp", "label": "C#"}, {"value": "cpp", "label": "C++"}, {"value": "go", "label": "Go"}]', '主要使用的编程语言', 1, 'system', 'system'
FROM a_assistant a WHERE a.code = 'code-assistant' AND a.delete_time IS NULL;

INSERT INTO a_assistant_param (assistant_id, key, label, param_type, default_value, options, description, sort_order, create_by, update_by)
SELECT a.id, 'code_style', '代码风格', 4, '"clean"', '[{"value": "clean", "label": "简洁"}, {"value": "detailed", "label": "详细注释"}, {"value": "enterprise", "label": "企业级"}]', '代码的编写风格', 2, 'system', 'system'
FROM a_assistant a WHERE a.code = 'code-assistant' AND a.delete_time IS NULL;

-- ==================================================
-- 4. 会员助手实例数据 (a_member_assistant)
-- ==================================================
-- 注意：这里假设已有会员ID 1-10，实际使用时需要根据真实会员数据调整

-- 会员1的助手实例
INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 1, a.id, '1.0.0', 25, '我的拍照识物', '{"recognition_mode": "plant", "detail_level": "detailed"}', true, true, 45, NOW() - INTERVAL '2 hours'
FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 1, a.id, '1.0.0', 1, '美食小助手', '{"cuisine_type": "chinese", "difficulty_level": "easy"}', false, true, 23, NOW() - INTERVAL '1 day'
FROM a_assistant a WHERE a.code = 'food-recommender' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 1, a.id, '1.0.0', 1, '工作文档助手', '{"document_type": "report", "writing_style": "business", "word_count": 1500}', true, true, 67, NOW() - INTERVAL '3 hours'
FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

-- 会员2的助手实例
INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 2, a.id, '1.0.0', 15, '创意画师', '{"art_style": "anime", "image_size": "1024x1792", "quality": "hd"}', true, true, 89, NOW() - INTERVAL '30 minutes'
FROM a_assistant a WHERE a.code = 'image-creator' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 2, a.id, '1.0.0', 1, '编程导师', '{"programming_language": "python", "code_style": "detailed"}', false, true, 156, NOW() - INTERVAL '1 hour'
FROM a_assistant a WHERE a.code = 'code-assistant' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 2, a.id, '1.0.0', 21, '视频制作专家', '{}', false, true, 12, NOW() - INTERVAL '2 days'
FROM a_assistant a WHERE a.code = 'video-creator' AND a.delete_time IS NULL;

-- 会员3的助手实例
INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 3, a.id, '1.0.0', 1, '学习伙伴', '{}', true, true, 234, NOW() - INTERVAL '15 minutes'
FROM a_assistant a WHERE a.code = 'study-tutor' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 3, a.id, '1.0.0', 1, '健康管家', '{}', true, true, 78, NOW() - INTERVAL '4 hours'
FROM a_assistant a WHERE a.code = 'health-advisor' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 3, a.id, '1.0.0', 1, '数据分析师', '{}', false, true, 45, NOW() - INTERVAL '6 hours'
FROM a_assistant a WHERE a.code = 'data-analyst' AND a.delete_time IS NULL;

-- 会员4的助手实例
INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 4, a.id, '1.0.0', 3, '智能客服', '{}', false, true, 567, NOW() - INTERVAL '10 minutes'
FROM a_assistant a WHERE a.code = 'smart-service' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 4, a.id, '1.0.0', 25, '万能识别器', '{"recognition_mode": "general", "detail_level": "medium"}', true, true, 123, NOW() - INTERVAL '45 minutes'
FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

-- 会员5的助手实例
INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 5, a.id, '1.0.0', 1, '全能助手', '{"document_type": "article", "writing_style": "casual"}', true, true, 89, NOW() - INTERVAL '2 hours'
FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

INSERT INTO a_member_assistant (member_id, assistant_id, template_version, model_id, custom_name, settings_override, is_favorite, is_active, usage_count, last_used_time)
SELECT 5, a.id, '1.0.0', 1, '美食达人', '{"cuisine_type": "mixed", "difficulty_level": "medium", "dietary_restrictions": "素食"}', false, true, 67, NOW() - INTERVAL '1 day'
FROM a_assistant a WHERE a.code = 'food-recommender' AND a.delete_time IS NULL;

-- ==================================================
-- 5. 助手收藏数据 (a_assistant_favorite)
-- ==================================================

-- 会员1的收藏
INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 1, a.id FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 1, a.id FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 1, a.id FROM a_assistant a WHERE a.code = 'code-assistant' AND a.delete_time IS NULL;

-- 会员2的收藏
INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 2, a.id FROM a_assistant a WHERE a.code = 'image-creator' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 2, a.id FROM a_assistant a WHERE a.code = 'video-creator' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 2, a.id FROM a_assistant a WHERE a.code = 'code-assistant' AND a.delete_time IS NULL;

-- 会员3的收藏
INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 3, a.id FROM a_assistant a WHERE a.code = 'study-tutor' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 3, a.id FROM a_assistant a WHERE a.code = 'health-advisor' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 3, a.id FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

-- 会员4的收藏
INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 4, a.id FROM a_assistant a WHERE a.code = 'smart-service' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 4, a.id FROM a_assistant a WHERE a.code = 'photo-identifier' AND a.delete_time IS NULL;

-- 会员5的收藏
INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 5, a.id FROM a_assistant a WHERE a.code = 'doc-writer' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 5, a.id FROM a_assistant a WHERE a.code = 'food-recommender' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 5, a.id FROM a_assistant a WHERE a.code = 'study-tutor' AND a.delete_time IS NULL;

-- 会员6-10的收藏（随机分布）
INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 6, a.id FROM a_assistant a WHERE a.code = 'image-creator' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 7, a.id FROM a_assistant a WHERE a.code = 'data-analyst' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 8, a.id FROM a_assistant a WHERE a.code = 'health-advisor' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 9, a.id FROM a_assistant a WHERE a.code = 'smart-service' AND a.delete_time IS NULL;

INSERT INTO a_assistant_favorite (member_id, assistant_id)
SELECT 10, a.id FROM a_assistant a WHERE a.code = 'code-assistant' AND a.delete_time IS NULL;

-- ==================================================
-- 数据插入完成
-- ==================================================
