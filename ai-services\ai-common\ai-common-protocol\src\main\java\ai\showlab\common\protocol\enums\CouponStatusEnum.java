package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠券状态枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CouponStatusEnum {
    
    /** 可用 */
    AVAILABLE(1, "可用"),
    
    /** 已用 */
    USED(2, "已用"),
    
    /** 过期 */
    EXPIRED(3, "过期");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static CouponStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CouponStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 