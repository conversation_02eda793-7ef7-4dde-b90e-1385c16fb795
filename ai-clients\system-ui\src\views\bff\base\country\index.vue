<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="国家ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入国家ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="国家地区" prop="nameEn">
        <el-input
          v-model="queryParams.nameEn"
          placeholder="请输入国家地区"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="本地名称" prop="nameNative">
        <el-input
          v-model="queryParams.nameNative"
          placeholder="请输入本地名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="两字代码" prop="iso2Code">
        <el-input
          v-model="queryParams.iso2Code"
          placeholder="请输入两字代码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="三字代码" prop="iso3Code">
        <el-input
          v-model="queryParams.iso3Code"
          placeholder="请输入三字代码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数字代码" prop="numericCode">
        <el-input
          v-model="queryParams.numericCode"
          placeholder="请输入数字代码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话区号" prop="phoneCode">
        <el-input
          v-model="queryParams.phoneCode"
          placeholder="请输入电话区号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="首都" prop="capital">
        <el-input
          v-model="queryParams.capital"
          placeholder="请输入首都"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在大洲" prop="continent">
        <el-select v-model="queryParams.continent" placeholder="请选择所在大洲" clearable>
          <el-option
            v-for="dict in continent"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="软删除时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeDeleteTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" style="width: 308px">
        <el-date-picker
          v-model="daterangeUpdateTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:baseCountry:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:baseCountry:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:baseCountry:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:baseCountry:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="baseCountryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="国家ID" align="center" prop="id" />
      <el-table-column label="国家地区" align="center" prop="nameEn" />
      <el-table-column label="本地名称" align="center" prop="nameNative" />
      <el-table-column label="两字代码" align="center" prop="iso2Code" />
      <el-table-column label="三字代码" align="center" prop="iso3Code" />
      <el-table-column label="数字代码" align="center" prop="numericCode" />
      <el-table-column label="电话区号" align="center" prop="phoneCode" />
      <el-table-column label="首都" align="center" prop="capital" />
      <el-table-column label="所在大洲" align="center" prop="continent">
        <template #default="scope">
          <dict-tag :options="continent" :value="scope.row.continent"/>
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" prop="isEnabled" />
      <el-table-column label="软删除时间" align="center" prop="deleteTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.deleteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:baseCountry:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:baseCountry:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改国家地区对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="baseCountryRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="国家地区" prop="nameEn">
          <el-input v-model="form.nameEn" placeholder="请输入国家地区" />
        </el-form-item>
        <el-form-item label="本地名称" prop="nameNative">
          <el-input v-model="form.nameNative" placeholder="请输入本地名称" />
        </el-form-item>
        <el-form-item label="两字代码" prop="iso2Code">
          <el-input v-model="form.iso2Code" placeholder="请输入两字代码" />
        </el-form-item>
        <el-form-item label="三字代码" prop="iso3Code">
          <el-input v-model="form.iso3Code" placeholder="请输入三字代码" />
        </el-form-item>
        <el-form-item label="数字代码" prop="numericCode">
          <el-input v-model="form.numericCode" placeholder="请输入数字代码" />
        </el-form-item>
        <el-form-item label="电话区号" prop="phoneCode">
          <el-input v-model="form.phoneCode" placeholder="请输入电话区号" />
        </el-form-item>
        <el-form-item label="首都" prop="capital">
          <el-input v-model="form.capital" placeholder="请输入首都" />
        </el-form-item>
        <el-form-item label="所在大洲" prop="continent">
          <el-select v-model="form.continent" placeholder="请选择所在大洲">
            <el-option
              v-for="dict in continent"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="国旗" prop="flagUrl">
          <image-upload v-model="form.flagUrl"/>
        </el-form-item>
        <el-form-item label="排序值" prop="sortOrder">
          <el-input v-model="form.sortOrder" placeholder="请输入排序值" />
        </el-form-item>
        <el-form-item label="软删除时间" prop="deleteTime">
          <el-date-picker clearable
            v-model="form.deleteTime"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择软删除时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BaseCountry">
import { listBaseCountry, getBaseCountry, delBaseCountry, addBaseCountry, updateBaseCountry } from "@/api/bff/base/country"

const { proxy } = getCurrentInstance()
const { continent } = proxy.useDict('continent')

const baseCountryList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const daterangeDeleteTime = ref([])
const daterangeCreateTime = ref([])
const daterangeUpdateTime = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    id: null,
    nameEn: null,
    nameNative: null,
    iso2Code: null,
    iso3Code: null,
    numericCode: null,
    phoneCode: null,
    capital: null,
    continent: null,
    isEnabled: null,
    deleteTime: null,
    updateTime: null
  },
  rules: {
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询国家地区列表 */
function getList() {
  loading.value = true
  queryParams.value.params = {}
  if (null != daterangeDeleteTime && '' != daterangeDeleteTime) {
    queryParams.value.params["beginDeleteTime"] = daterangeDeleteTime.value[0]
    queryParams.value.params["endDeleteTime"] = daterangeDeleteTime.value[1]
  }
  if (null != daterangeCreateTime && '' != daterangeCreateTime) {
    queryParams.value.params["beginCreateTime"] = daterangeCreateTime.value[0]
    queryParams.value.params["endCreateTime"] = daterangeCreateTime.value[1]
  }
  if (null != daterangeUpdateTime && '' != daterangeUpdateTime) {
    queryParams.value.params["beginUpdateTime"] = daterangeUpdateTime.value[0]
    queryParams.value.params["endUpdateTime"] = daterangeUpdateTime.value[1]
  }
  listBaseCountry(queryParams.value).then(response => {
    baseCountryList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    nameEn: null,
    nameNative: null,
    iso2Code: null,
    iso3Code: null,
    numericCode: null,
    phoneCode: null,
    capital: null,
    continent: null,
    flagUrl: null,
    isEnabled: null,
    sortOrder: null,
    deleteTime: null,
    createBy: null,
    updateBy: null,
    createTime: null,
    updateTime: null
  }
  proxy.resetForm("baseCountryRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeDeleteTime.value = []
  daterangeCreateTime.value = []
  daterangeUpdateTime.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加国家地区"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getBaseCountry(_id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改国家地区"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["baseCountryRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateBaseCountry(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addBaseCountry(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除国家地区编号为"' + _ids + '"的数据项？').then(function() {
    return delBaseCountry(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/base/country/export', {
    ...queryParams.value
  }, `baseCountry_${new Date().getTime()}.xlsx`)
}

getList()
</script>
