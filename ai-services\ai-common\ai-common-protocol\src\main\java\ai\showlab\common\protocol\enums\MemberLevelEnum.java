package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员计费等级枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MemberLevelEnum {
    
    /** 免费版 */
    FREE(1, "免费版"),
    
    /** VIP版 */
    VIP(2, "VIP版"),
    
    /** 企业版 */
    ENTERPRISE(3, "企业版");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static MemberLevelEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MemberLevelEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 