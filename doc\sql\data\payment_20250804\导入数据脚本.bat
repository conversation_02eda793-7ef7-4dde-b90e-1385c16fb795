@echo off
REM ====================================================================================
REM AIShowLab - 支付网关模块数据导入脚本 (Windows版本)
REM 文件名: 导入数据脚本.bat
REM 作者: AI Assistant
REM 描述: 用于导入支付网关模块模拟数据的Windows批处理脚本
REM 创建时间: 2025-08-04
REM ====================================================================================

setlocal enabledelayedexpansion

REM 默认配置
set DEFAULT_DB_HOST=localhost
set DEFAULT_DB_PORT=5432
set DEFAULT_DB_NAME=aishowlab
set DEFAULT_DB_USER=postgres

REM 设置默认值
set DB_HOST=%DEFAULT_DB_HOST%
set DB_PORT=%DEFAULT_DB_PORT%
set DB_NAME=%DEFAULT_DB_NAME%
set DB_USER=%DEFAULT_DB_USER%
set DB_PASSWORD=
set DRY_RUN=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-h" (
    set DB_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--host" (
    set DB_HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-p" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--port" (
    set DB_PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set DB_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--database" (
    set DB_NAME=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-u" (
    set DB_USER=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--user" (
    set DB_USER=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-w" (
    set DB_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--password" (
    set DB_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--dry-run" (
    set DRY_RUN=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
echo 未知参数: %~1
goto :show_help

:args_done

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set PAYMENT_SQL=%SCRIPT_DIR%支付网关模拟数据.sql
set VALIDATION_SQL=%SCRIPT_DIR%数据验证脚本.sql

REM 检查SQL文件是否存在
if not exist "%PAYMENT_SQL%" (
    echo [错误] 找不到支付网关数据文件: %PAYMENT_SQL%
    exit /b 1
)

REM 构建psql连接字符串
set PSQL_CONN=postgresql://%DB_USER%
if not "%DB_PASSWORD%"=="" (
    set PSQL_CONN=!PSQL_CONN!:%DB_PASSWORD%
)
set PSQL_CONN=!PSQL_CONN!@%DB_HOST%:%DB_PORT%/%DB_NAME%

echo ====================================================
echo AIShowLab 支付网关模块数据导入脚本
echo ====================================================
echo 数据库连接信息:
echo   主机: %DB_HOST%
echo   端口: %DB_PORT%
echo   数据库: %DB_NAME%
echo   用户: %DB_USER%

if "%DRY_RUN%"=="true" (
    echo   模式: 仅验证SQL语法（不执行导入）
) else (
    echo   模式: 导入支付网关模块数据
    echo   SQL文件: %PAYMENT_SQL%
)

echo ====================================================

REM 验证数据库连接
echo 正在验证数据库连接...
psql "%PSQL_CONN%" -c "SELECT version();" >nul 2>&1
if errorlevel 1 (
    echo [错误] 无法连接到数据库
    echo 请检查连接参数或确保数据库服务正在运行
    exit /b 1
)
echo [成功] 数据库连接成功

REM 检查必要的表是否存在
echo 正在检查数据库表结构...

set PAYMENT_TABLES=a_payment_gateway a_payment_gateway_country a_base_country
for %%t in (!PAYMENT_TABLES!) do (
    psql "%PSQL_CONN%" -c "SELECT 1 FROM %%t LIMIT 1;" >nul 2>&1
    if errorlevel 1 (
        echo [错误] 支付网关表 %%t 不存在
        echo 请先执行数据库架构脚本创建表结构
        exit /b 1
    )
)
echo [成功] 支付网关相关表都存在

REM 检查基础数据是否存在
echo 正在检查基础数据...
for /f %%i in ('psql "%PSQL_CONN%" -t -c "SELECT COUNT(*) FROM a_base_country WHERE is_enabled = true;"') do set COUNTRY_COUNT=%%i
set COUNTRY_COUNT=%COUNTRY_COUNT: =%
if %COUNTRY_COUNT% LSS 10 (
    echo [错误] 国家基础数据不足（当前: %COUNTRY_COUNT% 个），请先导入国家基础数据
    exit /b 1
)
echo [成功] 基础数据检查通过（%COUNTRY_COUNT% 个国家）

if "%DRY_RUN%"=="true" (
    REM 仅验证SQL语法
    echo 正在验证SQL语法...
    
    psql "%PSQL_CONN%" --single-transaction --set ON_ERROR_STOP=on -f "%PAYMENT_SQL%" --echo-errors >nul 2>&1
    if errorlevel 1 (
        echo [失败] 支付网关数据SQL语法验证失败
        exit /b 1
    )
    echo [成功] 支付网关数据SQL语法验证通过
) else (
    REM 执行数据导入
    echo 正在导入支付网关模块数据...
    psql "%PSQL_CONN%" -f "%PAYMENT_SQL%" --single-transaction --set ON_ERROR_STOP=on
    if errorlevel 1 (
        echo [失败] 支付网关数据导入失败
        exit /b 1
    ) else (
        echo [成功] 支付网关数据导入成功
    )
    
    REM 执行验证脚本（如果存在）
    if exist "%VALIDATION_SQL%" (
        echo 正在执行数据验证...
        for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
        set "timestamp=%dt:~0,8%_%dt:~8,6%"
        psql "%PSQL_CONN%" -f "%VALIDATION_SQL%" > "%TEMP%\payment_validation_result_!timestamp!.txt" 2>&1
        echo [成功] 数据验证完成，结果已保存到 %TEMP%\payment_validation_result_!timestamp!.txt
    )
)

echo ====================================================
echo 操作完成！

if not "%DRY_RUN%"=="true" (
    echo.
    echo 支付网关模块导入的数据包括:
    echo   - 15个支付网关配置（微信支付、支付宝、PayPal、Stripe等）
    echo   - 覆盖全球主要国家和地区的支付网关映射
    echo   - 国内支付、国际支付、地区特色支付等多种类型
    echo   - 完整的支付网关配置参数和状态管理
    echo.
    echo 建议执行验证脚本检查数据完整性:
    echo   psql -d %DB_NAME% -f "%VALIDATION_SQL%"
)

echo ====================================================
goto :eof

:show_help
echo AIShowLab 支付网关模块数据导入脚本
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -h, --host HOST        数据库主机地址 (默认: %DEFAULT_DB_HOST%)
echo   -p, --port PORT        数据库端口 (默认: %DEFAULT_DB_PORT%)
echo   -d, --database DB      数据库名称 (默认: %DEFAULT_DB_NAME%)
echo   -u, --user USER        数据库用户名 (默认: %DEFAULT_DB_USER%)
echo   -w, --password PASS    数据库密码 (可选，建议使用 .pgpass 文件)
echo   --dry-run              仅验证SQL语法，不执行导入
echo   --help                 显示此帮助信息
echo.
echo 示例:
echo   %~nx0 -h localhost -d aishowlab -u postgres
echo   %~nx0 --dry-run           # 仅验证SQL语法
echo.
exit /b 0
