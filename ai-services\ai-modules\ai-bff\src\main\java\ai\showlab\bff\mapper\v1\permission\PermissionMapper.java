package ai.showlab.bff.mapper.v1.permission;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 权限相关数据访问接口
 * <p>
 * 提供权限校验相关的数据库操作，包括角色权限和会员权限的查询。
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface PermissionMapper {

    /**
     * 统计会员通过角色拥有的指定权限数量
     * <p>
     * 查询路径：a_member_role -> a_func_role -> a_func_permission
     * </p>
     *
     * @param memberId 会员ID
     * @param permissionCode 权限编码
     * @return 权限数量，大于0表示有权限
     */
    Integer countMemberRolePermission(@Param("memberId") Long memberId, @Param("permissionCode") String permissionCode);

    /**
     * 获取会员的直接权限状态
     * <p>
     * 查询a_member_permission表，获取会员对指定权限的直接授权状态。
     * </p>
     *
     * @param memberId 会员ID
     * @param permissionCode 权限编码
     * @return true表示授权，false表示拒绝，null表示未设置
     */
    Boolean getMemberDirectPermission(@Param("memberId") Long memberId, @Param("permissionCode") String permissionCode);

    /**
     * 获取会员通过角色拥有的所有权限编码列表
     * <p>
     * 查询路径：a_member_role -> a_func_role -> a_func_permission
     * </p>
     *
     * @param memberId 会员ID
     * @return 权限编码列表
     */
    List<String> getMemberRolePermissions(@Param("memberId") Long memberId);

    /**
     * 获取会员的直接权限编码列表
     * <p>
     * 查询a_member_permission表，获取会员直接授权的权限编码列表。
     * 只返回is_granted=true的权限。
     * </p>
     *
     * @param memberId 会员ID
     * @return 权限编码列表
     */
    List<String> getMemberDirectPermissions(@Param("memberId") Long memberId);

    /**
     * 获取会员的所有角色编码列表
     * <p>
     * 查询a_member_role表，获取会员拥有的所有角色编码。
     * </p>
     *
     * @param memberId 会员ID
     * @return 角色编码列表
     */
    List<String> getMemberRoles(@Param("memberId") Long memberId);

    /**
     * 统计指定权限编码的数量
     * <p>
     * 用于验证权限编码是否在系统中定义。
     * </p>
     *
     * @param permissionCode 权限编码
     * @return 权限数量，大于0表示存在
     */
    Integer countPermissionByCode(@Param("permissionCode") String permissionCode);

    /**
     * 检查会员是否拥有指定角色
     * <p>
     * 查询a_member_role表，检查会员是否拥有指定角色。
     * </p>
     *
     * @param memberId 会员ID
     * @param roleCode 角色编码
     * @return 角色数量，大于0表示拥有该角色
     */
    Integer countMemberRole(@Param("memberId") Long memberId, @Param("roleCode") String roleCode);

    /**
     * 检查角色是否拥有指定权限
     * <p>
     * 查询a_func_role表，检查角色是否拥有指定权限。
     * </p>
     *
     * @param roleCode 角色编码
     * @param permissionCode 权限编码
     * @return 权限数量，大于0表示拥有该权限
     */
    Integer countRolePermission(@Param("roleCode") String roleCode, @Param("permissionCode") String permissionCode);
}
