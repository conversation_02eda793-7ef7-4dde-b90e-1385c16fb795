import request from '@/utils/request'

// 查询API密钥列表
export function listModelApiKey(query) {
  return request({
    url: '/system/model/apikey/list',
    method: 'get',
    params: query
  })
}

// 查询API密钥详细
export function getModelApiKey(id) {
  return request({
    url: '/system/model/apikey/' + id,
    method: 'get'
  })
}

// 新增API密钥
export function addModelApiKey(data) {
  return request({
    url: '/system/model/apikey',
    method: 'post',
    data: data
  })
}

// 修改API密钥
export function updateModelApiKey(data) {
  return request({
    url: '/system/model/apikey',
    method: 'put',
    data: data
  })
}

// 删除API密钥
export function delModelApiKey(id) {
  return request({
    url: '/system/model/apikey/' + id,
    method: 'delete'
  })
}
