package ai.showlab.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 认证授权中心
 *
 * <AUTHOR>
 */
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(scanBasePackages = {"com.ruoyi.auth", "ai.showlab.auth"})
@EnableFeignClients(basePackages = {"com.ruoyi.auth", "ai.showlab.auth"})
public class AiAuthApp {
    public static void main(String[] args) {
        SpringApplication.run(AiAuthApp.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ ai-auth （认证授权中心）启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
