<template>
  <el-select :model-value="modelValue" placeholder="请选择语言" @change="handleChange" clearable>
    <el-option
      v-for="lang in langList"
      :key="lang.id"
      :label="lang.name"
      :value="lang.id"
    />
  </el-select>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null
  },
  langList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

function handleChange(value) {
  emit('update:modelValue', value)
}
</script> 