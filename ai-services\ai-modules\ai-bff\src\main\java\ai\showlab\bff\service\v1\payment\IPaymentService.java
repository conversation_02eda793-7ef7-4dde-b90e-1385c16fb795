package ai.showlab.bff.service.v1.payment;

import ai.showlab.bff.common.param.RequestParams;
import ai.showlab.bff.entity.param.PaymentCallbackParam;
import ai.showlab.bff.entity.param.PaymentInitiateParam;
import ai.showlab.bff.entity.param.PaymentStatusQueryParam;
import ai.showlab.bff.entity.vo.v1.PaymentInitiateVo;
import ai.showlab.bff.entity.vo.v1.PaymentStatusVo;

/**
 * 支付服务接口
 * <p>
 * 提供支付发起、回调处理、状态查询等核心支付功能。
 * 与现有的billing服务集成，处理订单支付流程。
 * </p>
 *
 * <AUTHOR>
 */
public interface IPaymentService {

    /**
     * 发起支付
     * <p>
     * 基于已创建的订单发起支付，返回支付所需的信息（如支付URL、二维码等）。
     * 支持多种支付方式：重定向、二维码、表单提交、JSAPI等。
     * </p>
     *
     * @param requestParams 支付发起参数，包含订单号和支付网关ID
     * @return 支付发起结果，包含支付URL、二维码等信息
     */
    PaymentInitiateVo initiatePayment(RequestParams<PaymentInitiateParam> requestParams);

    /**
     * 处理支付回调
     * <p>
     * 处理支付网关的异步通知，验证签名，更新订单状态，处理业务逻辑。
     * 此方法需要确保幂等性，避免重复处理。
     * </p>
     *
     * @param callbackParam 支付回调参数，包含支付结果和网关数据
     * @return 处理结果，true表示处理成功，false表示处理失败
     */
    boolean handlePaymentCallback(PaymentCallbackParam callbackParam);

    /**
     * 查询支付状态
     * <p>
     * 查询订单的支付状态，可选择是否强制从支付网关查询最新状态。
     * 默认优先使用缓存数据，提高查询性能。
     * </p>
     *
     * @param requestParams 支付状态查询参数
     * @return 支付状态信息
     */
    PaymentStatusVo queryPaymentStatus(RequestParams<PaymentStatusQueryParam> requestParams);

    /**
     * 主动查询支付网关状态
     * <p>
     * 直接向支付网关查询订单的最新支付状态，用于状态同步。
     * 通常在支付回调丢失或状态不一致时使用。
     * </p>
     *
     * @param orderNo 订单号
     * @param paymentGatewayId 支付网关ID
     * @return 支付状态信息
     */
    PaymentStatusVo queryPaymentStatusFromGateway(String orderNo, Long paymentGatewayId);

    /**
     * 取消支付
     * <p>
     * 取消未支付的订单，释放冻结资源。
     * 只能取消状态为"待支付"的订单。
     * </p>
     *
     * @param orderNo 订单号
     * @param memberId 会员ID（用于权限验证）
     * @return 取消结果，true表示取消成功
     */
    boolean cancelPayment(String orderNo, Long memberId);

    /**
     * 验证支付回调签名
     * <p>
     * 验证支付网关回调数据的签名，确保数据来源的真实性。
     * 不同的支付网关有不同的签名算法。
     * </p>
     *
     * @param callbackParam 回调参数
     * @return 验证结果，true表示签名有效
     */
    boolean verifyCallbackSignature(PaymentCallbackParam callbackParam);

    /**
     * 处理支付超时
     * <p>
     * 处理超时未支付的订单，自动取消订单并释放资源。
     * 通常由定时任务调用。
     * </p>
     *
     * @param orderNo 订单号
     * @return 处理结果，true表示处理成功
     */
    boolean handlePaymentTimeout(String orderNo);
}
