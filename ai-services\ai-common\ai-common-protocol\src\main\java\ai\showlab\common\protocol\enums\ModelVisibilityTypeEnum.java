package ai.showlab.common.protocol.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型可见性类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ModelVisibilityTypeEnum {
    
    /** 按角色 */
    BY_ROLE(1, "按角色"),
    
    /** 按会员 */
    BY_MEMBER(2, "按会员"),
    
    /** 按地区 */
    BY_REGION(3, "按地区");
    
    private final Integer code;
    private final String info;
    
    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static ModelVisibilityTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModelVisibilityTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 