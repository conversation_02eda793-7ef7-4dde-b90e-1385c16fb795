-- ====================================================================================
-- AIShowLab - 支付网关模块模拟数据 (PostgreSQL)
-- 文件名: 支付网关模拟数据.sql
-- 作者: AI Assistant
-- 描述: 为支付网关相关表生成模拟数据，包括支付网关配置、国家地区映射等
-- 创建时间: 2025-08-04
-- ====================================================================================

-- ==================================================
-- 1. 支付网关配置数据 (a_payment_gateway)
-- ==================================================

-- 国内主流支付方式
INSERT INTO a_payment_gateway (name, code, logo_url, config_params, status, sort_order, description, create_by, update_by) VALUES
-- 微信支付
('微信支付', 'wechat_pay', '/images/payment/wechat_pay.png', 
'{"app_id": "wx1234567890abcdef", "mch_id": "1234567890", "api_key": "encrypted_api_key_here", "cert_path": "/certs/wechat/apiclient_cert.p12", "notify_url": "https://api.aishowlab.com/payment/wechat/notify", "sandbox": false, "timeout": 300}', 
1, 1, '微信支付是腾讯公司推出的移动支付产品，支持扫码支付、APP支付、H5支付等多种支付方式', 'system', 'system'),

-- 支付宝
('支付宝', 'alipay', '/images/payment/alipay.png',
'{"app_id": "2021001234567890", "private_key": "encrypted_private_key_here", "public_key": "encrypted_public_key_here", "alipay_public_key": "encrypted_alipay_public_key_here", "notify_url": "https://api.aishowlab.com/payment/alipay/notify", "return_url": "https://www.aishowlab.com/payment/success", "sandbox": false, "charset": "UTF-8", "sign_type": "RSA2"}',
1, 2, '支付宝是蚂蚁集团旗下的第三方支付平台，支持网页支付、手机支付、扫码支付等', 'system', 'system'),

-- 银联支付
('银联支付', 'unionpay', '/images/payment/unionpay.png',
'{"mer_id": "123456789012345", "access_type": "0", "version": "5.1.0", "encoding": "UTF-8", "txn_type": "01", "txn_sub_type": "01", "biz_type": "000201", "channel_type": "07", "front_url": "https://www.aishowlab.com/payment/success", "back_url": "https://api.aishowlab.com/payment/unionpay/notify", "cert_path": "/certs/unionpay/", "sandbox": false}',
1, 3, '中国银联推出的银行卡支付服务，支持借记卡和信用卡在线支付', 'system', 'system'),

-- 国际支付方式
-- PayPal
('PayPal', 'paypal', '/images/payment/paypal.png',
'{"client_id": "encrypted_client_id_here", "client_secret": "encrypted_client_secret_here", "mode": "live", "webhook_id": "encrypted_webhook_id_here", "notify_url": "https://api.aishowlab.com/payment/paypal/notify", "return_url": "https://www.aishowlab.com/payment/success", "cancel_url": "https://www.aishowlab.com/payment/cancel", "currency": "USD"}',
1, 4, 'PayPal是全球领先的在线支付平台，支持信用卡、借记卡和PayPal余额支付', 'system', 'system'),

-- Stripe
('Stripe', 'stripe', '/images/payment/stripe.png',
'{"publishable_key": "pk_live_encrypted_key_here", "secret_key": "sk_live_encrypted_key_here", "webhook_secret": "whsec_encrypted_secret_here", "api_version": "2023-10-16", "notify_url": "https://api.aishowlab.com/payment/stripe/webhook", "success_url": "https://www.aishowlab.com/payment/success", "cancel_url": "https://www.aishowlab.com/payment/cancel", "currency": "usd"}',
1, 5, 'Stripe是面向互联网企业的在线支付处理平台，支持全球多种支付方式', 'system', 'system'),

-- Apple Pay
('Apple Pay', 'apple_pay', '/images/payment/apple_pay.png',
'{"merchant_id": "merchant.com.aishowlab.app", "merchant_domain": "aishowlab.com", "display_name": "AIShowLab", "supported_networks": ["visa", "masterCard", "amex", "discover"], "merchant_capabilities": ["supports3DS"], "country_code": "US", "currency_code": "USD"}',
1, 6, 'Apple Pay是苹果公司推出的移动支付和数字钱包服务', 'system', 'system'),

-- Google Pay
('Google Pay', 'google_pay', '/images/payment/google_pay.png',
'{"merchant_id": "encrypted_merchant_id_here", "merchant_name": "AIShowLab", "gateway": "stripe", "gateway_merchant_id": "encrypted_gateway_merchant_id_here", "allowed_card_networks": ["AMEX", "DISCOVER", "JCB", "MASTERCARD", "VISA"], "allowed_card_auth_methods": ["PAN_ONLY", "CRYPTOGRAM_3DS"], "environment": "PRODUCTION"}',
1, 7, 'Google Pay是谷歌推出的数字钱包平台和在线支付系统', 'system', 'system'),

-- 其他地区支付方式
-- 日本 - LINE Pay
('LINE Pay', 'line_pay', '/images/payment/line_pay.png',
'{"channel_id": "encrypted_channel_id_here", "channel_secret": "encrypted_channel_secret_here", "merchant_device_type": "WEB", "locale": "ja", "currency": "JPY", "confirm_url": "https://api.aishowlab.com/payment/linepay/confirm", "cancel_url": "https://www.aishowlab.com/payment/cancel", "sandbox": false}',
1, 8, 'LINE Pay是LINE公司推出的移动支付服务，在日本、韩国等地区广泛使用', 'system', 'system'),

-- 韩国 - KakaoPay
('KakaoPay', 'kakao_pay', '/images/payment/kakao_pay.png',
'{"cid": "encrypted_cid_here", "admin_key": "encrypted_admin_key_here", "approval_url": "https://www.aishowlab.com/payment/kakao/success", "cancel_url": "https://www.aishowlab.com/payment/cancel", "fail_url": "https://www.aishowlab.com/payment/fail", "sandbox": false}',
1, 9, 'KakaoPay是韩国Kakao公司推出的移动支付服务', 'system', 'system'),

-- 东南亚 - GrabPay
('GrabPay', 'grab_pay', '/images/payment/grab_pay.png',
'{"partner_id": "encrypted_partner_id_here", "partner_secret": "encrypted_partner_secret_here", "merchant_id": "encrypted_merchant_id_here", "terminal_id": "encrypted_terminal_id_here", "currency": "SGD", "notify_url": "https://api.aishowlab.com/payment/grabpay/notify", "redirect_url": "https://www.aishowlab.com/payment/success", "sandbox": false}',
1, 10, 'GrabPay是Grab公司推出的数字钱包服务，在东南亚地区广泛使用', 'system', 'system'),

-- 欧洲 - SEPA
('SEPA Direct Debit', 'sepa', '/images/payment/sepa.png',
'{"creditor_id": "encrypted_creditor_id_here", "creditor_name": "AIShowLab Europe", "creditor_address": "Amsterdam, Netherlands", "mandate_reference": "AISHOWLAB-SEPA", "currency": "EUR", "country_code": "NL", "notify_url": "https://api.aishowlab.com/payment/sepa/notify"}',
1, 11, 'SEPA直接借记是欧洲单一支付区域的银行转账支付方式', 'system', 'system'),

-- 印度 - UPI
('UPI', 'upi', '/images/payment/upi.png',
'{"merchant_id": "encrypted_merchant_id_here", "merchant_key": "encrypted_merchant_key_here", "salt": "encrypted_salt_here", "environment": "production", "success_url": "https://www.aishowlab.com/payment/success", "failure_url": "https://www.aishowlab.com/payment/fail", "cancel_url": "https://www.aishowlab.com/payment/cancel"}',
1, 12, 'UPI（统一支付接口）是印度国家支付公司开发的即时实时支付系统', 'system', 'system'),

-- 巴西 - PIX
('PIX', 'pix', '/images/payment/pix.png',
'{"client_id": "encrypted_client_id_here", "client_secret": "encrypted_client_secret_here", "certificate_path": "/certs/pix/certificate.p12", "certificate_password": "encrypted_password_here", "environment": "production", "webhook_url": "https://api.aishowlab.com/payment/pix/webhook"}',
1, 13, 'PIX是巴西央行推出的即时支付系统，支持24小时实时转账', 'system', 'system'),

-- 测试支付网关（用于开发测试）
('测试支付网关', 'test_gateway', '/images/payment/test.png',
'{"test_mode": true, "success_rate": 0.95, "delay_seconds": 2, "supported_currencies": ["CNY", "USD", "EUR"], "webhook_url": "https://api.aishowlab.com/payment/test/webhook"}',
2, 99, '仅用于开发测试的模拟支付网关，可以模拟各种支付场景', 'system', 'system'),

-- 已停用的支付方式示例
('旧版支付接口', 'legacy_pay', '/images/payment/legacy.png',
'{"deprecated": true, "migration_notice": "请迁移到新版支付接口"}',
2, 100, '已停用的旧版支付接口，仅保留用于历史订单查询', 'system', 'system');

-- ==================================================
-- 2. 支付网关与国家地区映射数据 (a_payment_gateway_country)
-- ==================================================

-- 微信支付 - 主要支持中国大陆、香港、澳门等地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'wechat_pay' AND c.iso2_code IN ('CN', 'HK', 'MO', 'TW', 'MY', 'SG', 'TH');

-- 支付宝 - 支持中国大陆、香港、澳门、台湾及部分海外地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'alipay' AND c.iso2_code IN ('CN', 'HK', 'MO', 'TW', 'US', 'CA', 'AU', 'NZ', 'GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'JP', 'KR', 'SG', 'MY', 'TH', 'PH', 'ID', 'VN');

-- 银联支付 - 主要支持中国大陆及银联卡可用地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'unionpay' AND c.iso2_code IN ('CN', 'HK', 'MO', 'TW', 'JP', 'KR', 'SG', 'MY', 'TH', 'US', 'CA', 'AU', 'GB', 'FR', 'DE', 'IT', 'ES');

-- PayPal - 全球支持（除部分受限国家）
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'paypal' AND c.iso2_code IN ('US', 'CA', 'MX', 'BR', 'AR', 'CL', 'CO', 'PE', 'GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'PL', 'CZ', 'HU', 'RO', 'BG', 'HR', 'SI', 'SK', 'LT', 'LV', 'EE', 'IE', 'PT', 'GR', 'CY', 'MT', 'LU', 'AU', 'NZ', 'JP', 'KR', 'SG', 'MY', 'TH', 'PH', 'ID', 'VN', 'IN', 'HK', 'TW', 'IL', 'TR', 'ZA', 'EG', 'MA', 'NG', 'KE', 'GH', 'UG', 'TZ', 'ZW', 'BW', 'MU', 'RU', 'UA', 'BY', 'KZ', 'UZ', 'GE', 'AM', 'AZ', 'MD', 'LI', 'MC', 'SM', 'VA', 'AD');

-- Stripe - 全球支持（覆盖面广）
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'stripe' AND c.iso2_code IN ('US', 'CA', 'MX', 'BR', 'AR', 'CL', 'CO', 'PE', 'UY', 'EC', 'BO', 'PY', 'VE', 'GY', 'SR', 'GF', 'GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'PL', 'CZ', 'HU', 'RO', 'BG', 'HR', 'SI', 'SK', 'LT', 'LV', 'EE', 'IE', 'PT', 'GR', 'CY', 'MT', 'LU', 'AU', 'NZ', 'JP', 'KR', 'SG', 'MY', 'TH', 'PH', 'ID', 'VN', 'IN', 'HK', 'TW', 'CN', 'MO', 'IL', 'TR', 'ZA', 'EG', 'MA', 'NG', 'KE', 'GH', 'UG', 'TZ', 'ZW', 'BW', 'MU', 'AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB', 'PS');

-- Apple Pay - 支持Apple设备的国家和地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'apple_pay' AND c.iso2_code IN ('US', 'CA', 'MX', 'BR', 'GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'PL', 'CZ', 'HU', 'IE', 'PT', 'AU', 'NZ', 'JP', 'KR', 'SG', 'MY', 'TH', 'HK', 'TW', 'CN', 'IN', 'IL', 'TR', 'ZA', 'AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB', 'EG', 'MA', 'RU', 'UA', 'BY', 'KZ', 'GE', 'AM', 'AZ', 'MD');

-- Google Pay - 支持Android设备的国家和地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'google_pay' AND c.iso2_code IN ('US', 'CA', 'MX', 'BR', 'AR', 'CL', 'CO', 'PE', 'GB', 'FR', 'DE', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'PL', 'CZ', 'HU', 'RO', 'BG', 'HR', 'SI', 'SK', 'LT', 'LV', 'EE', 'IE', 'PT', 'GR', 'CY', 'MT', 'LU', 'AU', 'NZ', 'JP', 'KR', 'SG', 'MY', 'TH', 'PH', 'ID', 'VN', 'IN', 'HK', 'TW', 'CN', 'MO', 'IL', 'TR', 'ZA', 'EG', 'MA', 'NG', 'KE', 'GH', 'UG', 'TZ', 'ZW', 'BW', 'MU', 'AE', 'SA', 'QA', 'KW', 'BH', 'OM', 'JO', 'LB', 'RU', 'UA', 'BY', 'KZ', 'UZ', 'GE', 'AM', 'AZ', 'MD');

-- LINE Pay - 主要支持日本、韩国、台湾、泰国等地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'line_pay' AND c.iso2_code IN ('JP', 'KR', 'TW', 'TH', 'ID');

-- KakaoPay - 主要支持韩国
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'kakao_pay' AND c.iso2_code IN ('KR');

-- GrabPay - 主要支持东南亚地区
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'grab_pay' AND c.iso2_code IN ('SG', 'MY', 'TH', 'PH', 'ID', 'VN', 'MM', 'KH');

-- SEPA - 支持欧洲单一支付区域国家
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'sepa' AND c.iso2_code IN ('AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE', 'IS', 'LI', 'NO', 'CH', 'MC', 'SM', 'VA', 'AD');

-- UPI - 主要支持印度
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'upi' AND c.iso2_code IN ('IN');

-- PIX - 主要支持巴西
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'pix' AND c.iso2_code IN ('BR');

-- 测试支付网关 - 支持所有国家（用于测试）
INSERT INTO a_payment_gateway_country (gateway_id, country_id, create_by, update_by)
SELECT pg.id, c.id, 'system', 'system'
FROM a_payment_gateway pg, a_base_country c
WHERE pg.code = 'test_gateway' AND c.is_enabled = true;

-- ==================================================
-- 数据插入完成
-- ==================================================
