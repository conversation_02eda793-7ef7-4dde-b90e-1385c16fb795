package ai.showlab.bff.entity.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 知识库文档上传参数
 * <p>
 * 用于上传文档到知识库。
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class KnowledgeDocUploadParam {

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    /**
     * 存储路径
     */
    @NotBlank(message = "存储路径不能为空")
    private String storagePath;
    
    /**
     * 文件类型 (字典: file_mime_type)
     */
    private Integer fileType;
}
