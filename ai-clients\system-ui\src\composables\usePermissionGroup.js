import { ref } from 'vue'
import { listBasePermissionGroup } from '@/api/bff/base/permission/group'

export const permissionGroupList = ref([])

function getPermissionGroupList() {
  const defaultParams = { pageNum: 1, pageSize: 1000 }
  return listBasePermissionGroup(defaultParams)
}

// Data will be fetched when the module is first imported.
getPermissionGroupList().then(response => {
  permissionGroupList.value = response.rows
})

export const groupIdToName = (groupId) => {
  const group = permissionGroupList.value.find(item => item.id === groupId)
  return group ? group.name : ''
} 